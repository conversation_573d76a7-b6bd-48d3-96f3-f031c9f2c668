package com.cscec3b.iti.projectmanagement;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.config.SpringUtils;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.model.req.FlowEventSubscribeReq;
import com.cscec3b.iti.model.req.ProjectFlowEventNoticeReq;
import com.cscec3b.iti.model.resp.open.ProjectAssociationStatusResp;
import com.cscec3b.iti.projectmanagement.constant.ApiConstants;
import com.cscec3b.iti.projectmanagement.projectspi.IProjectTransferApi;
import com.cscec3b.iti.projectmanagement.projectspi.dto.ProjectProgressDto;
import com.cscec3b.iti.projectmanagement.projectspi.dto.ProjectSpiDto;
import com.cscec3b.iti.projectmanagement.projectspi.dto.ProjectTransferData;
import com.cscec3b.iti.retry.annotations.PmReTry;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.ImmutableMap;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Properties;

import static com.cscec3b.iti.projectmanagement.projectspi.abstarct.CommonValidationService.genProjectAssociationUrl;
import static com.cscec3b.iti.projectmanagement.projectspi.abstarct.CommonValidationService.respValidation;

/**
 * 项目中心推送项目数据到财商
 *
 * <AUTHOR>
 * @date 2022/10/31 15:13
 **/
@Slf4j
public class ProjectTransferToFinance implements IProjectTransferApi {

    private static final Map<Integer, String> dataTypeMap = ImmutableMap.of(1, "project", 2, "specialproject");


    /**
     * 推送非重大项目到智慧工地
     *
     * @param project      项目信息
     * @param subscribeDto 订阅信息
     * @param noticeDto    事件信息
     * @param progressDto  进度信息
     * @return {@link Boolean}
     */
    @Override
    @PmReTry
    public Boolean transfer(ProjectSpiDto project, FlowEventSubscribeReq subscribeDto,
            ProjectFlowEventNoticeReq noticeDto, ProjectProgressDto progressDto) {
        try {
            ProjectTransferData projectData = new ProjectTransferData().setRequestId(noticeDto.getRequestId())
                    .setId(String.valueOf(project.getId()))
                    .setDataType(dataTypeMap.get(project.getSourceSystem()));
            final String financeUrl = subscribeDto.getPushUrl();
            log.info("transfer==>>financeUrl:{}, projectData:{}", financeUrl, projectData);
            RestTemplate pmRestTemplate =
                    Optional.ofNullable((RestTemplate) SpringUtils.getBean(ApiConstants.PM_REST_TEMPLATE)).orElse(SpringUtils.getBean(RestTemplate.class));
            progressDto.setApproveStatus(1).setToFinanceTime(Instant.now().toEpochMilli());
            ResponseEntity<String> responseEntity = pmRestTemplate.postForEntity(financeUrl, projectData, String.class);
            log.info("transfer==>>responseEntity:{}", responseEntity);
            if (HttpStatus.OK.value() == responseEntity.getStatusCodeValue()) {
                JsonNode respBody = JsonUtils.parseStringToJson(responseEntity.getBody());
                if (ApiConstants.ZERO == respBody.get(ApiConstants.ERR_CODE).asInt()) {
                    return Boolean.TRUE;
                }
                String errorMsg = String.format("业务处理异常: errorCode: %s, errorMsg: %s",
                        respBody.get(ApiConstants.ERR_CODE).asText(), respBody.get(ApiConstants.ERR_MSG).asText());
                // 有重试，需要取第一次的异常信息
                String financeRemarks = StringUtils.isNotBlank(progressDto.getFinanceRemarks()) ?
                        progressDto.getFinanceRemarks() : errorMsg;
                progressDto.setFinanceRemarks(financeRemarks);
                throw new FrameworkException(-1, errorMsg);
            }
            String errorMsg = String.format("Http调用响应异常: %d", responseEntity.getStatusCodeValue());
            // 有重试，需要取第一次的异常信息
            String financeRemarks = StringUtils.isNotBlank(progressDto.getFinanceRemarks()) ?
                    progressDto.getFinanceRemarks() : errorMsg;
            progressDto.setFinanceRemarks(financeRemarks);
            throw new FrameworkException(-1, errorMsg);
        } catch (Exception e) {
            log.error("transfer==>>Exception:", e);
            String errorMsg = String.format("API请求异常: %s", e.getMessage());
            // 有重试，需要取第一次的异常信息
            String financeRemarks = StringUtils.isNotBlank(progressDto.getFinanceRemarks()) ?
                    progressDto.getFinanceRemarks() : errorMsg;
            progressDto.setFinanceRemarks(financeRemarks);
            throw new FrameworkException(-1, errorMsg);
        }
    }

    @Override
    public ResponseEntity<String> transfer(ProjectTransferData projectData) {
        try {
            Properties props = new Properties();
            props.load(new ClassPathResource(ApiConstants.API_YML).getInputStream());
            String financeUrl = props.getProperty(ApiConstants.FINANCE_URL);
            log.info("transfer==>>financeUrl:{}, projectData:{}", financeUrl, projectData);
            RestTemplate pmRestTemplate = (RestTemplate) SpringUtils.getBean(ApiConstants.PM_REST_TEMPLATE);
            ResponseEntity<String> responseEntity = pmRestTemplate.postForEntity(financeUrl, projectData, String.class);
            log.info("transfer==>>responseEntity:{}", responseEntity);
            if (HttpStatus.OK.value() == responseEntity.getStatusCodeValue()) {
                JsonNode respBody = JsonUtils.parseStringToJson(responseEntity.getBody());
                if (ApiConstants.ZERO == respBody.get(ApiConstants.ERR_CODE).asInt()) {
                    return responseEntity;
                }
                String errorMsg = String.format("业务处理异常: errorCode: %s, errorMsg: %s",
                        respBody.get(ApiConstants.ERR_CODE).asText(), respBody.get(ApiConstants.ERR_MSG).asText());
                throw new FrameworkException(-1, errorMsg);
            }
            String errorMsg = String.format("Http调用响应异常: %d", responseEntity.getStatusCodeValue());
            throw new FrameworkException(-1, errorMsg);
        } catch (Exception e) {
            log.error("transfer==>>Exception:", e);
            String errorMsg = String.format("API请求异常: %s", e.getMessage());
            throw new FrameworkException(-1, errorMsg);
        }
    }

    @Override
    public List<ProjectAssociationStatusResp> getProjectAssociationStatus(String cpmProjectKey,
        FlowEventSubscribeReq subscribeDto) {
        // 此url包含了"/cpm/project/flow/event/notice"
        final String pushUrl = subscribeDto.getPushUrl();
        final String newUrl = genProjectAssociationUrl(pushUrl, "/cpm/project/association/status");
        subscribeDto.setPushUrl(newUrl);
        RestTemplate pmRestTemplate =
            Optional.ofNullable((RestTemplate)SpringUtils.getBean(ApiConstants.PM_REST_TEMPLATE))
                .orElse(SpringUtils.getBean(RestTemplate.class));
        MultiValueMap<String, String> params = new LinkedMultiValueMap<String, String>();
        params.add("cpmProjectKey", cpmProjectKey);
        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(params);
        try {
            final ResponseEntity<GenericityResponse<List<ProjectAssociationStatusResp>>> responseEntity =
                pmRestTemplate.exchange(newUrl, HttpMethod.POST, httpEntity,
                    new ParameterizedTypeReference<GenericityResponse<List<ProjectAssociationStatusResp>>>() {});
            return respValidation(responseEntity);
        } catch (Exception e) {
            log.error("请求远程api异常：{}-{}", this.getClass().getName(), e.getMessage());
            throw new FrameworkException(-1, "请求远程api异常：" + e.getMessage());
        }

    }
}
