package com.cscec3b.iti.projectmanagement;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.pinming.openapi.client.invoke.EncryptUtils;
import cn.pinming.openapi.client.invoke.FormData;
import com.cscec3b.iti.common.web.config.SpringUtils;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.model.req.FlowEventSubscribeReq;
import com.cscec3b.iti.model.req.ProjectFlowEventNoticeReq;
import com.cscec3b.iti.projectmanagement.constant.ApiConstants;
import com.cscec3b.iti.projectmanagement.projectspi.IProjectTransferApi;
import com.cscec3b.iti.projectmanagement.projectspi.dto.ProjectProgressDto;
import com.cscec3b.iti.projectmanagement.projectspi.dto.ProjectSpiDto;
import com.google.common.collect.ImmutableMap;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.*;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.io.Serializable;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.time.Instant;
import java.util.Date;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 智慧工地推送服务
 *
 * <AUTHOR>
 * @date 2023/11/11
 */
@Slf4j
public class SmartSiteTransferService implements IProjectTransferApi {

    public static final String MARKETING_SEGMENT = "marketing_segment";
    public static final String SPECIAL_PROJECT = "special_project";
    public static final String CPM_SEGMENT = "cpm_segment";

    public static final String SMART_SITE_PROJECT = "smart_site_project";

    /**
     * 项目id
     */
    public static final String PROJECT_ID = "projectId";

    /**
     * 项目标识
     */
    public static final String CPM_PROJECT_KEY = "cpmProjectKey";

    /**
     * eventId
     */
    public static final String EVENT_ID = "eventId";

    /**
     * eventCode
     */
    private static final String EVENT_CODE = "eventCode";

    /**
     * 推送URI
     */
    public static final String PUSH_URI = "/projectApproval/syncProjectFromOut";

    /**
     * 拉取URI
     */
    public static final String PULL_URI = "/projectApproval/getProjectInfoById";
    public static final String PUSH = "PUSH";
    public static final String PULL = "PULL";


    /**
     * 推送非重大项目到智慧工地
     *
     * @param project      项目信息
     * @param subscribeDto 订阅信息
     * @param noticeDto    事件信息
     * @param progressDto  进度信息
     * @return {@link Boolean}
     */
    @Override
    public Boolean transfer(ProjectSpiDto project, FlowEventSubscribeReq subscribeDto,
                            ProjectFlowEventNoticeReq noticeDto, ProjectProgressDto progressDto) {

        // 事件类型 用于区分是去通知工地项目中心的事件，还是接收工地的事件去拉取工地数据
        String requestType = PUSH;
        log.info("项目【{}-{}】推送智慧工地", project.getId(), project.getCpmProjectName());
        // 区别 立项 事件，还是 更新 事件, 市场营销,和 特殊立项 事件为立项事件，项目中心为更新事件
        String flowNodeCode = noticeDto.getFlowNodeCode();
        final String flowDataTypeCode = noticeDto.getFlowDataTypeCode();
        ImmutableMap<String, ? extends Serializable> paramMap = null;
        if (flowDataTypeCode.equals("create")) {
            // 将 noticeDto 转换为 Map<String, Serializable>
            Map<String, Serializable> serializableMap = BeanUtil.beanToMap(noticeDto).entrySet()
                    .stream()
                    .collect(Collectors.toMap(
                            Map.Entry::getKey,
                            e -> (Serializable) e.getValue()
                    ));
            paramMap = ImmutableMap.copyOf(serializableMap);
//            noticeDto.setEventCode("A100001").setEventId("100001");
        } else if (flowDataTypeCode.equals("update")) {
            noticeDto.setEventCode("UD10001").setEventId("100002");
            paramMap = ImmutableMap.of(PROJECT_ID, project.getId(),
                    EVENT_ID, noticeDto.getEventId(), EVENT_CODE, noticeDto.getEventCode(), CPM_PROJECT_KEY, project.getCpmProjectKey());
        } else {
            // 接收到工地通知，需要到工地拉取信息
            // 由调用方法自行生成 eventCode, eventId
            requestType = PULL;
        }
        // if (flowNodeCode.equals(MARKETING_SEGMENT) || flowNodeCode.equals(SPECIAL_PROJECT)
        //         || flowNodeCode.equals(SMART_SITE_PROJECT)) {
        //     noticeDto.setEventCode("A100001").setEventId("100001");
        // } else if (flowNodeCode.equals(CPM_SEGMENT)) {
        //     noticeDto.setEventCode("UD10001").setEventId("100002");
        // } else {
        //     // 接收到工地通知，需要到工地拉取信息
        //     // 由调用方法自行生成 eventCode, eventId
        //     requestType = PULL;
        // }
        // 组装请求参数
//        ImmutableMap<String, ? extends Serializable> paramMap = ImmutableMap.of(PROJECT_ID, project.getId(),
//                EVENT_ID, noticeDto.getEventId(), EVENT_CODE, noticeDto.getEventCode(), CPM_PROJECT_KEY, project.getCpmProjectKey());
        Map<String, Object> stringObjectMap = encryptData(subscribeDto, paramMap, requestType);
        HttpEntity<MultiValueMap<String, Object>> httpEntity = genHttpentity(stringObjectMap);
        final String pushUrl = subscribeDto.getPushUrl();
        // 设置工地立项时间 及 立项状态为立项中 1
        progressDto.setSmartQueryTime(Instant.now().toEpochMilli()).setSmartApproveStatus(1);
        ResponseEntity<RespEntity> responseEntity = null;
        try {
            RestTemplate pmRestTemplate =
                    Optional.ofNullable((RestTemplate) SpringUtils.getBean(ApiConstants.PM_REST_TEMPLATE)).orElse(SpringUtils.getBean(RestTemplate.class));
            responseEntity = pmRestTemplate.postForEntity(pushUrl, httpEntity, RespEntity.class);
        } catch (RestClientException e) {
            String errorMsg = String.format("API请求异常: %s", e.getMessage());
            // 有重试，需要取第一次的异常信息
            String smartRemarks = StringUtils.isNotBlank(progressDto.getSmartRemarks())
                    ? progressDto.getSmartRemarks() : errorMsg;
            progressDto.setSmartRemarks(smartRemarks);
            throw new FrameworkException(-1, errorMsg);
        }
        return checkResp(progressDto, responseEntity);

    }

    /**
     * 检查响应值
     *
     * @param progressDto    项目进度信息
     * @param responseEntity 响应值
     * @return boolean
     */
    private static boolean checkResp(ProjectProgressDto progressDto, ResponseEntity<RespEntity> responseEntity) {
        log.info("eventPush==>>responseEntity:{}", responseEntity);
        final RespEntity resultEntity = responseEntity.getBody();
        if (HttpStatus.OK.value() != responseEntity.getStatusCodeValue()) {
            String errorMsg = String.format("Http调用响应异常: %d", responseEntity.getStatusCodeValue());
            // 有重试，需要取第一次的异常信息
            String smartRemarks = StringUtils.isNotBlank(progressDto.getSmartRemarks())
                    ? progressDto.getSmartRemarks() : errorMsg;
            progressDto.setSmartRemarks(smartRemarks);
            throw new FrameworkException(responseEntity.getStatusCodeValue(), errorMsg);
        }
        if (ObjectUtils.isNotEmpty(resultEntity)) {
            if (!resultEntity.isSuccess()) {
                final String errorMsg = String.format("业务处理异常: errorCode: %s, errorMsg: %s",
                        resultEntity.getErrorCode(),
                        resultEntity.getErrorMsg());
                // 有重试，需要取第一次的异常信息
                String smartRemarks = StringUtils.isNotBlank(progressDto.getSmartRemarks())
                        ? progressDto.getSmartRemarks() : errorMsg;
                progressDto.setSmartRemarks(smartRemarks);
                throw new FrameworkException(-1, errorMsg);
            }
            return true;
        }
        String errorMsg = String.format("API请求异常: %s", responseEntity.getStatusCodeValue());
        // 有重试，需要取第一次的异常信息
        String smartRemarks = StringUtils.isNotBlank(progressDto.getSmartRemarks())
                ? progressDto.getSmartRemarks() : errorMsg;
        progressDto.setSmartRemarks(smartRemarks);
        throw new FrameworkException(responseEntity.getStatusCodeValue(), errorMsg);
    }

    private static Map<String, Object> encryptData(FlowEventSubscribeReq subscribeDto, Object paramMap, String requestType) {
        log.info("encryptData==>>FlowEventSubscribeReq:{}, paramMap:{}", subscribeDto, paramMap);
        //1.组装业务数据对象
        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(paramMap);
        //3.字符串编码
        String encodeStr = encode(jsonStr);
        //4.aes加密
        String encryptStr = EncryptUtils.aesEncrypt(subscribeDto.getAppSecret(), encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        String extension = subscribeDto.getExtension();
        JSONObject jsonObject = JSONUtil.parseObj(extension);
        if (requestType.equals(PUSH)) {
            data.setItype((String) jsonObject.get("push_uri"));
        } else {
            data.setItype((String) jsonObject.get("pull_uri"));
        }
        data.setTime(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
        data.setData(encryptStr);
        data.setApiGroup((String) jsonObject.get("group"));
        //加密类型：1-AES; 2-BASE64
        data.setEtype(1);
        // 开发者类型： 1 企业开发者;2 平台开发者，默认为空代表企业开发者
        data.setDtype(2);
        data.setDkey(subscribeDto.getAppKey());
        //6.按字段顺序加密
        String originalString = data.getItype() + data.getTime() + data.getData() + subscribeDto.getAppSecret();
        String sign = EncryptUtils.md5Encrypt(originalString);
        data.setSign(sign);
        //7.对象转map
        return BeanUtil.beanToMap(data);
    }

    private static String encode(String s) {
        try {
            if (s == null) {
                return null;
            }
            return URLEncoder.encode(s, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new FrameworkException(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
        }
    }

    /**
     * 增加headers
     *
     * @param encryptData 加密后的数据
     * @return {@link HttpEntity}<{@link MultiValueMap}<{@link String}, {@link Object}>>
     */
    public static HttpEntity<MultiValueMap<String, Object>> genHttpentity(Map<String, Object> encryptData) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set("Accept", "application/json");
        final LinkedMultiValueMap<String, Object> multiValueMap = new LinkedMultiValueMap<>();
        encryptData.forEach(multiValueMap::add);
        return new HttpEntity<>(multiValueMap, headers);
    }

    /**
     * {
     * "success": false,
     * "errorCode": "404",
     * "errorMsg": "Not Found"
     * }
     *
     * <AUTHOR>
     * @date 2023/12/19
     */
    @Getter
    @Setter
    static class RespEntity implements Serializable {

        /**
         * 响应标识
         */
        private boolean success;

        /**
         * 错误码
         */
        private String errorCode;

        /**
         * 错误消息（工地系统）
         */
        private String errorMsg;

        /**
         * 错误消息 （工地网关）
         */
        private String error;

        public String getErrorMsg() {
            return StringUtils.isNotBlank(error) ? error + errorMsg : errorMsg;
        }
    }

}
