package com.cscec3b.iti.projectmanagement;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.web.config.SpringUtils;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.model.req.FlowEventSubscribeReq;
import com.cscec3b.iti.model.req.ProjectFlowEventNoticeReq;
import com.cscec3b.iti.model.resp.open.ProjectAssociationStatusResp;
import com.cscec3b.iti.projectmanagement.constant.ApiConstants;
import com.cscec3b.iti.projectmanagement.projectspi.IProjectTransferApi;
import com.cscec3b.iti.projectmanagement.projectspi.dto.ProjectProgressDto;
import com.cscec3b.iti.projectmanagement.projectspi.dto.ProjectSpiDto;
import com.cscec3b.iti.retry.annotations.PmReTry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.time.Instant;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.cscec3b.iti.projectmanagement.projectspi.abstarct.CommonValidationService.genHttpHeaders;
import static com.cscec3b.iti.projectmanagement.projectspi.abstarct.CommonValidationService.genProjectAssociationUrl;
import static com.cscec3b.iti.projectmanagement.projectspi.abstarct.CommonValidationService.respValidation;
import static com.cscec3b.iti.projectmanagement.projectspi.abstarct.CommonValidationService.responseValidation;

/**
 * 通用Spi实现- 对接SDK
 * 商务SPI实现
 *
 * <AUTHOR>
 * @date 2023/09/16 09:56
 **/
@Slf4j
public class CpmCommonTransferService implements IProjectTransferApi {

	/**
	 * 签名常量
	 */
	private static final String NEW_SIGN = "x-sign";
	/**
	 * 签名学常量
	 */
	private static final String TIMESTAMP = "x-timestamp";


	/**
	 * 推送非重大项目到智慧工地
	 *
	 * @param spiDto       项目信息
	 * @param subscribeDto 订阅信息
	 * @param noticeDto    事件信息
	 * @param progressDto  进度信息
	 * @return {@link Boolean}
	 */
	@PmReTry
	@Override
	public Boolean transfer(ProjectSpiDto spiDto, FlowEventSubscribeReq subscribeDto,
                            ProjectFlowEventNoticeReq noticeDto, ProjectProgressDto progressDto) {
		// 由于sdk 不支持新添加的 项目挂接事件， 所以此处进行转换，如何是项目挂接立项事件，则转换成标准立项事件推送
		if (Objects.equals(noticeDto.getFlowNodeCode(), "hook_project_approval")) {
			noticeDto.setFlowNodeCode("finance_smart_site_approval");
		}
		final String pushUrl = subscribeDto.getPushUrl();
		RestTemplate pmRestTemplate =
		Optional.ofNullable((RestTemplate) SpringUtils.getBean(ApiConstants.PM_REST_TEMPLATE)).orElse(SpringUtils.getBean(RestTemplate.class));
		// 旧的签名方式生成
		final HttpHeaders httpHeaders = genHttpHeaders(subscribeDto);
		// 新的签名方式生成
		genNewHttpHeaders(subscribeDto, httpHeaders);
		HttpEntity<ProjectFlowEventNoticeReq> httpEntity = new HttpEntity<>(noticeDto, httpHeaders);
		try {
			final ResponseEntity<String> responseEntity = pmRestTemplate.exchange(pushUrl,
					HttpMethod.POST, httpEntity, String.class);
			responseValidation(responseEntity);
		} catch (Exception e) {
			log.error("请求远程api异常：{}-{}", this.getClass().getName(), e.getMessage());
			throw new FrameworkException(-1, "请求远程api异常：" + e.getMessage());
		}
		return true;
	}

	/**
	 * 新的签名生成
	 *
	 * @param subscribeDto 业务参数
	 * @param httpHeaders  header
	 * <AUTHOR>
	 * @date 2023/10/19
	 */
	private void genNewHttpHeaders(FlowEventSubscribeReq subscribeDto, HttpHeaders httpHeaders) {
		final String currentTime = String.valueOf(Instant.now().toEpochMilli());
		final String digestedHex =
				SecureUtil.hmacSha256(subscribeDto.getAppSecret() + currentTime).digestHex(subscribeDto.getAppKey());
		httpHeaders.add(NEW_SIGN, digestedHex);
		httpHeaders.add(TIMESTAMP, currentTime);
		// 透传扩展字段
		final String extension = subscribeDto.getExtension();
		if (JSONUtil.isJson(extension)) {
			final JSONObject jsonObject = JSONUtil.parseObj(extension);
			jsonObject.forEach((key, value) -> httpHeaders.add(key, value.toString()));
		}
	}

    @Override
    public List<ProjectAssociationStatusResp> getProjectAssociationStatus(String cpmProjectKey,
        FlowEventSubscribeReq subscribeDto) {
        // 此url包含了"/cpm/project/flow/event/notice"
        final String pushUrl = subscribeDto.getPushUrl();
        final String newUrl = genProjectAssociationUrl(pushUrl, "/cpm/project/association/status");
        subscribeDto.setPushUrl(newUrl);
        RestTemplate pmRestTemplate =
            Optional.ofNullable((RestTemplate)SpringUtils.getBean(ApiConstants.PM_REST_TEMPLATE))
                .orElse(SpringUtils.getBean(RestTemplate.class));
        // 旧的签名方式生成
        final HttpHeaders httpHeaders = genHttpHeaders(subscribeDto);
        // 新的签名方式生成
        genNewHttpHeaders(subscribeDto, httpHeaders);

        MultiValueMap<String, String> params = new LinkedMultiValueMap<String, String>();
        params.add("cpmProjectKey", cpmProjectKey);
        HttpEntity<MultiValueMap<String, String>> httpEntity = new HttpEntity<>(params, httpHeaders);
        try {
            final ResponseEntity<GenericityResponse<List<ProjectAssociationStatusResp>>> responseEntity =
                pmRestTemplate.exchange(newUrl, HttpMethod.POST, httpEntity,
                    new ParameterizedTypeReference<GenericityResponse<List<ProjectAssociationStatusResp>>>() {});
            return respValidation(responseEntity);
        } catch (Exception e) {
            log.error("请求远程api异常：{}-{}", this.getClass().getName(), e.getMessage());
            throw new FrameworkException(-1, "请求远程api异常：" + e.getMessage());
        }
    }
}
