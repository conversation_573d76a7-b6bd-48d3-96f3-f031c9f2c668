#stages:
#  - scanner
#
#example-sonar:
#  stage: scanner
#  tags:
#    - gitlab-runner
#  script:
#    - mvn  compile  sonar:sonar
#      -Dsonar.host.url=http://************:35865
#      -Dsonar.gitlab.commit_sha=$CI_COMMIT_SHA
#      -Dsonar.gitlab.ref_name=$CI_COMMIT_REF_NAME
#      -Dsonar.gitlab.project_id=$CI_PROJECT_ID
#      -Dsonar.issuesReport.html.enable=true
#      -Dsonar.login=****************************************
#      -Dsonar.branch.name=$CI_COMMIT_REF_NAME
#      -Dsonar.projectName=$CI_PROJECT_NAME
#      -Dsonar.projectKey=$CI_PROJECT_NAME
#      -Dsonar.qualitygate.wait=true
###  only:
###    - merge_requests
#
###add somme comments


# 20230817 sonar服务切换
stages:
  - scanner

sonarqube:
  stage: scanner
  tags:
    - sonarqube
  script:
    - PROJECT_NAMESPACE=`echo $CI_PROJECT_NAMESPACE |sed s@/@-@g`
    - mvn clean compile
    - sonar-scanner
      -Dsonar.host.url=https://sonarqube.cscec3b-iti.com
      -Dsonar.login=squ_a810659bccbc508a341cc270611473b7fc67aead
      -Dsonar.branch.name=$CI_COMMIT_REF_NAME
      -Dsonar.projectName=$CI_PROJECT_PATH
      -Dsonar.projectKey=${PROJECT_NAMESPACE}-${CI_PROJECT_NAME}
      -Dsonar.qualitygate.wait=true
      -Dsonar.sources=.
      -Dsonar.java.binaries=./*/target
      -Dsonar.java.jdkHome=/usr/local/jdk1.8.0_371
      -Dsonar.java.libraries=/home/<USER>/.m2/repository/org/projectlombok/lombok/*/*.jar
  only:
    - dev
