## 项目中心SDK

### 概述
![项目生命周期及内置事件](.\流转及内置节点.png)

* 项目中心在项目流转过过程中按不同的业务板块节点内置了前置及后置事件，
同时按项目数据类型划分了项目立项及项目更新两种数据类型；
* 项目立项事件会记录项目所有信息
* 项目更新事件会记录更新点当前板块的信息，注意： **前置更新事件无法记录当前板块的信息**

### SDK提供能力
- 提供默认鉴权功能，~~兼容神禹网关~~(1.0.3版本保留兼容，后续版本移除)；
    > sdk借鉴了神禹网关的鉴权功能(1.0.0版)，可以直接接入商务系统的神禹网关

- ~~提供自定义鉴权功能~~
  > ~~对于未接入神禹网关的业务系统，SDK内置的鉴权功能也可以脱离神禹网关使用，
  同时项目中心也提供了SPI扩展包，SDK中也开放了自定义鉴权接口，可供业务系统自行实现~~
- 提供幂等性校验接口
    > 提供了幂等校验接口(默认实现为空)
- 开放业务实现接口，供业务系统自行实现
    > 业务系统须自行实现 

![sdk对接方案](.\sdk对接方案.png)
> 接入方只需要实现自己的业务逻辑即可

### 使用方法

#### 1. 引入依赖
```xml
  <dependency>
      <groupId>com.cscec3b.iti.projectmanagement</groupId>
      <artifactId>projectmanagement-sdk</artifactId>
  <version>1.1.4.release</version>
  </dependency>
```
#### 2. 加入包扫描
> 扫描范围为： com.cscec3b.iti.cpm 或 com.cscec3b.iti

> 以下两种方式均设置包扫描范围(选择其中一种设置)，需要注意@ComponentScan 优先级大于 @SpringBootApplication(
> scanBasePackages)
```java
@SpringBootApplication(scanBasePackages = {"业务系统的包路径","com.cscec3b.iti.cpm", })
```
```java
import org.springframework.context.annotation.ComponentScan;

@ComponentScan(basePackages = {"业务系统的包路径","com.cscec3b.iti.cpm"})
```

#### 3. 放行sdk api

> sdk api路径为：/cpm/project/flow/event/notice

因sdk无法获取业务系统的token等鉴权信息，无法适配业务系统的前置鉴权逻辑，所以需要业务系统放行sdk api,由sdk内部进行鉴权；

放行逻辑：ExcludePath.matchUri(String requestUri) 方法会检验当前请求路径是会否为sdk api路径，返回true则需要放行鉴权(
跳过token校验)等

示例：

```java
public class LoginInterceptor extends HandlerInterceptorAdapter {

  @Override
  public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {

    response.setCharacterEncoding(CharsetUtil.UTF_8);
    String requestMethod = request.getMethod();
    String requestUri = request.getRequestURI();
    log.debug("enter the token interceptor : {} {}", requestMethod, requestUri);

    // 加入下列语句即可放行sdk api
    // 跳过sdk接口鉴权
    if (ExcludePath.matchUri(url)) {
      return true;
    }

    // todo 业务系统自定义鉴权逻辑
    // 认证(校验Token)
    // 缓存用户信息
    // 鉴权


    log.debug("no permission, return 403");
    response.setStatus(HttpStatus.FORBIDDEN.value());
    return false;
  }

}

```

```java
  // 匹配sdk api路径
  if(ExcludePath.matchUri(url)){
      return true;
  }

```
> 如项目中未使用 HandlerInterceptor 需要自行处理放行逻辑，api路径为：/cpm/project/flow/event/notice,建议编码为 "/**/cpm/project/flow/event/notice"

#### 4. 业务实现



**业务系统需要添加sdk对应API路径(/cpm/project/flow/event/notice)白名单**
- 添加配置信息

 ```yml
cpm:
  push-auth:
    app-key: 在业务系统中鉴权的appkey (业务系统提供给项目中心(同时在项目中心注册页面填写))
    secret-key: 在业务系统中鉴权的secretKey (业务系统提供给项目中心(同时在项目中心注册页面填写))
  call-back:
    cpm-host: 项目中心回调地址： 
              开发：https://openplatformdev.cscec3b-iti.com                 测试：https://openplatformtest.cscec3b-iti.com 
              预发：https://openplatformpre.cscec3b-iti.com                 生产：https://openplatform.cscec3b-iti.com
    cpm-auth-app-key: 项目中心鉴权appkey(项目中心提供给业务系统)
    cpm-auth-app-secret: 项目中心鉴权appsecret(项目中心提供给业务系统)
# 示例：
cpm:
  push-auth:
    app-key: 2C1608B6D44348E79BF9ADD3A25D2112
    secret-key: 1FEB26BB83DA4E5889DA9A995CAF97EA
  call-back:
    cpm-host: https://openplatformtest.cscec3b-iti.com
    #      开发：https://openplatformdev.cscec3b-iti.com                 测试：https://openplatformtest.cscec3b-iti.com
    #      预发：https://openplatformpre.cscec3b-iti.com                 生产：https://openplatform.cscec3b-iti.com
    cpm-auth-app-key: AF697786A0E24532BB8C0A4CC6A95F06
    cpm-auth-app-secret: C753C68221E64A648ADE05975A62580C

 ```
1. 事件通知，项目信息状态同步：
- 创建自定义实现类继承 AbstractProjectArchiveServiceImpl  并注入spring
- 重写apply方法，在apply方法中进行业务逻辑处理（注意事务处理）

```java

@Slf4j
@Service
@Transcational(rollbackFor = Exception.class)
public class CusCpmService extends AbstractProjectArchiveServiceImpl {


  protected CusCpmService(CpmCallBackProperties callBackProperties, CpmPushProperties pushProperties,
          RestTemplate cpmRestTemplate) {
    super(callBackProperties, pushProperties, cpmRestTemplate);
  }
  //
  // /**
  //  * 跳过鉴权
  //  */
  // @Override
  // public boolean enableApiAuth() {
  //     return true;
  // }
    
    /**
     * 业务系统自行实现逻辑
     */
    
  @Override
  public void apply(FlowNodeDataTypeEnum flowNodeDataTypeEnum, FlowNodeEnum flowNodeEnum,
                    ProjectArchiveResp projectArchiveResp) throws FrameworkException {
    log.info("------------------------------------------------------------------------------");
    log.info("接收到项目中心的消息推送:{} -{}", flowNodeEnum.getName(), flowNodeDataTypeEnum.getName());
    log.info("接收到的项目信息：{}", JSONUtil.toJsonStr(projectArchiveResp));
    log.info("------------------------------------------------------------------------------");
    // todo  业务逻辑处理
  }
}
```
> 如果callBackProperties, callBackProperties 提示 “无法自动装配。找不到 'CpmCallBackProperties' 类型的 Bean。” 请检查包扫描配置是否正确


~~//检查提示RestTemplate 无法注入，请注入RestTemplate的Bean  
  @Bean
  public RestTemplate restTemplate() {
      return new RestTepmlate();
}~~

如需要跳过sdk鉴权，可以实现类进行关闭，关闭后，SDK就不会处理鉴权，需要业务系统自行处理，**不建议直接关闭鉴权**。
```java
     @Override
    public boolean enableApiAuth() {
        return false;
    }
```
2. openApi 
- 在业务实现类中 注入 ICpmApiService
```
@Resource 或 @AutoWired
private ICpmApiService cpmApiService 

```
- 在需求的地方直接使用cpmApiService. 调用对应方法
  



#### 5. 验证

以上操作完成后，启动项目， 本地请求 http://localhost + 端口 + context-path + /cpm/project/flow/event/notice, 提示
api认证失败，则接入正常
![api认证失败](.\post_api.png)

#### 5.字段说明
* ProjectArchiveResp
  <br>[数据模型](.\projectArchiveResp.md)
```java
{
		"businessSegment": {
		"projectStatusBiz": "string"
		},
		"cpmProjectKey": "string",
		"cpmSegment": {
		"cpmProjectMark": "string",
		"standardProjectCode": "string",
		"yunshuExecuteTreeId": "string",
		"yunshuExecuteUnit": "string",
		"yunshuExecuteUnitId": "string"
		},
		"financeSegment": {
		"projectFinanceAbbreviation": "string",
		"projectFinanceCode": "string",
		"projectFinanceName": "string",
		"projectStatusFin": "string"
		},
		"marketingSegment": {
		"businessType": "string",
		"contractMode": "string",
		"countryProjectType": "string",
		"financialBusinessSegment": "string",
		"marketProjectType": "string",
		"projectAddress": "string",
		"projectName": "string",
		"projectType": "string",
		"region": "string",
		"standardType": "string"
		},
		"projectId": 0,
		"smartSiteSegment": {
		"contractEndDate": 0,
		"contractStartDate": 0,
		"isEcologySensitive": true,
		"isEdgeSmall": true,
		"lat": "string",
		"lng": "string",
		"projectA8no": "string",
		"projectScale": "string",
		"projectStatusEng": "string",
		"qualityTask": "string",
		"realEnterTime": 0,
		"realOpenTrafficTime": 0,
		"realWorkBeginTime": 0,
		"recordDate": 0,
		"securityTask": "string",
		"smartContractModel": "string",
		"smartProjectAddress": "string",
		"workEndTime": 0,
		"yunshuOrgId": "string",
		"yunshuParentOrgId": "string",
		"yunshuParentOrgName": "string",
		"yunshuParentTreeId": "string"
		},
		"supplySegment": {}
		}
```


* FlowNodeDataTypeEnum
  * FlowNodeDataTypeEnum.UPDATE 项目更新
  > 项目更新，收到更新事件时，返回projectId, cpmProjectKey, 及业务系统在项目中心配置的板块信息，
  > 如：工程信息更新，返回信息中就只有 smartSiteSegment 下有值，其他板块信息为null
  * FlowNodeDataTypeEnum.CREATE 项目立项
  > 项目立项，返回projectId,cpmProjectKey,以及所有板块信息(可能为空)

### 更新日志
#### 1.1.2.RELEASE
1. openApi: 增加云筑网项目关系映射信息, cpmApiService.getMappingInfo()
2. 注入”cpmRestTemplate"，并添加日志,记录

#### 1.1.3.RELEASE

1. WebConfig类重命名为cpmWebConfig, 避免和业务系统WebConfig冲突
2. 添加IProjectArchiveService的默认实现类DefaultProjectArchiveCallbackImpl,在只接入OpenApi时，无需再实现IProjectArchiveCallback;

#### 1.1.4.RELEASE

1. 回调项目中心方法增加异常处理逻辑
2. 事件通知接口直接将返回值写入response中(某些系统会全局返回体及异常处理逻辑，会导致返回体格式发生偏差，导致服务端无法判断推送成功与否)

#### 1.1.5.RELEASE
1. 重命名相关类名,添加 cpm前缀，减少重名机率

#### 1.1.6.RELEASE

1. projectArchiveResp 类中基本档案增加项目立项文件信息(类型，编码，id)，财商业务板块及编码
2. 市场板块增加客户相关方信息，增加项目关联文件列表、业务板块信息
3. 抽取projectArchiveResp基础字段为父类统一管理
4. projectmanagement-model 版本升级为1.0.1.release

1.1.7.RELEASE

1.工地板块增加项目立时间

#### 1.1.8.RELEASE

1. 新增:工程项目立项事件; 工程/施工项目绑定 、工程/施工解绑、工程/施工换绑事件;
2. projectBaseResp中增加 mainStandardProject(是否主施工)、engineeringProjectId(工程项目id)、engineeringKey(工程项目标识)
   、engineeringName(工程项目名称)、engineeringCode(工程项目编码)字段,同一施工项目,同一时间只能绑定一个工程项目;
3. projectArchiveResp中增加ProjectBindingSegment(工程/施工项目绑定信息)字段;
4. 施工项目立项事件保持现在逻辑不变(工程与财商全部立项完成);
5. 工程项目立项事件逻辑为:

> 1. 集团系统下发工程与施工编码的映射关系; 财商系统会回调施工项目与施工编码的关系;
>2. 集团下发映射关系时,首先对数据进行清洗暂存,然后判断是否是主施工项目(第一次下发施工编码)
    a. 如果是,则直接触发施工项目立项事件和工程项目立项事件;
    b. 如果不是,则通过施工编码查找施工项目;
    ⅰ. 如果存在施工项目,则触发工程/施工绑定 更新事件;
    ⅱ. 如果不存在,则结束当前请求,等待施工项目立项事件
>3. 财商系统回调时,先触发标准立项事件;然后通过施工编码查询与工程项目的绑定关系;
    a. 如果存在绑定关系,则触发工程/施工绑定 更新事件
    b. 如果不存在绑定关系,则结束,并等待集团映射关系下发





