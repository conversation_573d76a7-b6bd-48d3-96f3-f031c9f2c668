package com.cscec3b.iti.cpm.service.impl;

import com.cscec3b.iti.cpm.api.GenericityResponse;
import com.cscec3b.iti.cpm.config.CpmCallBackProperties;
import com.cscec3b.iti.cpm.service.ICpmApiService;
import com.cscec3b.iti.model.req.OpenProjectSyncReq;
import com.cscec3b.iti.model.req.ProjectOpenReq;
import com.cscec3b.iti.model.req.open.ProjectYzwMappingReq;
import com.cscec3b.iti.model.resp.ProjectOpenResp;
import com.cscec3b.iti.model.resp.ShenYuResponse;
import com.cscec3b.iti.model.resp.open.ProjectOpenMappingResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;

@Slf4j
@Service
public class CpmApiServiceImpl implements ICpmApiService {

    /**
     * 回调配置
     */
    private final CpmCallBackProperties callBackProperties;

    /**
     * restTemplate
     */
    private final RestTemplate resttemplate;

    public CpmApiServiceImpl(CpmCallBackProperties callBackProperties,
            @Qualifier("cpmRestTemplate") RestTemplate resttemplate) {
        this.callBackProperties = callBackProperties;
        this.resttemplate = resttemplate;
    }

    /**
     * 业务系统向项目中心同步项目信息
     *
     * @param syncReq {@link OpenProjectSyncReq}
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    @Override
    public Boolean syncProjectInfo(OpenProjectSyncReq syncReq) {
        final String path = "/cpm/api/external/open/project/sync";
        final ResponseEntity<ShenYuResponse<Boolean>> shenYuResponseResponseEntity = callBackToCpm(syncReq,
                callBackProperties, path, resttemplate);
        return checkResponse(shenYuResponseResponseEntity);
    }


    /**
     * 项目对外信息获取API
     *
     * @param projectOpenReq {@link ProjectOpenReq}
     * @return {@link GenericityResponse}<{@link List }<{@link ProjectOpenResp }>>
     */
    @Override
    public List<ProjectOpenResp> openProjectInfo(ProjectOpenReq projectOpenReq) {
        final String path = "/cpm/api/external/open/project";
        final ParameterizedTypeReference<ShenYuResponse<List<ProjectOpenResp>>> typeReference =
                new ParameterizedTypeReference<ShenYuResponse<List<ProjectOpenResp>>>() {
                };
        final ResponseEntity<ShenYuResponse<List<ProjectOpenResp>>> shenYuResponseResponseEntity =
                callBackToCpm(projectOpenReq, callBackProperties, path, resttemplate, typeReference);
        return checkResponse(shenYuResponseResponseEntity);
    }

    @Override
    public List<ProjectOpenMappingResp> getMappingInfo(List<ProjectYzwMappingReq> mappingReqs) {
        final String path = "/cpm/api/external/open/yzw-mapping";
        final ParameterizedTypeReference<ShenYuResponse<List<ProjectOpenMappingResp>>> typeReference =
                new ParameterizedTypeReference<ShenYuResponse<List<ProjectOpenMappingResp>>>() {
                };
        final ResponseEntity<ShenYuResponse<List<ProjectOpenMappingResp>>> shenYuResponseResponseEntity =
                callBackToCpm(mappingReqs, callBackProperties, path, resttemplate, typeReference);
        return checkResponse(shenYuResponseResponseEntity);
    }
}
