package com.cscec3b.iti.retry.model.entity;

import java.time.Instant;

/**
 * API异常回调
 */
public class ApiRetryCall {
    /**
     * 主键id
     */
    private String id;

    /**
     * 类名或接口名称
     */
    private String className;

    /**
     * 方法名
     */
    private String methodName;

    /**
     * 方法参数类型
     */
    private String paramType;

    /**
     * 方法参数值
     */
    private String param;

    /**
     * api返回结果
     */
    private String result;

    /**
     * 请求结果 1:成功; 2:失败
     */
    private Integer success = 0;

    /**
     * 定时任务重试结果
     */
    private String tryResult;

    /**
     * 是否需要人工干预 0:否; 1:是;
     */
    private Integer humanIntervention;

    /**
     * 创建时间
     */
    private Long createAt = Instant.now().toEpochMilli();

    /**
     * 更新时间
     */
    private Long updateAt = Instant.now().toEpochMilli();

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getMethodName() {
        return methodName;
    }

    public void setMethodName(String methodName) {
        this.methodName = methodName;
    }

    public String getParamType() {
        return paramType;
    }

    public void setParamType(String paramType) {
        this.paramType = paramType;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Integer getSuccess() {
        return success;
    }

    public void setSuccess(Integer success) {
        this.success = success;
    }

    public String getTryResult() {
        return tryResult;
    }

    public void setTryResult(String tryResult) {
        this.tryResult = tryResult;
    }

    public Integer getHumanIntervention() {
        return humanIntervention;
    }

    public void setHumanIntervention(Integer humanIntervention) {
        this.humanIntervention = humanIntervention;
    }

    public Long getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Long createAt) {
        this.createAt = createAt;
    }

    public Long getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Long updateAt) {
        this.updateAt = updateAt;
    }
}