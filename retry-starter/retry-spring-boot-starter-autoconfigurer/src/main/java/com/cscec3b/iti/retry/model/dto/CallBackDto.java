package com.cscec3b.iti.retry.model.dto;

import java.io.Serializable;

/**
 * callback dto
 *
 * <AUTHOR>
 * @date 2022/11/01 16:03
 **/
public class CallBackDto implements Serializable {

    /**
     * 请求api地址
     */
    private String url;

    /**
     * 表示类名或接口名
     */
    private String className;

    /**
     * 表示方法名
     */
    private String methodName;
    /**
     * 表示方法参数类型
     */
    private Class[] paramTypes;
    /**
     * 表示方法参数值
     */
    private String[] params;

    /**
     * 请求结果
     */
    private String result;

    public String getUrl() {
        return url;
    }
    
    /**
     * 设置url
     *
     * @param url url
     * @return CallBackDto
     */
    public CallBackDto setUrl(String url) {
        this.url = url;
        return this;
    }

    public String getClassName() {
        return className;
    }
    
    /**
     * 设置类名
     * @param className  className
     * @return CallBackDto
     */
    public CallBackDto setClassName(String className) {
        this.className = className;
        return this;
    }

    public String getMethodName() {
        return methodName;
    }
    
    /**
     * 设置方法名
     * @param methodName 方法名
     * @return CallBackDto
     */
    public CallBackDto setMethodName(String methodName) {
        this.methodName = methodName;
        return this;
    }

    public Class[] getParamTypes() {
        return paramTypes;
    }
    
    /**
     *  设置设置方法参数类型类型
     * @param paramTypes 参数类型
     * @return CallBackDto
     */
    public CallBackDto setParamTypes(Class[] paramTypes) {
        this.paramTypes = paramTypes;
        return this;
    }

    public String[] getParams() {
        return params;
    }
    
    /**
     * 设置方法参数
     * @param params 参数
     * @return callBackDto
     */
    public CallBackDto setParams(String[] params) {
        this.params = params;
        return this;
    }

    public String getResult() {
        return result;
    }
    
    /**
     * 设置返回结果
     * @param result 返回结果
     * @return callBackDto
     */
    public CallBackDto setResult(String result) {
        this.result = result;
        return this;
    }
}
