package com.cscec3b.iti.model.resp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description MarketingSegment
 * @date 2023/09/16 11:33
 */

@Data
@ApiModel(value = "MarketingSegment", description = "项目信息分发-市场营销数据部分")
public class MarketingSegment implements Serializable {

    /**
     * 市场营销工程名称
     */
    @ApiModelProperty(value = "市场营销工程名称")
    private String projectName;

    /**
     * 市场营销承包模式
     */
    @ApiModelProperty(value = "市场营销承包模式")
    private String contractMode;

    /**
     * 市场营销承包模式
     */
    @ApiModelProperty(value = "市场营销承包模式Code")
    private String contractModeCode;

    /**
     * 工程类型（国家标准）
     */
    @ApiModelProperty(value = "工程类型（国家标准）")
    private String countryProjectType;

    /**
     * 工程类型（国家标准）
     */
    @ApiModelProperty(value = "工程类型Code（国家标准）")
    private String countryProjectTypeCode;

    /**
     * 工程类型（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）")
    private String marketProjectType;

    /**
     * 工程类型（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型Code（总公司市场口径）")
    private String marketProjectTypeCode;

    /**
     * 工程类型（总公司综合口径）
     */
    @ApiModelProperty(value = "工程类型（总公司综合口径）")
    private String projectType;

    /**
     * 工程类型（总公司综合口径）
     */
    @ApiModelProperty(value = "工程类型Code（总公司综合口径）")
    private String projectTypeCode;

    /**
     * 行政区域（地理位置）
     */
    @ApiModelProperty(value = "行政区域（地理位置）")
    private String region;

    /**
     * 项目地址
     */
    @ApiModelProperty(value = "项目地址")
    private String projectAddress;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessType;
    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型")
    private String businessTypeCode;

    /**
     * 财商业务板块
     */
    @ApiModelProperty(value = "财商业务板块")
    private String financialBusinessSegment;

    /**
     * 财商业务板块codePath
     */
    @ApiModelProperty(value = "财商业务板块codePath")
    private String businessSegmentCodePath;

    /**
     * 局标准分类
     */
    @ApiModelProperty(value = "局标准分类")
    private String standardType;

    /**
     * 局标准分类
     */
    @ApiModelProperty(value = "局标准分类")
    private String standardTypeCodePath;

    /**
     * 合同开工日期
     */
    @ApiModelProperty(value = "合同开工日期")
    private Long workerBeginTime;

    /**
     * 合同竣工日期
     */
    @ApiModelProperty(value = "合同竣工日期")
    private Long workerEndTime;


    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理")
    private String projectManager;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式", position = 72)
    private String payTypeNew;

    /**
     * 实际签约日期
     */
    @ApiModelProperty(value = "实际签约日期", position = 48)
    private Long actualSignedTime;

    /**
     * 预计实际竣工日期
     */
    @ApiModelProperty(value = "预计实际竣工日期", position = 43)
    private Long predictWorkEndTime;

    /**
     * 总工期
     */
    @ApiModelProperty(value = "总工期", position = 41)
    private Integer countDays;

    /**
     * 预付款比例
     */
    @ApiModelProperty(value = "预付款比例")
    private String advancesRate;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", position = 44)
    private String customerName;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户Id", position = 44)
    private String customerId;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 客户母公司id
     */
    @ApiModelProperty(value = "客户母公司id")
    private String superiorCompanyId;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String businessLicenseCode;

    /**
     * 客户母公司
     */
    @ApiModelProperty(value = "客户母公司", position = 45)
    private String superiorCompanyName;

    /**
     * 客户企业性质
     */
    @ApiModelProperty(value = "客户企业性质", position = 46)
    private String enterpriseType;

    /**
     * 客户企业性质
     */
    @ApiModelProperty(value = "客户企业性质Code", position = 46)
    private String enterpriseTypeCode;

    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别", position = 38)
    private String customerLevel;

    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别code", position = 38)
    private String customerLevelCode;

    /**
     * 实际中标日期
     */
    @ApiModelProperty(value = "实际中标日期", position = 47)
    private Long successfulTime;

    /**
     * 签约主体
     */
    @ApiModelProperty(value = "签约主体", position = 49)
    private String signedSubjectValue;
    /**
     * 签约主体Code
     */
    @ApiModelProperty(value = "签约主体", position = 49)
    private String signedSubjectCode;

    /**
     * 实施单位
     */
    @ApiModelProperty(value = "实施单位", position = 50)
    private String doUnit;

    /**
     * 实施单位Code
     */
    @ApiModelProperty(value = "实施单位Code", position = 50)
    private String doUnitCode;

    /**
     * 合同总金额（元）
     */
    @ApiModelProperty(value = "合同总金额")
    private BigDecimal contractAmount;

    /**
     * 自行施工不含税金额
     */
    @ApiModelProperty(value = "自行施工不含税金额")
    private BigDecimal midAmountSelf;

    /**
     * 项目合同/文件列表
     */
    @ApiModelProperty(value = "项目合同/文件列表")
    private List<ContractFileRelationResp> contractFileList;

}
