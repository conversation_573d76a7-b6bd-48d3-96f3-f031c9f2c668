package com.cscec3b.iti.model.resp.open;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel(value = "云筑网映射关系返回参数")
public class ProjectOpenMappingResp implements Serializable {

    /**
     * 项目标识
     */
    @ApiModelProperty(value = "项目标识")
    private String cpmProjectKey;

    /**
     * 项目中心项目id
     */
    @ApiModelProperty(value = "项目中心项目id")
    private Long projectId;

    /**
     * 云枢组织id
     */
    @ApiModelProperty(value = "云枢组织id")
    private String yunshuOrgId;

    /**
     * 智慧工地项目id
     */
    @ApiModelProperty(value = "智慧工地项目id")
    private Long smartSiteProjectId;

    /**
     * 云筑网劳务id
     */
    @ApiModelProperty(value = "云筑网劳务id", notes = "对应工地='service_system_id' 字段")
    private String yzwServiceId;

    /**
     * 云筑网集采id
     */
    @ApiModelProperty(value = "云筑网集采id")
    private String yzwProjectId;

    /**
     * 云筑网智联id
     */
    @ApiModelProperty(value = "云筑网智联id", notes = "对应工地-'deo_project_id' 字段")
    private String yzwVideoId;

    /**
     * 智慧工地品茗项目id
     */
    @ApiModelProperty(value = "智慧工地品茗项目id", notes = "对应工地-'pinming_project_id' 字段")
    private Long smartSitePinmingId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String cpmProjectName;
}
