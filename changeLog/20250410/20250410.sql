alter table engineering_project
    add column execute_unit_id varchar(64) default null comment '执行单位id',
    add column project_dept_id varchar(64) default null comment '项目部id',
    add column is_history      tinyint(2)  default 0 comment '是否历史工程(0:非历史工程,1:历史工程)',
    add column contact_user    varchar(16) default null comment '对接人',
    add column contact_user_id         varchar(16) default null comment '对接用户id',
    add column mapping_execute_unit_id varchar(64) default null comment '关联执行单位id';

-- 更新执行单位id
update engineering_project ep
    inner join project p on ep.init_project_id = p.id
set ep.mapping_execute_unit_id = p.yunshu_execute_unit_id;

update engineering_project
set is_history = 1;