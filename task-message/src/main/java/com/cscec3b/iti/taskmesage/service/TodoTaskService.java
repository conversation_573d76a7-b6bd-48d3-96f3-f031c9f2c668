package com.cscec3b.iti.taskmesage.service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.quartz.JobKey;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.config.SpringUtils;
import com.cscec3b.iti.taskmesage.dto.TaskAndMsgDto;
import com.cscec3b.iti.taskmesage.dto.TimeInterval;
import com.cscec3b.iti.taskmesage.mapper.TodoTaskMapper;
import com.cscec3b.iti.taskmesage.model.TodoTask;
import com.cscec3b.iti.taskmesage.utils.DateUtils;
import com.cscec3b.iti.taskmesage.utils.ReminderTimeCalculator;
import com.g3.msg.vo.NormalTodoVO;
import com.google.common.collect.Sets;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class TodoTaskService extends ServiceImpl<TodoTaskMapper, TodoTask> {


    private final QuartzService quartzService;

    /**
     * 初始化待办任务及催办任务
     *
     * @param todoVOList
     * @param dto DTO
     */
    public void initTaskAndJob(List<NormalTodoVO> todoVOList, TaskAndMsgDto dto) {
        log.info("init task and job: {}", JsonUtils.toJsonStr(dto));
        LocalDateTime startTime = Objects.nonNull(dto.getStartTime()) ? dto.getStartTime() : LocalDateTime.now();
        List<TodoTask> todoTasks = new ArrayList<>();
        final String retryTimeCycle = dto.getRetryTimeCycle();
        final TimeInterval timeInterval = Optional.ofNullable(retryTimeCycle).filter(StringUtils::isNotBlank)
            .map(el -> new ReminderTimeCalculator().getTimeInterval(el, startTime)).orElse(new TimeInterval());
        log.info("timeInterval info:{}", JsonUtils.toJsonStr(timeInterval));
        final LocalDateTime nextRemindTime = Optional.of(timeInterval).map(TimeInterval::getNextTime)
            .map(time -> LocalDateTime.ofInstant(time, ZoneId.systemDefault())).orElse(null);
        todoVOList.forEach(vo -> {
            TodoTask todoTask = new TodoTask(vo.getTaskCode(), vo.getBillId(), vo.getBillType(), vo.getBillTypeName(), vo.getStartUserName(),
                vo.getStartUser(), vo.getTitle(), vo.getState(), vo.getBpmInstanceId(), startTime,
                vo.getExpectedDeadline(), vo.getAppLink(), vo.getWebLink(), vo.getProcessUserId(), dto.getConfigCode(),
                dto.getPayload(), timeInterval.getRepeatCount(), retryTimeCycle);
            todoTask.setCreateAt(Instant.now().toEpochMilli());
            todoTask.setUpdateAt(Instant.now().toEpochMilli());
            todoTask.setBusinessKey(dto.getBusinessKey());
            if (Objects.nonNull(nextRemindTime)) {
                todoTask.setNextRemindTime(nextRemindTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            }
            todoTasks.add(todoTask);
            if (StringUtils.isNotBlank(retryTimeCycle)) {
                quartzService.addRemindJob(timeInterval, vo.getTaskCode(), vo.getBpmInstanceId(), vo.getBillId(),
                    vo.getBillType(), vo.getExpectedDeadline(), retryTimeCycle);
            }
        });
        this.saveBatch(todoTasks);
    }


    /**
     * 完成任务
     *
     * @param tasks
     */
    public void doneTask(List<TodoTask> tasks) {
        tasks.forEach(task -> {
            final JobKey jobKey = new JobKey(task.getTaskCode(), task.getBpmInstanceId());
            quartzService.cancelRemindJob(jobKey);
        });
        this.updateBatchById(tasks);
    }

    // @Lock
    @Transactional(rollbackFor = Exception.class)
    public void remindJob(String taskCode) {
        final TodoTask task = getOne(Wrappers.<TodoTask>lambdaQuery().eq(TodoTask::getTaskCode, taskCode));

        // 待办中
        if (Objects.nonNull(task) && task.getState() == 0
            && (task.getCurrentCount() < task.getRepeatCount() || task.getRepeatCount() == -1)) {

            // 增加当前执行次数
            task.setCurrentCount(task.getCurrentCount() + 1);
            LocalDateTime nextReminderTime = ReminderTimeCalculator
                .calculateNextReminderTime(task.getRemindExpression(), LocalDateTime.now().plusMinutes(1));
            // todo 判断是否在非工作日及非非工作时间
            nextReminderTime = DateUtils.adjustToNextWorkingTime(nextReminderTime);
            task.setNextRemindTime(nextReminderTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());
            updateById(task);

            // 触发催办消息
            TaskAndMessageService taskAndMessageService = SpringUtils.getBean(TaskAndMessageService.class);
            if (Objects.isNull(taskAndMessageService)) {
                log.error("TaskAndMessageService is null");
                return;
            }
            try {
                final TaskAndMsgDto taskAndMsgDto = new TaskAndMsgDto(task.getWebLink(), task.getAppLink(),
                    task.getConfigCode(), task.getPayload(), Sets.newHashSet(task.getTargetUser()));
                taskAndMessageService.sendNoticeMsg(taskAndMsgDto);
            } catch (Exception e) {
                // todo 异常重试
                throw new RuntimeException(e);
            }
            // 如果
        }
        log.info("remind job");
    }

}
