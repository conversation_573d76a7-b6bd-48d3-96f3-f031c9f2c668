package com.cscec3b.iti.taskmesage.service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.scheduling.quartz.SchedulerFactoryBean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.cscec3b.iti.taskmesage.dto.TimeInterval;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class QuartzService {

    private final SchedulerFactoryBean schedulerFactory;

    public QuartzService(SchedulerFactoryBean schedulerFactory) {
        this.schedulerFactory = schedulerFactory;
    }

    public void addRemindJob(TimeInterval timeInterval, String taskCode, String bpmInstanceId, String billId,
        String billType, LocalDateTime expectedDeadline, String expression) {
        log.info("add remind job:timeInterval={}, taskCode={}, bpmInstanceId={}, billId={}, billType={}, expectedDeadline={}, expression={}",
                timeInterval, taskCode, bpmInstanceId, billId, billType, expectedDeadline, expression);
        try {
            // 任务待办是一条任务一个用户一条记录 ，对应应催办也是如此
            final JobKey jobKey = new JobKey(taskCode, bpmInstanceId);
            // 检查任务调度器
            final Scheduler scheduler = checkSchedulerAndJob(jobKey);
            final JobDetail jobDetail = JobBuilder.newJob(RemindJobBean.class).withIdentity(jobKey).storeDurably()
                .usingJobData("taskCode", taskCode).build();
            Date startTimeAsDate = Date.from(timeInterval.getStartTime());
            Date nextTimeAsDate = Date.from(timeInterval.getNextTime());
            Date endTimeAsDate = Objects.nonNull(expectedDeadline)
                ? Date.from(expectedDeadline.atZone(ZoneId.systemDefault()).toInstant()) : null;
            final TriggerKey triggerKey = new TriggerKey(billId + "-" + taskCode, billType);
            final TriggerBuilder<Trigger> triggerBuilder =
                TriggerBuilder.newTrigger().withIdentity(triggerKey).startAt(nextTimeAsDate).endAt(endTimeAsDate);
            final String cycleType = timeInterval.getCycleType();
            switch (cycleType) {
                case "time":
                    triggerBuilder.startAt(startTimeAsDate);
                    break;
                case "cron":
                    triggerBuilder.withSchedule(CronScheduleBuilder.cronSchedule(expression));
                    break;
                case "cycle":
                    triggerBuilder.withSchedule(SimpleScheduleBuilder.simpleSchedule()
                        .withIntervalInMilliseconds(timeInterval.getDuration().toMillis())
                        .withRepeatCount(timeInterval.getRepeatCount()));
                    break;
            }
            final Trigger trigger = triggerBuilder.build();

            log.info("scheduleJob:jobDetail={}, trigger={}", JSONUtil.toJsonStr(jobDetail), JSONUtil.toJsonStr(trigger));
            scheduler.scheduleJob(jobDetail, trigger);
            log.info("Job {} successfully scheduled with trigger {}", jobKey, trigger.getKey());
        } catch (SchedulerException e) {
            log.error("add remind job error", e);
        }
    }

    /**
     * 检查 Scheduler 和 Job
     *
     * @param jobKey Job 键
     * @return {@link Scheduler }
     * @throws SchedulerException 调度程序异常
     */
    private Scheduler checkSchedulerAndJob(JobKey jobKey) throws SchedulerException {
        // Ensure scheduler is started
        Scheduler scheduler = schedulerFactory.getScheduler();
        if (!scheduler.isStarted()) {
            scheduler.start();
        }
        // Check if job already exists before scheduling
        if (scheduler.checkExists(jobKey)) {
            log.warn("Job {} already exists, replacing.", jobKey);
            scheduler.deleteJob(jobKey);
        }
        return scheduler;
    }

    /**
     * 重试作业计划
     */
    private void retryJobScheduling(TimeInterval timeInterval, String taskCode, String bpmInstanceId, String billId,
        String billType, LocalDateTime expectedDeadline, String expression) {
        // Implement retry logic with exponential backoff
        int maxRetries = 3;
        for (int attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                Thread.sleep(attempt * 1000); // Exponential backoff
                this.addRemindJob(timeInterval, taskCode, bpmInstanceId, billId, billType, expectedDeadline,
                    expression);
            } catch (Exception e) {
                log.error("Retry {} failed", attempt);
            }
        }
    }

    public void cancelRemindJob(JobKey jobKey) {
        try {
            log.info("delete remind job:jobKey={}", JSONUtil.toJsonStr(jobKey));
            // final JobDetail jobDetail = schedulerFactory.getScheduler().getJobDetail(jobKey);
            // schedulerFactory.getScheduler().deleteJob(jobKey);
            final Scheduler scheduler = schedulerFactory.getScheduler();
            if (scheduler.checkExists(jobKey)) {
                final List<? extends Trigger> triggersOfJob = scheduler.getTriggersOfJob(jobKey);
                final List<TriggerKey> triggerKeys =
                    triggersOfJob.stream().map(Trigger::getKey).collect(Collectors.toList());
                scheduler.unscheduleJobs(triggerKeys);
                scheduler.deleteJob(jobKey);
            }
            log.info("delete remind job success:jobKey={}", JSONUtil.toJsonStr(jobKey));
        } catch (SchedulerException e) {
            log.error("delete remind job error", e);
        }
    }

    public Set<JobKey> getAllJobKeys() {
        try {
            GroupMatcher<JobKey> matcher = GroupMatcher.anyJobGroup();
            Set<JobKey> jobKeys = schedulerFactory.getScheduler().getJobKeys(matcher);
            log.info("getAllJobKeys:{}", JSONUtil.toJsonStr(jobKeys));
            return jobKeys;
        } catch (SchedulerException e) {
            log.error("getAllJobKeys error", e);
            return Collections.emptySet();
        }
    }

    public void addTaskPushJobBean(TimeInterval timeInterval, String taskCode, String pushType, String billType, String expression) {
        log.info("add TaskPushJob:timeInterval={}, taskCode={}, pushType={},  billType={}, expression={}",
                timeInterval, taskCode, pushType, billType, expression);
        try {
            // 任务待办是一条任务一个用户一条记录 ，对应应催办也是如此
            final JobKey jobKey = new JobKey(taskCode, pushType);
            // 检查任务调度器
            final Scheduler scheduler = checkSchedulerAndJob(jobKey);
            final JobDetail jobDetail = JobBuilder.newJob(TaskPushJobBean.class).withIdentity(jobKey).storeDurably()
                    .usingJobData("taskCode", taskCode).build();
            Date startTimeAsDate = Date.from(timeInterval.getStartTime());
            Date nextTimeAsDate = Date.from(timeInterval.getNextTime());
            final TriggerKey triggerKey = new TriggerKey(pushType + "-" + taskCode, billType);
            final TriggerBuilder<Trigger> triggerBuilder =
                    TriggerBuilder.newTrigger().withIdentity(triggerKey).startAt(nextTimeAsDate).endAt(null);
            final String cycleType = timeInterval.getCycleType();
            switch (cycleType) {
                case "time":
                    triggerBuilder.startAt(startTimeAsDate);
                    break;
                case "cron":
                    triggerBuilder.withSchedule(CronScheduleBuilder.cronSchedule(expression));
                    break;
                case "cycle":
                    triggerBuilder.withSchedule(SimpleScheduleBuilder.simpleSchedule()
                            .withIntervalInMilliseconds(timeInterval.getDuration().toMillis())
                            .withRepeatCount(timeInterval.getRepeatCount()));
                    break;
            }
            final Trigger trigger = triggerBuilder.build();

            log.info("scheduleJob:TaskPushJobDetail={}, trigger={}", JSONUtil.toJsonStr(jobDetail), JSONUtil.toJsonStr(trigger));
            scheduler.scheduleJob(jobDetail, trigger);
            log.info("TaskPushJob {} successfully scheduled with trigger {}", jobKey, trigger.getKey());
        } catch (SchedulerException e) {
            log.error("add TaskPushJob error", e);
        }
    }
}
