DROP TABLE IF EXISTS QRTZ_FIRED_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_PAUSED_TRIGGER_GRPS;
DROP TABLE IF EXISTS QRTZ_SCHEDULER_STATE;
DROP TABLE IF EXISTS QRTZ_LOCKS;
DROP TABLE IF EXISTS QRTZ_SIMPLE_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_SIMPROP_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_CRON_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_BLOB_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_TRIGGERS;
DROP TABLE IF EXISTS QRTZ_JOB_DETAILS;
DROP TABLE IF EXISTS QRTZ_CALENDARS;


CREATE TABLE QRTZ_JOB_DETAILS
(
    SCHED_NAME        VARCHAR(120) NOT NULL,
    JOB_NAME          VARCHAR(200) NOT NULL,
    JOB_GROUP         VARCHAR(200) NOT NULL,
    DESCRIPTION       VARCHAR(250) NULL,
    JOB_CLASS_NAME    VARCHAR(250) NOT NULL,
    IS_DURABLE        VARCHAR(1)   NOT NULL,
    IS_NONCONCURRENT  VARCHAR(1)   NOT NULL,
    IS_UPDATE_DATA    VARCHAR(1)   NOT NULL,
    REQUESTS_RECOVERY VARCHAR(1)   NOT NULL,
    JOB_DATA          BLOB         NULL,
    PRIMARY KEY (SCHED_NAME, JOB_NAME, JOB_GROUP)
);

CREATE TABLE QRTZ_TRIGGERS
(
    SCHED_NAME     VARCHAR(120) NOT NULL,
    TRIGGER_NAME   VARCHAR(200) NOT NULL,
    TRIGGER_GROUP  VARCHAR(200) NOT NULL,
    JOB_NAME       VARCHAR(200) NOT NULL,
    JOB_GROUP      VARCHAR(200) NOT NULL,
    DESCRIPTION    VARCHAR(250) NULL,
    NEXT_FIRE_TIME BIGINT(13)   NULL,
    PREV_FIRE_TIME BIGINT(13)   NULL,
    PRIORITY       INTEGER      NULL,
    TRIGGER_STATE  VARCHAR(16)  NOT NULL,
    TRIGGER_TYPE   VARCHAR(8)   NOT NULL,
    START_TIME     BIGINT(13)   NOT NULL,
    END_TIME       BIGINT(13)   NULL,
    CALENDAR_NAME  VARCHAR(200) NULL,
    MISFIRE_INSTR  SMALLINT(2)  NULL,
    JOB_DATA       BLOB         NULL,
    PRIMARY KEY (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP),
    FOREIGN KEY (SCHED_NAME, JOB_NAME, JOB_GROUP)
        REFERENCES QRTZ_JOB_DETAILS (SCHED_NAME, JOB_NAME, JOB_GROUP)
);

CREATE TABLE QRTZ_SIMPLE_TRIGGERS
(
    SCHED_NAME      VARCHAR(120) NOT NULL,
    TRIGGER_NAME    VARCHAR(200) NOT NULL,
    TRIGGER_GROUP   VARCHAR(200) NOT NULL,
    REPEAT_COUNT    BIGINT(7)    NOT NULL,
    REPEAT_INTERVAL BIGINT(12)   NOT NULL,
    TIMES_TRIGGERED BIGINT(10)   NOT NULL,
    PRIMARY KEY (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP),
    FOREIGN KEY (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP)
        REFERENCES QRTZ_TRIGGERS (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP)
);

CREATE TABLE QRTZ_CRON_TRIGGERS
(
    SCHED_NAME      VARCHAR(120) NOT NULL,
    TRIGGER_NAME    VARCHAR(200) NOT NULL,
    TRIGGER_GROUP   VARCHAR(200) NOT NULL,
    CRON_EXPRESSION VARCHAR(200) NOT NULL,
    TIME_ZONE_ID    VARCHAR(80),
    PRIMARY KEY (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP),
    FOREIGN KEY (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP)
        REFERENCES QRTZ_TRIGGERS (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP)
);

CREATE TABLE QRTZ_SIMPROP_TRIGGERS
(
    SCHED_NAME    VARCHAR(120)   NOT NULL,
    TRIGGER_NAME  VARCHAR(200)   NOT NULL,
    TRIGGER_GROUP VARCHAR(200)   NOT NULL,
    STR_PROP_1    VARCHAR(512)   NULL,
    STR_PROP_2    VARCHAR(512)   NULL,
    STR_PROP_3    VARCHAR(512)   NULL,
    INT_PROP_1    INT            NULL,
    INT_PROP_2    INT            NULL,
    LONG_PROP_1   BIGINT         NULL,
    LONG_PROP_2   BIGINT         NULL,
    DEC_PROP_1    NUMERIC(13, 4) NULL,
    DEC_PROP_2    NUMERIC(13, 4) NULL,
    BOOL_PROP_1   VARCHAR(1)     NULL,
    BOOL_PROP_2   VARCHAR(1)     NULL,
    PRIMARY KEY (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP),
    FOREIGN KEY (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP)
        REFERENCES QRTZ_TRIGGERS (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP)
);

CREATE TABLE QRTZ_BLOB_TRIGGERS
(
    SCHED_NAME    VARCHAR(120) NOT NULL,
    TRIGGER_NAME  VARCHAR(200) NOT NULL,
    TRIGGER_GROUP VARCHAR(200) NOT NULL,
    BLOB_DATA     BLOB         NULL,
    PRIMARY KEY (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP),
    FOREIGN KEY (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP)
        REFERENCES QRTZ_TRIGGERS (SCHED_NAME, TRIGGER_NAME, TRIGGER_GROUP)
);

CREATE TABLE QRTZ_CALENDARS
(
    SCHED_NAME    VARCHAR(120) NOT NULL,
    CALENDAR_NAME VARCHAR(200) NOT NULL,
    CALENDAR      BLOB         NOT NULL,
    PRIMARY KEY (SCHED_NAME, CALENDAR_NAME)
);

CREATE TABLE QRTZ_PAUSED_TRIGGER_GRPS
(
    SCHED_NAME    VARCHAR(120) NOT NULL,
    TRIGGER_GROUP VARCHAR(200) NOT NULL,
    PRIMARY KEY (SCHED_NAME, TRIGGER_GROUP)
);

CREATE TABLE QRTZ_FIRED_TRIGGERS
(
    SCHED_NAME        VARCHAR(120) NOT NULL,
    ENTRY_ID          VARCHAR(95)  NOT NULL,
    TRIGGER_NAME      VARCHAR(200) NOT NULL,
    TRIGGER_GROUP     VARCHAR(200) NOT NULL,
    INSTANCE_NAME     VARCHAR(200) NOT NULL,
    FIRED_TIME        BIGINT(13)   NOT NULL,
    SCHED_TIME        BIGINT(13)   NOT NULL,
    PRIORITY          INTEGER      NOT NULL,
    STATE             VARCHAR(16)  NOT NULL,
    JOB_NAME          VARCHAR(200) NULL,
    JOB_GROUP         VARCHAR(200) NULL,
    IS_NONCONCURRENT  VARCHAR(1)   NULL,
    REQUESTS_RECOVERY VARCHAR(1)   NULL,
    PRIMARY KEY (SCHED_NAME, ENTRY_ID)
);

CREATE TABLE QRTZ_SCHEDULER_STATE
(
    SCHED_NAME        VARCHAR(120) NOT NULL,
    INSTANCE_NAME     VARCHAR(200) NOT NULL,
    LAST_CHECKIN_TIME BIGINT(13)   NOT NULL,
    CHECKIN_INTERVAL  BIGINT(13)   NOT NULL,
    PRIMARY KEY (SCHED_NAME, INSTANCE_NAME)
);

CREATE TABLE QRTZ_LOCKS
(
    SCHED_NAME VARCHAR(120) NOT NULL,
    LOCK_NAME  VARCHAR(40)  NOT NULL,
    PRIMARY KEY (SCHED_NAME, LOCK_NAME)
);


commit;

-- project_management.u_todo_task definition

CREATE TABLE `u_todo_task`
(
    `id`                bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `task_code`         varchar(32)         NOT NULL COMMENT '任务编号',
    `bill_id`           varchar(64)         NOT NULL COMMENT '单据id',
    `bill_type`         varchar(64)                  DEFAULT NULL COMMENT '单据类型',
    `bill_type_name` varchar(128) NOT NULL COMMENT '单据名称',
    `start_user_name`   varchar(64)         NOT NULL COMMENT '发送人名称',
    `start_user_id`     varchar(64)         NOT NULL COMMENT '发起人id',
    `title`             varchar(256)        NOT NULL COMMENT '任务标题',
    `state`             tinyint(3) unsigned NOT NULL COMMENT '待办状态：0:表示待办，1:表示已办',
    `bpm_instance_id`   varchar(64)                  DEFAULT NULL COMMENT '聚合任务id(在我的发起中合并多个任务为1条)',
    `start_time`        datetime            NOT NULL COMMENT '待办开始时间',
    `expected_deadline` datetime                     DEFAULT NULL COMMENT '期望任务截止时间',
    `app_link`          varchar(1024)                DEFAULT NULL COMMENT 'app link',
    `web_link`          varchar(1024)       NOT NULL COMMENT 'web link',
    `target_user`       varchar(64)         NOT NULL COMMENT '任务与消息目标用户 id',
    `config_code`       varchar(64)                  DEFAULT NULL COMMENT 'uc平台消息配置id',
    `payload`           varchar(512)                 DEFAULT NULL COMMENT '消息体',
    `repeat_count`      int(11)             NOT NULL DEFAULT '0' COMMENT '催办次数',
    `current_count`     int(11)                      DEFAULT '0' COMMENT '当前已催办次数',
    `remind_expression` varchar(128)                 DEFAULT NULL COMMENT '催办时间表达式：1:cron 表达式；2:R3/PT1H/2024-11-25 14:00:00(从待办开始时间 到24-11-25 14:00:00 前共执行3次，每隔1小时); 3:2024-11-15 14:00:00',
    `create_at`         bigint(20) unsigned NOT NULL COMMENT '创建时间',
    `update_at`         bigint(20) unsigned          DEFAULT NULL COMMENT '更新时间',
    `deleted`           bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '状态: 0正常; 时间戳:删除时间',
    `approval_rate`     tinyint(4)          NOT NULL DEFAULT '0' COMMENT '任务通过比例: 0: 任意一人通过即通过；>0 通过/总数 > 比例为通过',
    PRIMARY KEY (`id`),
    UNIQUE KEY `u_todo_task_code_unique` (`task_code`),
    UNIQUE KEY `u_todo_task_unique` (`bill_id`, `target_user`, `bpm_instance_id`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1859580130877923330
  DEFAULT CHARSET = utf8mb4 COMMENT ='应用待办任务记录表';

CREATE TABLE `u_task_push_record`
(
    `id`            bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'id',
    `task_code`     varchar(64)         NOT NULL COMMENT '任务编号',
    `bill_type`     varchar(64)         NOT NULL COMMENT '业务类型',
    `push_type`     varchar(64)         NOT NULL COMMENT '推送类型（待办todo/已办done）',
    `push_param`    text                         DEFAULT NULL COMMENT '推送参数',
    `is_success`    varchar(64)         NOT NULL COMMENT '是否成功（Y/N）',
    `if_retry`      varchar(64)         NOT NULL COMMENT '是否重试（Y/N）',
    `push_result`   text                NOT NULL COMMENT '推送结果',
    `execute_time`  bigint(20)          NOT NULL COMMENT '执行时间',
    `execute_count` int(11)             NOT NULL DEFAULT '0' COMMENT '执行次数',
    `create_at`     bigint(20) unsigned NOT NULL COMMENT '创建时间',
    `update_at`     bigint(20) unsigned          DEFAULT NULL COMMENT '更新时间',
    `deleted`       bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '状态: 0正常; 时间戳:删除时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `u_todo_task_unique` (`task_code`, `push_type`)
) ENGINE = InnoDB
  AUTO_INCREMENT = 1860889973299486750
  DEFAULT CHARSET = utf8mb4 COMMENT ='推送任务记录表';
