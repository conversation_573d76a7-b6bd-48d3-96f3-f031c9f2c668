<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.cscec3b.iti</groupId>
        <artifactId>projectmanagement</artifactId>
        <version>1.0.0-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>projectmanagement-server</artifactId>
    <name>projectmanagement-server</name>
    <packaging>jar</packaging>

    <profiles>
        <!-- 本地环境 -->
        <profile>
            <id>local</id>
            <properties>
                <active-profile>local</active-profile>
            </properties>
        </profile>
        <!-- 开发联调环境 -->
        <profile>
            <id>dev</id>
            <!--默认启用-->
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <active-profile>dev</active-profile>
            </properties>
        </profile>
        <!-- 测试环境 -->
        <profile>
            <id>test</id>
            <properties>
                <active-profile>test</active-profile>
            </properties>
        </profile>
        <!-- 预生产环境 -->
        <profile>
            <id>pre</id>
            <properties>
                <active-profile>pre</active-profile>
            </properties>
        </profile>
        <!-- 生产环境 -->
        <profile>
            <id>prod</id>
            <properties>
                <active-profile>prod</active-profile>
            </properties>
        </profile>
    </profiles>

    <dependencies>
        <dependency>
            <groupId>com.cscec3b.iti</groupId>
            <artifactId>retry-spring-boot-starter</artifactId>
            <version>1.0.3-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.cscec3b.iti</groupId>
            <artifactId>logger-spring-boot-starter</artifactId>
            <version>1.0.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-json</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cscec3b.iti</groupId>
            <artifactId>projectmanagement-api</artifactId>
            <version>${project.parent.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>simpleclient</artifactId>
                    <groupId>io.prometheus</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
        </dependency>

        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper-spring-boot-starter</artifactId>
            <version>1.4.6</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsr305</artifactId>
                    <groupId>com.google.code.findbugs</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>HdrHistogram</artifactId>
                    <groupId>org.hdrhistogram</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-fileupload</artifactId>
                    <groupId>commons-fileupload</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cscec3b.iti</groupId>
            <artifactId>common-orika</artifactId>
            <version>${iti.common.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cscec3b.iti</groupId>
            <artifactId>common-stddict</artifactId>
            <version>${iti.common.version}</version>
        </dependency>
        <dependency>
            <groupId>com.cscec3b.iti</groupId>
            <artifactId>common-feign</artifactId>
            <version>${iti.common.version}</version>
        </dependency>
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>3.5.2</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>

        <!--单测覆盖率插件-->
        <dependency>
            <groupId>org.jacoco</groupId>
            <artifactId>jacoco-maven-plugin</artifactId>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
        </dependency>

        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4-rule-agent</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.shenyu</groupId>
            <artifactId>shenyu-spring-boot-starter-client-springmvc</artifactId>
        </dependency>

        <dependency>
            <groupId>com.cscec3b.iti.projectmanagement</groupId>
            <artifactId>project-projecttofinace</artifactId>
            <version>1.0.15.snapshot</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-http</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-jwt</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.pinming</groupId>
            <artifactId>openapi-client</artifactId>
            <version>1.3.2</version>
        </dependency>
        <dependency>
            <groupId>com.cscec3b.iti</groupId>
            <artifactId>common-redis</artifactId>
            <version>1.1.1-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.cscec3b.iti</groupId>
            <artifactId>common-portalmsg</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.aliyun.oss/aliyun-sdk-oss -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.16.0</version>
        </dependency>


        <!--        <dependency>-->
        <!--            <groupId>com.authine.cloudpivot</groupId>-->
        <!--            <artifactId>cloudpivot-java-sdk</artifactId>-->
        <!--            <version>1.8</version>-->
        <!--            <exclusions>-->
        <!--                <exclusion>-->
        <!--                    <groupId>com.squareup.okhttp3</groupId>-->
        <!--                    <artifactId>okhttp</artifactId>-->
        <!--                </exclusion>-->
        <!--            </exclusions>-->
        <!--        </dependency>-->
        <!-- 监控平台 -->
        <dependency>
            <groupId>com.cscec3b.iti</groupId>
            <artifactId>monitor-sdk-feign</artifactId>
            <version>1.0.11</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-sleuth</artifactId>
        </dependency>

        <dependency>
            <groupId>com.g3.security</groupId>
            <artifactId>g3-security</artifactId>
            <version>2.0.1-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>commons-validator</groupId>
            <artifactId>commons-validator</artifactId>
            <version>1.7</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-amqp</artifactId>
            <version>2.4.17</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
        </dependency>
        <dependency>
            <groupId>com.cscec3b.iti</groupId>
            <artifactId>task-message</artifactId>
            <version>0.0.7.release</version>
        </dependency>
        <!-- 对象池 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.11.1</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-text</artifactId>
            <version>1.10.0</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources/</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/profiles/${active-profile}/</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source> <!-- depending on your project -->
                    <target>1.8</target> <!-- depending on your project -->
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>0.2.0</version>
                        </path>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${mapstruct.version}</version>
                        </path>


                        <!-- other annotation processors -->
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <!--jacoco离线模式必须指定（解决powermock覆盖率统计不到的问题）-->
                <configuration>
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>target/coverage-reports/jacoco-unit.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>xls</nonFilteredFileExtension>
                        <nonFilteredFileExtension>xlsx</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <id>copy</id>
                        <phase>package</phase>
                        <configuration>
                            <target>
                                <copy file="target/${project.artifactId}-${project.version}.${project.packaging}"
                                      tofile="../target/${project.artifactId}-${project.version}.${project.packaging}"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!--单测覆盖率-->
            <plugin>
                <!--跳过jacoco 《 mvn clean install -Djacoco.skip=true 》 -->
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.8.7</version>
                <configuration>
                    <destFile>target/coverage-reports/jacoco-unit.exec</destFile>
                    <!--Jacoco是根据.exec文件生成最终的报告，所以需指定.exec的存放路径-->
                    <dataFile>target/coverage-reports/jacoco-unit.exec</dataFile>

                    <excludes>
                        <exclude>com/cscec3b/iti/template/server/entity/**/*</exclude>
                        <exclude>com/cscec3b/iti/template/server/controller/**/*</exclude>
                        <exclude>com/cscec3b/iti/template/server/TemplateApplication.class</exclude>
                    </excludes>

                    <!-- rules里面指定覆盖规则 -->
                    <rules>
                        <rule implementation="org.jacoco.maven.RuleConfiguration">
                            <element>BUNDLE</element>
                            <limits>
                                <!-- 指定方法覆盖最低 -->
                                <limit implementation="org.jacoco.report.check.Limit">
                                    <counter>METHOD</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0.80</minimum>
                                </limit>
                                <!-- 指定分支覆盖最低 -->
                                <limit implementation="org.jacoco.report.check.Limit">
                                    <counter>BRANCH</counter>
                                    <value>COVEREDRATIO</value>
                                    <minimum>0.80</minimum>
                                </limit>
                                <!-- 指定类覆盖到，最多Missed 0 -->
                                <limit implementation="org.jacoco.report.check.Limit">
                                    <counter>CLASS</counter>
                                    <value>MISSEDCOUNT</value>
                                    <maximum>0</maximum>
                                </limit>
                            </limits>
                        </rule>
                    </rules>
                </configuration>
                <executions>
                    <!--离线模式（解决powermock无法统计问题）-->
                    <execution>
                        <id>default-instrument</id>
                        <goals>
                            <goal>instrument</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-restore-instrumented-classes</id>
                        <goals>
                            <goal>restore-instrumented-classes</goal>
                        </goals>
                    </execution>

                    <!--生成报告-->
                    <execution>
                        <id>post-test</id>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>

                    <!--覆盖率检查-->
                    <execution>
                        <!--《 mvn clean install -Dmaven.test.failure.ignore=true 》 时会进行阈值检验，阈值超限会install失败，如果只是生成报告，可以注释此execution-->
                        <id>jacoco-check</id>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
