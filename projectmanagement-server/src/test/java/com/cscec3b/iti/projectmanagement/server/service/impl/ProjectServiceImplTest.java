package com.cscec3b.iti.projectmanagement.server.service.impl;

import com.cscec3b.iti.common.base.dictionary.YesNoEnum;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ContractRelationDto;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ProjectDto;
import com.cscec3b.iti.projectmanagement.api.dto.request.FinanceReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectQueryParams;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.FinanceResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectResp;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.BidOpenRecord;
import com.cscec3b.iti.projectmanagement.server.entity.BidSummary;
import com.cscec3b.iti.projectmanagement.server.entity.Contract;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.enums.ApiEnum;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.ProTenderMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.service.ITaskService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectProgressService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.http.HttpStatus;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertTrue;

@RunWith(PowerMockRunner.class)
@PrepareForTest({ProjectServiceImpl.class})
@Slf4j
public class ProjectServiceImplTest {

    @InjectMocks
    private ProjectServiceImpl projectServiceImpl;

    @Mock
    private ProjectMapper projectMapper;

    @Mock
    private ProTenderMapper proTenderMapper;

//    @Mock
//    private IAppStdOrgService stdOrgService;

    @Mock
    private StringRedisTemplate stringRedisTemplate;

    @Mock
    private ITaskService taskService;

    @Mock
    private ValueOperations valueOperations;

    @Mock
    private ProjectProgressService projectProgressService;

    @Before
    public void setUp() throws IllegalAccessException {
        projectServiceImpl = PowerMockito.spy(projectServiceImpl);
        PowerMockito.when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
        MemberModifier.field(ProjectServiceImpl.class, "earlyWarningService").set(projectServiceImpl, projectProgressService);

    }

    @Test
    public void qryProjectByConId() {
        PowerMockito.doReturn(Lists.newArrayList(new Project().setId(1l))).when(projectMapper)
                .qryProjectByConId(Mockito.any(), Mockito.any());
        assertTrue(projectServiceImpl.qryProjectByConId(Constants.NUMBER_ONE, 1l).size() == Constants.NUMBER_ONE);
    }

    @Test
    public void createProject() {
        PowerMockito.doReturn(Constants.NUMBER_ONE).when(projectMapper).createProject(Mockito.any());
        assertTrue(projectServiceImpl.createProject(new Project().setId(1l)) == Constants.NUMBER_ONE);
    }

    @Test
    public void setProjectMoney() throws Exception {
        Project req = new Project();
        req.setRegion("江夏区").setCountryProjectType("国家类型1").setMarketProjectType("市场类型1").setProjectType("公司类型1")
                .setInvestmentProjects("是").setNoTaxIncludedMoney(new BigDecimal(1)).setBusinessType("基础设施1")
                .setCustomerName("客户姓名2").setEnterpriseType("客户企业性质").setBusinessType("基础设施1")
                .setIndependentContractType(IndContractsTypeEnum.PRESENTATION.getDictCode()).setIndependentContractId(1l);
        PowerMockito.doReturn(new ProjectDto().setContractAmount(new BigDecimal(Constants.NUMBER_ONE)))
                .when(projectServiceImpl, "sumAmount", Mockito.any(),
                        Mockito.any());
        PowerMockito.doReturn(new ArrayList<>()).when(projectMapper).getAmount(Mockito.any(), Mockito.any());
        assertTrue(projectServiceImpl
                .setProjectMoney(new Project().setIndependentContractId(1l).setIndependentContractType(3).setId(1l))
                .getId() == 1);
        PowerMockito.doReturn(Lists.newArrayList(new ProjectDto().setTotalAmount(new BigDecimal(Constants.NUMBER_ONE))))
                .when(projectMapper).getAmount(Mockito.any(), Mockito.any());
        assertTrue(projectServiceImpl
                .setProjectMoney(new Project().setIndependentContractId(1l).setIndependentContractType(3).setId(1l))
                .getId() == 1);

    }

    @Test
    public void detail() throws Exception {
        PowerMockito.doReturn(new ProjectResp().setIndContractType(2)).when(projectMapper, "qryProjectById",
                Mockito.any());
        assertTrue(StringUtils.isNotEmpty(projectServiceImpl.detail(1l).getIndContractTypeStr()));

        PowerMockito.doReturn(new ProjectResp().setIndContractType(10)).when(projectMapper, "qryProjectById",
                Mockito.any());
        assertTrue(StringUtils.isEmpty(projectServiceImpl.detail(1l).getIndContractTypeStr()));
    }

    @Test
    public void contractRelationList() throws Exception {
        PowerMockito
                .doReturn(Lists.newArrayList(
                        new ContractRelationDto().setIsFirstContract(YesNoEnum.YES.getDictCode())
                                .setNodeType(IndContractsTypeEnum.PRESENTATION.getDictCode()).setNodeId(1l).setNodeName("测试")
                                .setOriginFileId(2l),
                        new ContractRelationDto().setIsFirstContract(YesNoEnum.YES.getDictCode())
                                .setNodeType(IndContractsTypeEnum.AGREEMENT_PRESENTATION.getDictCode()).setNodeId(1l).setNodeName("测试1")
                                .setOriginFileId(3l),
                        new ContractRelationDto().setIsFirstContract(YesNoEnum.YES.getDictCode())
                                .setNodeType(IndContractsTypeEnum.AGREEMENT_PRESENTATION.getDictCode()).setNodeId(1l).setNodeName("测试1")
                                .setOriginFileId(4l),
                        new ContractRelationDto().setIsFirstContract(YesNoEnum.NO.getDictCode())
                                .setNodeType(IndContractsTypeEnum.INTERNAL_PRESENTATION.getDictCode()).setNodeId(1l)
                                .setNodeName("测试2").setOriginFileId(4l),
                        new ContractRelationDto().setIsFirstContract(YesNoEnum.NO.getDictCode())
                                .setNodeType(IndContractsTypeEnum.INTERNAL_PRESENTATION.getDictCode()).setNodeId(1l)
                                .setNodeName("测试2").setOriginFileId(6l)))
                .when(projectMapper, "qryContractList", Mockito.any(), Mockito.any());

        assertTrue(!Constants.NUMBER_ZERO.equals(projectServiceImpl
                .contractRelationList(2l, IndContractsTypeEnum.TENDER_SUMMARY.getDictCode()).getRootContractList().size()));

        assertTrue(!Constants.NUMBER_ZERO.equals(projectServiceImpl
                .contractRelationList(3l, IndContractsTypeEnum.AGREEMENT_PRESENTATION.getDictCode()).getRootContractList().size()));

        assertTrue(!Constants.NUMBER_ZERO.equals(
                projectServiceImpl.contractRelationList(4l, IndContractsTypeEnum.INTERNAL_PRESENTATION.getDictCode())
                        .getRootContractList().size()));

        assertTrue(Constants.NUMBER_ZERO
                .equals(projectServiceImpl.contractRelationList(4l, IndContractsTypeEnum.INTERNAL_AGREEMENT.getDictCode())
                        .getRootContractList().size()));
    }

    @Test
    public void tenderRelationList() throws Exception {
        PowerMockito.doReturn(Lists.newArrayList(
                        new ContractRelationDto().setIsFirstContract(YesNoEnum.NO.getDictCode())
                                .setNodeType(IndContractsTypeEnum.PRESENTATION.getDictCode()).setNodeId(1l).setNodeName("测试")
                                .setOriginFileId(2l),
                        new ContractRelationDto().setIsFirstContract(YesNoEnum.NO.getDictCode())
                                .setNodeType(IndContractsTypeEnum.PRESENTATION.getDictCode()).setNodeId(4l).setNodeName("测试1")
                                .setOriginFileId(3l)))
                .when(projectMapper, "qryTenderList", Mockito.any(), Mockito.any());

        assertTrue(!Constants.NUMBER_ZERO.equals(projectServiceImpl
                .tenderRelationList(2l, IndContractsTypeEnum.TENDER_SUMMARY.getDictCode()).getRootContractList().size()));

        assertTrue(!Constants.NUMBER_ZERO.equals(projectServiceImpl
                .tenderRelationList(3l, IndContractsTypeEnum.AGREEMENT_PRESENTATION.getDictCode()).getRootContractList().size()));
    }

    @Test
    public void contractDetail() throws Exception {
        Contract contract =
                new Contract().setProjectBelong(Constants.PROJECT_BELONG).setProvince("湖北").setCity("武汉").setRegion("江夏区")
                        .setContractMode1("模式1").setContractMode2("模式2").setMarketProjectType("口径1").setProjectType("类型");
        PowerMockito.doReturn(contract).when(proTenderMapper, "qryContractById", Mockito.anyLong());
        assertTrue(StringUtils.isNotEmpty(projectServiceImpl.contractDetail(2l).getRegion()));

        contract.setContractMode2("").setProjectBelong("海外").setCountry("中国").setMarketProjectType2("口径2")
                .setProjectType2("类型2").setProjectType3("类型3").setProjectType4("类型4");
        PowerMockito.doReturn(contract).when(proTenderMapper, "qryContractById", Mockito.anyLong());
        assertTrue(StringUtils.isNotEmpty(projectServiceImpl.contractDetail(2l).getRegion()));
    }

    @Test
    public void tenderDetail() throws Exception {
        BidSummary bidSummary =
                new BidSummary().setProjectBelong(Constants.PROJECT_BELONG).setProvince("湖北").setCity("武汉").setRegion("江夏区")
                        .setContractMode1("模式1").setContractMode2("模式2").setMarketProjectType("口径1").setProjectType("类型")
                        .setStructuralStyle("结构形式");
        PowerMockito.doReturn(bidSummary).when(proTenderMapper, "qryTenderById", Mockito.anyLong());
        assertTrue(StringUtils.isNotEmpty(projectServiceImpl.tenderDetail(2l).getRegion()));

        bidSummary.setContractMode2("").setProjectBelong("海外").setCountry("中国").setMarketProjectType2("口径2")
                .setProjectType2("类型2").setProjectType3("类型3").setProjectType4("类型4").setStructuralStyle2("结构形式2");
        PowerMockito.doReturn(bidSummary).when(proTenderMapper, "qryTenderById", Mockito.anyLong());
        PowerMockito.doReturn(new ArrayList<>()).when(proTenderMapper, "qryRecordByRelationId", Mockito.anyLong());
        assertTrue(StringUtils.isNotEmpty(projectServiceImpl.tenderDetail(2l).getRegion()));

        PowerMockito
                .doReturn(Lists.newArrayList(new BidOpenRecord().setDataFrom(Constants.DATA_FROM_Y_CODE).setId(1l),
                        new BidOpenRecord().setDataFrom(Constants.DATA_FROM_N_CODE).setId(2l),
                        new BidOpenRecord().setDataFrom("").setId(3l)))
                .when(proTenderMapper, "qryRecordByRelationId", Mockito.anyLong());
        assertTrue(StringUtils.isNotEmpty(projectServiceImpl.tenderDetail(2l).getRegion()));
    }

    @Test
    public void sumAmount() throws Exception {
        PowerMockito.doReturn(new BigDecimal(Constants.NUMBER_ONE)).when(projectServiceImpl, "getBigDecimal",
                Mockito.any());
        Method privateMethod =
                ProjectServiceImpl.class.getDeclaredMethod("sumAmount", ProjectDto.class, ProjectDto.class);
        privateMethod.setAccessible(true);
        privateMethod.invoke(projectServiceImpl, null, new ProjectDto().setTotalAmount(new BigDecimal(1l)));
        privateMethod.invoke(projectServiceImpl, new ProjectDto().setTotalAmount(new BigDecimal(1l)),
                new ProjectDto().setTotalAmount(new BigDecimal(1l)));
        privateMethod.invoke(projectServiceImpl, new ProjectDto().setTotalAmount(new BigDecimal(1l)), null);
    }

    @Test
    public void getBigDecimal() throws Exception {
        Method privateMethod = ProjectServiceImpl.class.getDeclaredMethod("getBigDecimal", BigDecimal.class);
        privateMethod.setAccessible(true);
        privateMethod.invoke(projectServiceImpl, new BigDecimal(Constants.NUMBER_ONE));
    }

    @Test
    public void updateProject() throws Exception {
        PowerMockito.doReturn(1).when(projectMapper).updateProjectById(Mockito.any());
        Assert.assertEquals(1, projectServiceImpl.updateProject(new Project()));
    }

    @Test
    public void pageListByCondition() {
        List<ProjectResp> resp = new ArrayList<>();
        resp.add(new ProjectResp());
        resp.add(new ProjectResp());
        resp.add(new ProjectResp());
        PowerMockito.doReturn(resp).when(projectMapper).pageList(Mockito.any());
        ProjectQueryParams queryParams = new ProjectQueryParams();
        queryParams.setSize(2);
        Assert.assertEquals(resp.size(), projectServiceImpl.pageListByCondition(queryParams).getTotal());

    }

    @Test
    public void updateProjectInner() {
        ProjectUpdateReq req = new ProjectUpdateReq();
        PowerMockito.doReturn(0).when(projectMapper).updateProjectInPMById(Mockito.any());
        try {
            projectServiceImpl.updateProjectInner(req);
        } catch (BusinessException e) {
            Assert.assertEquals(8010008, e.getStatus());
        }
        PowerMockito.doReturn(1).when(projectMapper).updateProjectInPMById(Mockito.any());
        Assert.assertTrue(projectServiceImpl.updateProjectInner(req));
    }

    @Test
    public void getProjectInfo() {
        PowerMockito.doReturn(null).when(projectMapper).selectById(Mockito.anyLong());
        try {
            projectServiceImpl.getProjectInfo(10L);
        } catch (BusinessException e) {
            Assert.assertEquals(8010081, e.getStatus());
        }
        Project project = new Project();
        project.setId(10L);
        PowerMockito.doReturn(project).when(projectMapper).selectById(Mockito.anyLong());
        Assert.assertEquals(project.getId(), projectServiceImpl.getProjectInfo(10L).getId());
    }

//    @Test
//    public void searchOrgByNameDomain() {
//        GenericityResponse<List<AppFuzzyQueryStdOrgRes>> response = new GenericityResponse<>();
//        ArrayList<AppFuzzyQueryStdOrgRes> data = new ArrayList<>();
//        response.setData(data);
//        PowerMockito.doReturn(response).when(stdOrgService).fuzzyQuery(Mockito.anyString(), Mockito.anyString(),
//            Mockito.anyString());
//        Assert.assertEquals(0, projectServiceImpl.searchOrgByNameDomain("code", "name", "abbreviation").size());
//        AppFuzzyQueryStdOrgRes stdOrgRes = new AppFuzzyQueryStdOrgRes();
//        stdOrgRes.setIdPath("/1/");
//        stdOrgRes.setId(2L);
//        stdOrgRes.setChildrenNum(3);
//        stdOrgRes.setOrgType(2);
//        stdOrgRes.setFullName("标准组织");
//        stdOrgRes.setCode("code");
//        stdOrgRes.setParentOrgName("标准组织父name");
//        data.add(stdOrgRes);
//        PowerMockito.doReturn(response).when(stdOrgService).fuzzyQuery(Mockito.anyString(), Mockito.anyString(),
//            Mockito.anyString());
//        Assert.assertEquals(1, projectServiceImpl.searchOrgByNameDomain("code", "name", "abbreviation").size());
//    }
//
//    @Test
//    public void getNodeListByParentCode() {
//        GenericityResponse<List<StdorganizationTreeNodeRes>> response = new GenericityResponse<>();
//        ArrayList<StdorganizationTreeNodeRes> data = new ArrayList<>();
//        response.setData(data);
//        PowerMockito.doReturn(response).when(stdOrgService).orgTreeNode(Mockito.anyString());
//        Assert.assertEquals(0, projectServiceImpl.getNodeListByParentCode("code").size());
//        StdorganizationTreeNodeRes stdOrgRes = new StdorganizationTreeNodeRes();
//        stdOrgRes.setIdPath("/1/");
//        stdOrgRes.setId(2L);
//        stdOrgRes.setChildrenNum(3);
//        stdOrgRes.setOrgType(2);
//        stdOrgRes.setFullName("标准组织");
//        stdOrgRes.setCode("code");
//        data.add(stdOrgRes);
//        PowerMockito.doReturn(response).when(stdOrgService).orgTreeNode(Mockito.anyString());
//        Assert.assertEquals(1, projectServiceImpl.getNodeListByParentCode("code").size());
//    }
//
//    @Test
//    public void getRootNode() {
//        PowerMockito.mockStatic(UserUtil.class);
//        PowerMockito.mock(UserUtil.class);
//        PowerMockito.when(UserUtil.getUserInfoResp()).thenReturn(null);
//        Assert.assertNull(projectServiceImpl.getRootNode().getId());
//        PmUserInfoResp pmUserInfoResp = new PmUserInfoResp();
//        pmUserInfoResp.setCurrentOrg(null);
//        PowerMockito.when(UserUtil.getUserInfoResp()).thenReturn(pmUserInfoResp);
//        try {
//            projectServiceImpl.getRootNode();
//        } catch (BusinessException e) {
//            Assert.assertEquals(8010104, e.getStatus());
//        }
//        CurrentOrg org = new CurrentOrg();
//        org.setChildrenNum(2).setIdPath("/1/").setStdOrgId(2L).setStdOrgCode("std code").setStdOrgName("name")
//        .setLeanBuildOrgId("not std code");
//        pmUserInfoResp.setCurrentOrg(org);
//        PowerMockito.when(UserUtil.getUserInfoResp()).thenReturn(pmUserInfoResp);
//        Assert.assertEquals("/1/2/", projectServiceImpl.getRootNode().getIdPath());
//    }

    @Test
    public void updateProjectStatus() {
        Long id = 1L;
        Integer projectStatus = 1;
        PowerMockito.doReturn(1).when(projectMapper).checkProjectInfo(Mockito.any());
        PowerMockito.doReturn(1).when(projectMapper).updateStatusById(Mockito.any(), Mockito.any());
        Assert.assertEquals(1, projectServiceImpl.updateProjectStatus(id, projectStatus));

        projectStatus = 0;
        PowerMockito.doReturn(1).when(projectMapper).checkProjectInfo(Mockito.any());
        Assert.assertEquals(0, projectServiceImpl.updateProjectStatus(id, projectStatus));
    }

    @Test
    public void selectById() {
        PowerMockito.doReturn(new Project()).when(projectMapper).selectById(Mockito.any());
        Assert.assertNotNull(projectServiceImpl.selectById(1L));
    }

    @Test
    public void pushDataToFinance() {
        Long id = null;
        try {
            projectServiceImpl.pushDataToFinance(id);
        } catch (BusinessException e) {
            Assert.assertEquals(8010004, e.getStatus());
        }

        id = 1L;
        try {
            Assert.assertEquals(0, projectServiceImpl.pushDataToFinance(id).getStatus());
        } catch (NullPointerException e) {
        }
    }

    @Test
    public void financeGetData() {
        String id = "7", type = "project1";
        PowerMockito.doReturn(new FinanceResp()).when(projectMapper).getMarketingProjectForFinance(Mockito.any());
        Assert.assertNull(projectServiceImpl.financeGetData(id, type).getData());
        type = ApiEnum.TYPE_PROJECT.getName();
        PowerMockito.doReturn(new FinanceResp()).when(projectMapper).getMarketingProjectForFinance(Mockito.any());
        Assert.assertNotNull(projectServiceImpl.financeGetData(id, type).getData());
    }

    @Test
    public void financeUpdateData() throws Exception {
        FinanceReq financeReq = new FinanceReq();
        PowerMockito.doReturn("1").when(valueOperations).get(Mockito.any());
        Assert.assertEquals(projectServiceImpl.financeUpdateData(financeReq).getStatus(), HttpStatus.OK.value());

        financeReq.setProjectId("1");
        PowerMockito.doReturn("").when(valueOperations).get(Mockito.any());
        PowerMockito.doReturn(new Project().setId(12L).setProjectStatus(YesNoEnum.YES.getDictCode()))
                .when(projectMapper).selectById(Mockito.any());
        Assert.assertThrows(BusinessException.class, () -> projectServiceImpl.financeUpdateData(financeReq));

        PowerMockito.doReturn(new Project().setId(12L).setProjectStatus(YesNoEnum.NO.getDictCode())
                        .setProjectDeptId("1").setProjectDeptName("1"))
                .when(projectMapper).selectById(Mockito.any());
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(projectMapper).updateProjectByFinance(Mockito.any());
        Assert.assertThrows(BusinessException.class, () -> projectServiceImpl.financeUpdateData(financeReq));

        PowerMockito.doReturn(Constants.NUMBER_ONE).when(projectMapper).updateProjectByFinance(Mockito.any());
        PowerMockito.doNothing().when(projectServiceImpl, "updateProjectProgressStatus", Mockito.any(), Mockito.anyLong());
        Assert.assertEquals(projectServiceImpl.financeUpdateData(financeReq).getStatus(), HttpStatus.OK.value());
    }

    @Test
    public void saveProjectTask() {
        ProjectReq projectReq = new ProjectReq();
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(projectMapper).updateProjectByProjectReq(Mockito.any());
        Assert.assertThrows(BusinessException.class, () -> projectServiceImpl.saveProjectTask(projectReq));

        PowerMockito.doReturn(Constants.NUMBER_ONE).when(projectMapper).updateProjectByProjectReq(Mockito.any());
        Assert.assertTrue(projectServiceImpl.saveProjectTask(projectReq));
    }

    @Test
    public void submitProjectTask() {
        ProjectReq projectReq = new ProjectReq();
        PowerMockito.doReturn(new Project().setProjectStatus(YesNoEnum.YES.getDictCode()))
                .when(projectMapper).selectById(Mockito.any());
        Assert.assertThrows(BusinessException.class, () -> projectServiceImpl.submitProjectTask(projectReq));

        PowerMockito.doReturn(new Project().setProjectStatus(YesNoEnum.NO.getDictCode()))
                .when(projectMapper).selectById(Mockito.any());
        Assert.assertThrows(BusinessException.class, () -> projectServiceImpl.submitProjectTask(projectReq));

        PowerMockito.doReturn(new Project().setProjectStatus(YesNoEnum.NO.getDictCode())
                        .setProjectDeptId("1").setProjectDeptName("1").setProjectFinanceCode("1").setProjectFinanceName("1"))
                .when(projectMapper).selectById(Mockito.any());
        PowerMockito.doReturn(true).when(projectServiceImpl).saveProjectTask(Mockito.any());
        PowerMockito.doReturn(true).when(taskService).updateStatusDone(Mockito.any());
        Assert.assertTrue(projectServiceImpl.submitProjectTask(projectReq));
    }

//    @Test
//    public void externalOpenProject() {
//        ProjectOpenReq projectOpenReq = new ProjectOpenReq();
//        Assert.assertThrows(BusinessException.class, () -> projectServiceImpl.externalOpenProject(projectOpenReq));
//
//        projectOpenReq.setProjectDeptId("1");
//        PowerMockito.doReturn(new GenericityResponse<StandardOrganizationInfoRes>())
//                .when(stdOrgService).geOrgInfoByCode(Mockito.any());
//        Assert.assertThrows(FrameworkException.class, () -> projectServiceImpl.externalOpenProject(projectOpenReq));
//
//        StandardOrganizationInfoRes orgInfo = new StandardOrganizationInfoRes();
//        orgInfo.setId(1L);
//        orgInfo.setIdPath("1/2");
//        PowerMockito.doReturn(new GenericityResponse<>(0, "msg", orgInfo))
//                .when(stdOrgService).geOrgInfoByCode(Mockito.any());
//        Assert.assertNotNull(projectServiceImpl.externalOpenProject(projectOpenReq));
//    }

//    @Test
//    public void createCommandDeptByUc() throws Exception {
//        Project project = new Project().setProjectDeptId("1234").setProjectDeptName("指挥部1");
//        PowerMockito.doReturn(project).when(projectMapper, "selectById",
//                Mockito.any());
//        CreateCommandDeptReq createCommandDeptReq = new CreateCommandDeptReq();
//        try {
//            projectServiceImpl.createCommandDeptByUc(1L, createCommandDeptReq);
//        } catch (BusinessException ex) {
//            Assert.assertEquals(8010045, ex.getStatus());
//        }
//
//        createCommandDeptReq.setFullName("指挥部11").setOrgType(1);
//        PowerMockito.doReturn(new Project()).when(projectMapper, "selectById",
//                Mockito.any());
//        try {
//            projectServiceImpl.createCommandDeptByUc(1L, createCommandDeptReq);
//        } catch (BusinessException ex) {
//            Assert.assertEquals(8010041, ex.getStatus());
//        }
//
//        GenericityResponse<CreatedRes> createdCmdRes = new GenericityResponse<>();
//        createdCmdRes.setStatus(1);
//        createCommandDeptReq.setOrgType(2);
//        PowerMockito.doReturn(createdCmdRes).when(stdOrgService, "createCommandDept",
//                Mockito.any(), Mockito.any());
//        try {
//            projectServiceImpl.createCommandDeptByUc(1L, createCommandDeptReq);
//        } catch (FrameworkException fx) {
////            Assert.assertThrows(createdCmdRes.getMsg());
//        }
//
//        CreatedRes createdRes = new CreatedRes(false, "code", "/1/2/", 11L);
//        createdRes.setIsSuccess(false);
//        createdCmdRes.setStatus(0);
//        createdCmdRes.setData(createdRes);
//        PowerMockito.doReturn(createdCmdRes).when(stdOrgService, "createCommandDept",
//                Mockito.any(), Mockito.any());
//        try {
//            projectServiceImpl.createCommandDeptByUc(1L, createCommandDeptReq);
//        } catch (BusinessException ex) {
//            Assert.assertEquals(8010042, ex.getStatus());
//        }
//
//        createdRes.setIsSuccess(true);
//        createdCmdRes.setData(createdRes);
//        PowerMockito.doReturn(createdCmdRes).when(stdOrgService, "createCommandDept",
//                Mockito.any(), Mockito.any());
//        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(projectMapper, "updateProjectCmdByUc",
//                Mockito.any());
//        try {
//            projectServiceImpl.createCommandDeptByUc(1L, createCommandDeptReq);
//        } catch (BusinessException ex) {
//            Assert.assertEquals(8010043, ex.getStatus());
//        }
//        createdRes.setIsSuccess(true);
//        PowerMockito.doReturn(new Project()).when(projectMapper, "selectById",
//                Mockito.any());
//        PowerMockito.doReturn(Constants.NUMBER_ONE).when(projectMapper, "updateProjectCmdByUc",
//                Mockito.any());
//        assertTrue(createdCmdRes.getData().equals(projectServiceImpl.createCommandDeptByUc(1L,
//        createCommandDeptReq)));
//
//    }
//
//    @Test
//    public void getCommandDeptInfo() throws Exception {
//        GenericityResponse<CommandDeptInfoRes> commandDeptInfo = new GenericityResponse<>();
//        commandDeptInfo.setStatus(1);
//        PowerMockito.doReturn(commandDeptInfo).when(stdOrgService, "getCommandDeptInfo",
//                Mockito.any());
//        try {
//            projectServiceImpl.getCommandDeptInfo(Mockito.any());
//        } catch (FrameworkException fx) {
////            Assert.assertThrows(createdCmdRes.getMsg());
//        }
//        commandDeptInfo.setStatus(0);
//        try {
//            projectServiceImpl.getCommandDeptInfo(Mockito.any());
//        } catch (BusinessException ex) {
//            Assert.assertEquals(8010044, ex.getStatus());
//        }
//        commandDeptInfo.setData(new CommandDeptInfoRes());
//        PowerMockito.doReturn(commandDeptInfo).when(stdOrgService, "getCommandDeptInfo",
//                Mockito.any());
//        projectServiceImpl.getCommandDeptInfo(Mockito.any());
//    }
}
