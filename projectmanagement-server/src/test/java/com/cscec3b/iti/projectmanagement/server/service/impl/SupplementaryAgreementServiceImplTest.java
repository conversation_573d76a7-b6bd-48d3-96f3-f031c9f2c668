package com.cscec3b.iti.projectmanagement.server.service.impl;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.request.MarketProReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.SupplementaryAgreementReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.SupplementaryAgreementResp;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectProgress;
import com.cscec3b.iti.projectmanagement.server.entity.SupplementaryAgreement;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.SupplementaryAgreementMapper;
import com.cscec3b.iti.projectmanagement.server.service.ProjectProgressService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.api.support.membermodification.MemberModifier;
import org.powermock.core.classloader.annotations.PrepareForTest;
import org.powermock.modules.junit4.PowerMockRunner;
import org.powermock.reflect.Whitebox;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertTrue;

/**
 * @Description SupplementaryAgreementServiceImplTest
 * <AUTHOR>
 * @Date 2022/10/27 15:22
 */
@Slf4j
@RunWith(PowerMockRunner.class)
@PrepareForTest({SupplementaryAgreementServiceImpl.class})
public class SupplementaryAgreementServiceImplTest {

    @Mock
    private SupplementaryAgreementMapper supplementaryAgreementMapper;

    @Mock
    private TaskServiceImpl taskService;

    @Mock
    private ProjectService projectService;

    @Mock
    private ProjectProgressService projectProgressService;

    @InjectMocks
    private SupplementaryAgreementServiceImpl supplementaryAgreementService;

    @Before
    public void setUp() throws Exception {
        supplementaryAgreementService = PowerMockito.spy(supplementaryAgreementService);
        MemberModifier.field(SupplementaryAgreementServiceImpl.class, "supplementaryAgreementMapper")
                .set(supplementaryAgreementService, supplementaryAgreementMapper);
        MemberModifier.field(SupplementaryAgreementServiceImpl.class, "earlyWarningService")
                .set(supplementaryAgreementService, projectProgressService);
    }

    @Test
    public void approvalBySupplementaryAgreement() throws Exception {
        SupplementaryAgreementReq saReq = new SupplementaryAgreementReq();
        saReq.setIsCreateHead(Constants.IS_CREATE_HEAD).setBusinessType(Constants.BUSINESS_TYPE);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("123", 211);
        map.put("222", "3242");
        MarketProReq<SupplementaryAgreementReq> request = new MarketProReq<>();
        saReq.setProjectBelong("国外").setProvince("湖北").setCity("武汉").setRegion("江夏区").setAddress("中国.湖北.武汉.洪山区.中建三局G5-1")
                .setCountry("中国")
                .setCountryProjectType("国家类型1").setMarketProjectType("市场类型1").setProjectType("公司类型1")
                .setProjectAttachment1(Lists.newArrayList(map)).setInvestmentProjects("是")
                .setNoTaxIncludedMoney(new BigDecimal(1)).setBusinessType("基础设施1").setCustomerName("客户姓名2")
                .setEnterpriseType("客户企业性质").setIsCreateHead("否").setBusinessType("基础设施1");

        request.setData(saReq).setAssociatedId(0L).setOriginFileType("");

        try {
            //独立合同类型获取异常
            supplementaryAgreementService.approvalBySupplementaryAgreement(request);
        } catch (BusinessException e) {
            Assert.assertEquals(8010009, e.getStatus());
        }

        request.setOriginFileType("补充协议");
        List<Project> list = new ArrayList<>();
        list.add(new Project());
        PowerMockito.doReturn(list).when(projectService, "qryProjectByConId", Mockito.any(), Mockito.any());
        PowerMockito.doNothing().when(supplementaryAgreementService, "saveSupplementaryAgreement", Mockito.any(), Mockito.any(), Mockito.any());
        try {
            // 项目已存在，请勿重复立项
            supplementaryAgreementService.approvalBySupplementaryAgreement(request);
        } catch (BusinessException e) {
            Assert.assertEquals(8010011, e.getStatus());
        }

        PowerMockito.doReturn(null).when(projectService, "qryProjectByConId", Mockito.any(), Mockito.any());
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(projectService, "createProject", Mockito.any());
        try {
            // 项目信息入库失败
            supplementaryAgreementService.approvalBySupplementaryAgreement(request);
        } catch (BusinessException e) {
            Assert.assertEquals(8010012, e.getStatus());
        }
        saReq.setProjectBelong(Constants.PROJECT_BELONG).setIsCreateHead(Constants.IS_CREATE_HEAD).setContractMode1("模式1").setContractMode2("模式2").setMarketProjectType("口径1")
                .setMarketProjectType2("口径2").setProjectType("类型1").setProjectType2("类型2").setProjectType3("类型3")
                .setProjectType4("类型4").setApprovalPerson("HCY")
                .setBusinessType(Constants.BUSINESS_TYPE);
        request.setData(saReq).setAssociatedId(1L).setOriginFileType("补充协议");
        PowerMockito.doReturn(Constants.NUMBER_ONE).when(projectService, "createProject", Mockito.any());
        PowerMockito.doReturn(Boolean.TRUE).when(taskService, "createTask", Mockito.any());

        PowerMockito.doReturn(1).when(projectService, "createProject", Mockito.any());
        GenericityResponse<Object> response = new GenericityResponse<>();
        PowerMockito.doReturn(response).when(projectService, "pushDataToFinance", Mockito.anyLong());
        assertTrue(supplementaryAgreementService.approvalBySupplementaryAgreement(request));
    }

    @Test
    public void entrySupplementaryAgreement() throws Exception {
        SupplementaryAgreementReq saReq = new SupplementaryAgreementReq();
        saReq.setIsCreateHead(Constants.IS_CREATE_HEAD).setBusinessType(Constants.BUSINESS_TYPE);
        MarketProReq<SupplementaryAgreementReq> request = new MarketProReq<>();
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("123", 211);
        map.put("222", "3242");
        saReq.setProjectBelong("国外").setProvince("湖北").setCity("武汉").setRegion("江夏区").setAddress("中国.湖北.武汉.洪山区.中建三局G5-1")
                .setCountry("中国")
                .setCountryProjectType("国家类型1").setMarketProjectType("市场类型1").setProjectType("公司类型1")
                .setProjectAttachment1(Lists.newArrayList(map)).setInvestmentProjects("是")
                .setNoTaxIncludedMoney(new BigDecimal(1)).setBusinessType("基础设施1").setCustomerName("客户姓名2")
                .setEnterpriseType("客户企业性质").setIsCreateHead("否").setBusinessType("基础设施1");
        request.setData(new SupplementaryAgreementReq()).setAssociatedId(0L).setOriginFileType("");
        try {
            //独立合同类型获取异常
            supplementaryAgreementService.entrySupplementaryAgreement(request);
        } catch (BusinessException e) {
            Assert.assertEquals(8010009, e.getStatus());
        }

        request.setOriginFileType("补充协议");
        PowerMockito.doReturn(new SupplementaryAgreement().setId(11L))
                .when(supplementaryAgreementMapper, "qrySupplementaryAgreementByBelongId", Mockito.any());
        try {
            // 补充协议已录入，请勿重复录入
            supplementaryAgreementService.entrySupplementaryAgreement(request);
        } catch (BusinessException e) {
            Assert.assertEquals(8010025, e.getStatus());
        }
        saReq.setProjectBelong(Constants.PROJECT_BELONG).setIsCreateHead(Constants.IS_CREATE_HEAD);
        request.setData(saReq).setAssociatedId(1L).setOriginFileType("补充协议");
        PowerMockito.doReturn(null)
                .when(supplementaryAgreementMapper, "qrySupplementaryAgreementByBelongId", Mockito.any());
        PowerMockito.doNothing()
                .when(supplementaryAgreementService, "saveSupplementaryAgreement", Mockito.any(), Mockito.any(), Mockito.any());
        PowerMockito.doReturn(new Project())
                .when(supplementaryAgreementService, "getProjectBySa", Mockito.any(), Mockito.any(), Mockito.any());
        PowerMockito.doReturn(new Project()).when(projectService, "setProjectMoney", Mockito.any());
        PowerMockito.doReturn(Constants.NUMBER_ZERO).when(projectService, "updateProject", Mockito.any());
        try {
            // 补充协议更新项目信息失败
            supplementaryAgreementService.entrySupplementaryAgreement(request);
        } catch (BusinessException e) {
            Assert.assertEquals(8010022, e.getStatus());
        }

        saReq.setProjectBelong(Constants.PROJECT_BELONG).setIsCreateHead(Constants.IS_CREATE_HEAD).setContractMode1("模式1").setContractMode2("模式2").setMarketProjectType("口径1")
                .setMarketProjectType2("口径2").setProjectType("类型1").setProjectType2("类型2").setProjectType3("类型3")
                .setProjectType4("类型4").setApprovalPerson("HCY")
                .setBusinessType(Constants.BUSINESS_TYPE);
        request.setData(saReq).setAssociatedId(1L).setOriginFileType("补充协议");
        PowerMockito.doReturn(Constants.NUMBER_ONE).when(projectService, "updateProject", Mockito.any());
        assertTrue(supplementaryAgreementService.entrySupplementaryAgreement(request));

        saReq.setProjectBelong("国外");
        request.setData(saReq);
        assertTrue(supplementaryAgreementService.entrySupplementaryAgreement(request));
    }

    @Test
    public void saveSupplementaryAgreement() throws Exception {
        PowerMockito.doReturn(Constants.NUMBER_ZERO)
                .when(supplementaryAgreementMapper, "createSupplementaryAgreement", Mockito.any());
        SupplementaryAgreementReq saReq = new SupplementaryAgreementReq();
        saReq.setIsCreateHead(Constants.IS_CREATE_HEAD).setBusinessType(Constants.BUSINESS_TYPE);
        Method save =
                SupplementaryAgreementServiceImpl
                        .class.getDeclaredMethod("saveSupplementaryAgreement", SupplementaryAgreementReq.class, Long.class, IndContractsTypeEnum.class);
        save.setAccessible(true);
        try {
            Whitebox.invokeMethod(supplementaryAgreementService, "saveSupplementaryAgreement", saReq, 1L, IndContractsTypeEnum.AGREEMENT_PRESENTATION);
//            save.invoke(supplementaryAgreementService,  saReq, 1L,IndContractsTypeEnum.AGREEMENT);
        } catch (BusinessException e) {
            Assert.assertEquals(8010026, e.getStatus());
        }
        PowerMockito.doReturn(Constants.NUMBER_ONE)
                .when(supplementaryAgreementMapper, "createSupplementaryAgreement", Mockito.any());
        save.invoke(supplementaryAgreementService, saReq, 1L, IndContractsTypeEnum.AGREEMENT_PRESENTATION);
    }

    @Test
    public void getProjectBySa() throws Exception {
        List<Project> projectList = new ArrayList<>();
        Project p = new Project();
        projectList.add(p);
        SupplementaryAgreementReq saReq = new SupplementaryAgreementReq();
        saReq.setIsCreateHead(Constants.IS_CREATE_HEAD).setBusinessType(Constants.BUSINESS_TYPE);

        PowerMockito.doReturn(new ArrayList<>())
                .when(projectService, "qryProjectByConId", Mockito.any(), Mockito.any());
        try {
            Whitebox.invokeMethod(supplementaryAgreementService, "getProjectBySa", saReq, 1L, IndContractsTypeEnum.AGREEMENT_PRESENTATION);
        } catch (BusinessException e) {
            Assert.assertEquals(8010021, e.getStatus());
        }

        PowerMockito.doReturn(projectList)
                .when(projectService, "qryProjectByConId", Mockito.any(), Mockito.any());
        Whitebox.invokeMethod(supplementaryAgreementService, "getProjectBySa", saReq, 1L, IndContractsTypeEnum.AGREEMENT_PRESENTATION);
    }

    @Test
    public void supplementaryAgreementDetail() throws Exception {
        PowerMockito.doReturn(null)
                .when(supplementaryAgreementMapper, "getSupplementaryAgreementDetail", Mockito.any());
        try {
            // 补充协议详情为空
            supplementaryAgreementService.supplementaryAgreementDetail(1L);
        } catch (BusinessException e) {
            Assert.assertEquals(8010029, e.getStatus());
        }
        SupplementaryAgreementResp resp = new SupplementaryAgreementResp().setProjectBelong(Constants.PROJECT_BELONG).setProvince("湖北").setCity("武汉").setRegion("江夏区").setAddress("中国.湖北.武汉.洪山区.中建三局G5-1")
                .setCountry("中国")
                .setCountryProjectType("国家类型1").setMarketProjectType("市场类型1").setProjectType("公司类型1")
                .setNoTaxIncludedMoney(new BigDecimal(1)).setBusinessType("基础设施1").setCustomerName("客户姓名2")
                .setEnterpriseType("客户企业性质").setBusinessType("基础设施1")
                .setMarketProjectType2("口径2").setProjectType("类型1").setProjectType2("类型2").setProjectType3("类型3")
                .setProjectType4("类型4").setApprovalPerson("HCY")
                .setContractMode1("合同1").setContractMode2("合同2");
        PowerMockito.doReturn(resp)
                .when(supplementaryAgreementMapper, "getSupplementaryAgreementDetail", Mockito.any());
        supplementaryAgreementService.supplementaryAgreementDetail(1L);

        resp.setProjectBelong("国外");
        supplementaryAgreementService.supplementaryAgreementDetail(1L);

    }

    @Test
    public void updateProjectProgressStatus() throws Exception {
        ProjectProgress projectProgress = new ProjectProgress();
        projectProgress.setSignStatus(1);
        PowerMockito.doReturn(projectProgress).when(projectProgressService, "selectProjectProgress", Mockito.anyLong());
//        supplementaryAgreementService.updateProjectProgressStatus(IndContractsTypeEnum.TENDER_SUMMARY,33L);

        projectProgress.setSignStatus(0);
        PowerMockito.doReturn(projectProgress).when(projectProgressService, "selectProjectProgress", Mockito.anyLong());
        PowerMockito.doNothing().when(projectProgressService, "updateProjectProgress", Mockito.any());
//        supplementaryAgreementService.updateProjectProgressStatus(IndContractsTypeEnum.TENDER_SUMMARY,33L);

        PowerMockito.doReturn(null).when(projectProgressService, "selectProjectProgress", Mockito.anyLong());
        PowerMockito.doNothing().when(projectProgressService, "saveProjectProgress", Mockito.any());
//        supplementaryAgreementService.updateProjectProgressStatus(IndContractsTypeEnum.AGREEMENT,33L);
    }
}
