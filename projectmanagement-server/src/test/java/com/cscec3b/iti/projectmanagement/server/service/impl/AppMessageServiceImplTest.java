//package com.cscec3b.iti.projectmanagement.server.service.impl;
//
//import cn.hutool.json.JSONArray;
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import com.cscec3b.iti.projectmanagement.api.dto.request.UcAppNotify;
//import com.cscec3b.iti.projectmanagement.api.dto.response.PmUserInfoResp;
//import com.cscec3b.iti.projectmanagement.server.service.IPmAuthService;
//import com.cscec3b.iti.usercenter.server.appcofig.UcApiProperties;
//import com.google.common.collect.Lists;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections4.CollectionUtils;
//import org.junit.Assert;
//import org.junit.Before;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.powermock.api.mockito.PowerMockito;
//import org.powermock.core.classloader.annotations.PrepareForTest;
//import org.powermock.modules.junit4.PowerMockRunner;
//import org.powermock.reflect.Whitebox;
//import org.springframework.data.redis.core.RedisCallback;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.data.redis.core.ValueOperations;
//import org.springframework.security.crypto.password.PasswordEncoder;
//
//import java.lang.reflect.InvocationTargetException;
//import java.lang.reflect.Method;
//import java.util.ArrayList;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Set;
//
//@Slf4j
//@RunWith(PowerMockRunner.class)
//@PrepareForTest({AppMessageServiceImpl.class, UcApiProperties.class, JSONUtil.class})
//public class AppMessageServiceImplTest {
//
//    @Mock
//    private PasswordEncoder passwordEncoder;
//
//    @Mock
//    private UcApiProperties ucApiProperties;
//
//    @Mock
//    private StringRedisTemplate stringRedisTemplate;
//
//    @Mock
//    private IPmAuthService pmAuthService;
//
//    @Mock
//    private ValueOperations valueOperations;
//
//    @InjectMocks
//    AppMessageServiceImpl appMessageService;
//
//    @Before
//    public void setUp() {
//        PowerMockito.mockStatic(JSONUtil.class);
//        PowerMockito.mockStatic(UcApiProperties.class);
//        PowerMockito.when(stringRedisTemplate.opsForValue()).thenReturn(valueOperations);
//        appMessageService = PowerMockito.spy(appMessageService);
//    }
//
//    @Test
//    public void receiveMessage() throws Exception {
//
//        UcAppNotify<String> reqMap = new UcAppNotify<>();
//        // 1. secret is blank;
//        Assert.assertFalse(appMessageService.receiveMessage(reqMap));
//
//        // 2. secret is error
//        reqMap.setSecret("333");
//        Assert.assertFalse(appMessageService.receiveMessage(reqMap));
//
//        // 3. containsKey(userUuid)
//        reqMap.setSecret("123");
//        PowerMockito.doReturn(Boolean.TRUE).when(passwordEncoder).matches(Mockito.any(), Mockito.any());
//        PowerMockito.doNothing().when(appMessageService, "handlerMessage", Mockito.any());
//        Assert.assertTrue(appMessageService.receiveMessage(reqMap));
//
//    }
//
//    @Test
//    public void handlerMessage() throws Exception {
//        Method method = AppMessageServiceImpl.class.getDeclaredMethod("handlerMessage", UcAppNotify.class);
//        method.setAccessible(true);
//        UcAppNotify reqMap = new UcAppNotify();
//        reqMap.setContent("{\"userUuid\":[\"1111\",\"2222\",\"33333\"]}");
//
//        // 1. typeEnum is null
//        Assert.assertNull(method.invoke(appMessageService, reqMap));
//
//        PowerMockito.doReturn(Lists.newArrayList("111")).when(appMessageService, "getUserUuidsByKey", Mockito.any()
//        , Mockito.any());
//        PowerMockito.doNothing().when(appMessageService, "updateCache", Mockito.any());
//
//        for (int i = 1; i <= 50; i++) {
//            reqMap.setType(i);
//            Assert.assertNull(method.invoke(appMessageService, reqMap));
//        }
//
//    }
//
//    @Test
//    public void updateCache() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
//        Method method = AppMessageServiceImpl.class.getDeclaredMethod("updateCache", List.class);
//        method.setAccessible(true);
//        List<String> uuids = new ArrayList<>();
//        Set<String> sets = new HashSet<>();
//
//
//        // 1.listPage is null
//        PowerMockito.doReturn(sets).when(stringRedisTemplate).execute((RedisCallback<Object>) Mockito.any());
//        Assert.assertNull(method.invoke(appMessageService, uuids));
//
//        // set is empty
//        uuids.add("111");
//        Assert.assertNull(method.invoke(appMessageService, uuids));
//
//        //  token is null
//        sets.add("uc:token:userinfo:AAA:111");
//        String refreshToken = null;
//        PowerMockito.doReturn(refreshToken).when(valueOperations).get(Mockito.any());
//        Assert.assertNull(method.invoke(appMessageService, uuids));
//
//        //  token is not null
//        refreshToken = "refreshToken";
//        PowerMockito.doReturn(refreshToken).when(valueOperations).get(Mockito.any());
//        PmUserInfoResp userInfoResp = new PmUserInfoResp();
//        PowerMockito.doReturn(userInfoResp).when(pmAuthService).getUserInfo(refreshToken);
//        Assert.assertNull(method.invoke(appMessageService, uuids));
//
//        //  expire == -1
//        Long expire = -1L;
//        PowerMockito.doReturn(expire).when(stringRedisTemplate).getExpire(Mockito.any());
//        Assert.assertNull(method.invoke(appMessageService, uuids));
//
//        //  expire != -1L
//        expire = 100L;
//        PowerMockito.doReturn(expire).when(stringRedisTemplate).getExpire(Mockito.any());
//        Assert.assertNull(method.invoke(appMessageService, uuids));
//    }
//
//    @Test
//    public void getUserUuidsByKey() throws Exception {
//        ArrayList<String> uuids = Lists.newArrayList("1111", "2222");
//        JSONObject jsonObject = new JSONObject();
//        JSONArray jsonArray = new JSONArray();
//        jsonArray.add(0, "1111");
//        //jsonArray.set(1,"2222");
//        jsonObject.set("uuids", uuids);
//
//        PowerMockito.doReturn(new JSONArray()).when(JSONUtil.class, "parseArray", Mockito.any());
//
//        //key is null
//        Assert.assertTrue(CollectionUtils.isEmpty(Whitebox.invokeMethod(appMessageService, "getUserUuidsByKey",
//        jsonObject, "uuidss")));
//
//        // key is not null;
//        Assert.assertTrue(CollectionUtils.isEmpty(Whitebox.invokeMethod(appMessageService, "getUserUuidsByKey",
//        jsonObject, "uuids")));
//
//    }
//
//
//}
