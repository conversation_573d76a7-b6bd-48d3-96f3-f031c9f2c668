<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.OMMapper">
    <sql id="InitProjectColumns">
        id,
        yunshu_execute_unit_abbreviation,
        yunshu_execute_unit_id,
        yunshu_execute_unit,
        yunshu_execute_unit_code,
        yunshu_execute_unit_id_path,
        project_name,
        project_finance_code,
        project_finance_name,
        project_finance_abbreviation,
        project_address,
        yunshu_org_id,
        project_type,
        marketing_business_segment,
        standard_type,
        cpm_project_key,
        is_create_head
    </sql>

    <insert id="initProject" useGeneratedKeys="true"
            keyProperty="id">
        insert into project(
        yunshu_execute_unit_abbreviation,
        yunshu_execute_unit_id,
        yunshu_execute_unit,
        yunshu_execute_unit_code,
        yunshu_execute_unit_id_path,
        project_name,
        project_finance_code,
        project_finance_name,
        project_finance_abbreviation,
        project_address,
        yunshu_org_id,
        project_type,
        marketing_business_segment,
        cpm_project_key,
        is_create_head,
        source_system,
        project_status)
        values (
        #{project.yunshuExecuteUnitAbbreviation},
        #{project.yunshuExecuteUnitId},
        #{project.yunshuExecuteUnit},
        #{project.yunshuExecuteUnitCode},
        #{project.yunshuExecuteUnitIdPath},
        #{project.projectName},
        #{project.projectFinanceCode},
        #{project.projectFinanceName},
        #{project.projectFinanceAbbreviation},
        #{project.projectAddress},
        #{project.yunshuOrgId},
        #{project.projectType},
        #{project.marketingBusinessSegment},
        #{project.cpmProjectKey},
        #{project.updateAt},
        1,
        1
        )
    </insert>

    <select id="getInitProjectById" resultType="com.cscec3b.iti.projectmanagement.api.dto.response.OMInitProjectResp">
        select
        <include refid="InitProjectColumns"/>
        FROM project
        WHERE ID = #{id}
    </select>

    <update id="updateInitProject">
        update project
        set
        yunshu_execute_unit_id = #{project.yunshuExecuteUnitId},
        yunshu_execute_unit = #{project.yunshuExecuteUnit},
        yunshu_execute_unit_code = #{project.yunshuExecuteUnitCode},
        yunshu_execute_unit_id_path = #{project.yunshuExecuteUnitIdPath},
        project_name = #{project.projectName},
        project_finance_code = #{project.projectFinanceCode},
        project_finance_name = #{project.projectFinanceName},
        project_finance_abbreviation = #{project.projectFinanceAbbreviation},
        project_address = #{project.projectAddress},
        yunshu_org_id = #{project.yunshuOrgId},
        project_type = #{project.projectType},
        marketing_business_segment = #{project.marketingBusinessSegment},
        cpm_project_key = #{project.cpmProjectKey},
        is_create_head = #{project.isCreateHead}
        where id = #{project.id}
    </update>

    <insert id="insertIgnoreProjectProgress">
        insert ignore into project_progress (project_id)
        values (#{projectId})
    </insert>

    <update id="batchAddProjectDeptIdPath">
        update project
        set project_dept_id_path =concat(execute_unit_id_path, execute_unit_id, '/')
        where project_status = 1
          and (project_dept_id is null or project_dept_id = '')
          and (project_dept_id_path is null or project_dept_id_path = '')
    </update>
</mapper>

