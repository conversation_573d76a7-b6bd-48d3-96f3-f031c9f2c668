<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.ProjectRevisionRecordMapper">
  <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.ProjectRevisionRecord">
    <!--@mbg.generated-->
    <!--@Table project_revision_record-->
    <id column="id" property="id"/>
    <result column="project_id" property="projectId"/>
    <result column="original_value" property="originalValue"/>
    <result column="revised_value" property="revisedValue"/>
    <result column="user_id" property="userId"/>
    <result column="username" property="username"/>
    <result column="create_at" property="createAt"/>
    <result column="remark" property="remark"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, project_id, original_value, revised_value, user_id, username, create_at, remark
  </sql>

  <select id="getRecodrPageList"
          resultType="com.cscec3b.iti.projectmanagement.api.dto.response.project.RevisionRecordResp">
    select
    pyrr.id, pyrr.project_id, pyrr.original_value, pyrr.revised_value, pyrr.user_id, pyrr.username, pyrr.create_at,
    pyrr.remark, p.project_finance_code
    from project_revision_record pyrr
    left join project p on pyrr.project_id = p.id
    <where>
      <if test="projectId != null">
        pyrr.project_id = #{projectId}
      </if>
      <if test="revisionType != null">
        and pyrr.revision_type = #{revisionType}
      </if>
      <if test="projectFinanceCode != null and projectFinanceCode != ''">
        and p.project_finance_code = #{projectFinanceCode}
      </if>
      <if test="projectFinanceName != null and projectFinanceName != ''">
        and p.project_finance_name like concat('%', #{projectFinanceName}, '%')
      </if>
      <if test="originalValue != null and originalValue != ''">
        and pyrr.original_value = #{originalValue}
      </if>
      <if test="revisedValue != null and revisedValue != ''">
        and pyrr.revised_value = #{revisedValue}
      </if>
      <if test="userId != null and userId != ''">
        and pyrr.user_id = #{userId}
      </if>
      <if test="username != null and username != ''">
        and pyrr.username like concat('%', #{username}, '%')
      </if>
      <if test="startTime != null">
        and pyrr.create_at &gt;= #{startTime}
      </if>
      <if test="endTime != null">
        and pyrr.create_at &lt;= #{endTime}
      </if>
      <if test="remark != null and remark != ''">
        and pyrr.remark = #{remark}
      </if>
    </where>
  </select>

  <insert id="insert" keyColumn="id" keyProperty="id"
          parameterType="com.cscec3b.iti.projectmanagement.server.entity.ProjectRevisionRecord" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into project_revision_record (project_id, revision_type, original_value, revised_value, user_id, username,
    create_at, remark)
    values (#{projectId}, #{revisionType}, #{originalValue}, #{revisedValue}, #{userId}, #{username},
    #{createAt}, #{remark})
  </insert>
</mapper>
