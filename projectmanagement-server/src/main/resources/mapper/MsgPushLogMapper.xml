<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.MsgPushLogMapper">
  <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.MsgPushLog">
    <!--@mbg.generated-->
    <!--@Table msg_push_log-->
    <id column="msg_id" property="msgId"/>
    <result column="production_time" property="productionTime"/>
    <result column="msg_content" property="msgContent"/>
    <result column="consumption_time" property="consumptionTime"/>
    <result column="retry_count" property="retryCount"/>
    <result column="consumption_result" property="consumptionResult"/>
    <result column="create_at" property="createAt"/>
    <result column="update_at" property="updateAt"/>
    <result column="remark" property="remark"/>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    msg_id, production_time, msg_content, consumption_time, retry_count, consumption_result,
    create_at, update_at, remark
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from msg_push_log
    where msg_id = #{msgId}
  </select>
  <insert id="insert" parameterType="com.cscec3b.iti.projectmanagement.server.entity.MsgPushLog">
    <!--@mbg.generated-->
    insert into msg_push_log (msg_id, production_time, msg_content, consumption_time, retry_count,
    consumption_result, create_at, update_at, remark)
    values (#{msgId}, #{productionTime}, #{msgContent}, #{consumptionTime}, #{retryCount},
    #{consumptionResult}, #{createAt}, #{updateAt}, #{remark})
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.cscec3b.iti.projectmanagement.server.entity.MsgPushLog">
    <!--@mbg.generated-->
    update msg_push_log
    <set>
      <if test="productionTime != null">
        production_time = #{productionTime},
      </if>
      <if test="msgContent != null">
        msg_content = #{msgContent},
      </if>
      <if test="consumptionTime != null">
        consumption_time = #{consumptionTime},
      </if>
      <if test="retryCount != null">
        retry_count = #{retryCount},
      </if>
      <if test="consumptionResult != null">
        consumption_result = #{consumptionResult},
      </if>
      <if test="createAt != null">
        create_at = #{createAt},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt},
      </if>
      <if test="remark != null and remark != ''">
        remark = #{remark},
      </if>
    </set>
    where msg_id = #{msgId}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.cscec3b.iti.projectmanagement.server.entity.MsgPushLog">
    <!--@mbg.generated-->
    update msg_push_log
    set production_time = #{productionTime},
    msg_content = #{msgContent},
    consumption_time = #{consumptionTime},
    retry_count = #{retryCount},
    consumption_result = #{consumptionResult},
    create_at = #{createAt},
    update_at = #{updateAt},
    remark = #{remark}
    where msg_id = #{msgId}
  </update>
</mapper>
