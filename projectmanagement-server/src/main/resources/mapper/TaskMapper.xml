<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.TaskMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.Task">
        <result column="id" property="id"/>
        <result column="relation_id" property="relationId"/>
        <result column="task_name" property="taskName"/>
        <result column="task_type" property="taskType"/>
        <result column="init_person" property="initPerson"/>
        <result column="handler_person" property="handlerPerson"/>
        <result column="init_time" property="initTime"/>
        <result column="finish_time" property="finishTime"/>
        <result column="status" property="status"/>
        <result column="init_person_page_param" property="initPersonPageParam"/>
        <result column="handler_person_page_param" property="handlerPersonPageParam"/>
        <result column="create_person" property="createPerson"/>
        <result column="update_person" property="updatePerson"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="delete_flag" property="deleteFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="BaseColumnList">
        id, relation_id, task_name, task_type, init_person, handler_person, init_time, finish_time, status,
        init_person_page_param, handler_person_page_param, create_person, update_person, create_time, update_time,
        delete_flag
    </sql>

    <!--创建待办任务-->
    <insert id="createTask" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO task(relation_id, task_name, task_type, init_person, handler_person, init_time, finish_time, status,
            init_person_page_param, handler_person_page_param, create_person, update_person, create_time, update_time)
        VALUES(#{relationId}, #{taskName}, #{taskType}, #{initPerson}, #{handlerPerson}, #{initTime}, #{finishTime}, #{status},
            #{initPersonPageParam}, #{handlerPersonPageParam}, #{createPerson}, #{updatePerson}, #{createTime}, #{updateTime})
    </insert>

    <!--更新任务状态-->
    <update id="updateStatus">
        update
            task
        set
            <if test="finishTime != null">
                finish_time = #{finishTime},
            </if>
            status = #{status},
            update_person = #{updatePerson},
            update_time = #{updateTime}
        where
            id = #{id}
    </update>

    <!--查询条件-->
    <sql id="pageListWhere">
        where
            t.delete_flag = '0'
        and
            t.handler_person = #{handlerPerson}
        <if test='statusType == @<EMAIL>'>
            and t.status in (0, 1) order by t.init_time desc
        </if>
        <if test='statusType == @<EMAIL>'>
            and t.status = 2 order by t.finish_time desc
        </if>
    </sql>

    <!--获取总条数-->
    <select id="getCount" resultType="long">
        select
            count(*)
        from
            task t
        <include refid="pageListWhere"/>
    </select>

    <!--分页查询-->
    <select id="pageList" resultMap="BaseResultMap">
        select
            <include refid="BaseColumnList"/>
        from
            task t
        <include refid="pageListWhere"/>
        limit
            #{start}, #{size}
    </select>

    <!--根据id查询任务数据-->
    <select id="getById" resultMap="BaseResultMap">
        select
            <include refid="BaseColumnList"/>
        from
            task t
        where
            t.delete_flag = '0'
        and
            t.id = #{id}
    </select>

    <!--根据项目id查询待办任务数据-->
    <select id="getTaskByProjectId" resultMap="BaseResultMap">
        select
        <include refid="BaseColumnList"/>
        from
        task t
        where
        t.delete_flag = '0'
        and
        t.relation_id = #{relationId}
    </select>
</mapper>
