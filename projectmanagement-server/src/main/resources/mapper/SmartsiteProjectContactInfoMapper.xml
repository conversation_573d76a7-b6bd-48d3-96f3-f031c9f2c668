<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.SmartsiteProjectContactInfoMapper">
  <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.SmartsiteProjectContactInfo">
    <!--@mbg.generated-->
    <!--@Table smartsite_project_contact_info-->
    <id column="id" property="id" />
    <result column="project_id" property="projectId" />
    <result column="customer_no" property="customerNo" />
    <result column="customer_name" property="customerName" />
    <result column="customer_company_property" property="customerCompanyProperty" />
    <result column="customer_level" property="customerLevel" />
    <result column="customer_parent_company" property="customerParentCompany" />
    <result column="customer_unified_social_credit_no" property="customerUnifiedSocialCreditNo" />
    <result column="owner_info" property="ownerInfo" />
    <result column="subpackage_info" property="subpackageInfo" />
    <result column="construction" property="construction" />
    <result column="supervise" property="supervise" />
    <result column="design" property="design" />
    <result column="deputy" property="deputy" />
    <result column="display_info" property="displayInfo" />
    <result column="created_at" property="createdAt" />
    <result column="updated_at" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, project_id, customer_no, customer_name, customer_company_property, customer_level, 
    customer_parent_company, customer_unified_social_credit_no, owner_info, subpackage_info, 
    construction, supervise, design, deputy, display_info, created_at, updated_at
  </sql>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.cscec3b.iti.projectmanagement.server.entity.SmartsiteProjectContactInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into smartsite_project_contact_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      project_id,
      customer_no,
      customer_name,
      customer_company_property,
      customer_level,
      customer_parent_company,
      customer_unified_social_credit_no,
      owner_info,
      subpackage_info,
      construction,
      supervise,
      design,
      deputy,
      display_info,
      created_at,
      updated_at,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      #{projectId},
      #{customerNo},
      #{customerName},
      #{customerCompanyProperty},
      #{customerLevel},
      #{customerParentCompany},
      #{customerUnifiedSocialCreditNo},
      #{ownerInfo},
      #{subpackageInfo},
      #{construction},
      #{supervise},
      #{design},
      #{deputy},
      #{displayInfo},
      #{createdAt},
      #{updatedAt},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      project_id = #{projectId},
      customer_no = #{customerNo},
      customer_name = #{customerName},
      customer_company_property = #{customerCompanyProperty},
      customer_level = #{customerLevel},
      customer_parent_company = #{customerParentCompany},
      customer_unified_social_credit_no = #{customerUnifiedSocialCreditNo},
      owner_info = #{ownerInfo},
      subpackage_info = #{subpackageInfo},
      construction = #{construction},
      supervise = #{supervise},
      design = #{design},
      deputy = #{deputy},
      display_info = #{displayInfo},
      created_at = #{createdAt},
      updated_at = #{updatedAt},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.cscec3b.iti.projectmanagement.server.entity.SmartsiteProjectContactInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into smartsite_project_contact_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectId != null">
        project_id,
      </if>
      <if test="customerNo != null and customerNo != ''">
        customer_no,
      </if>
      <if test="customerName != null and customerName != ''">
        customer_name,
      </if>
      <if test="customerCompanyProperty != null and customerCompanyProperty != ''">
        customer_company_property,
      </if>
      <if test="customerLevel != null and customerLevel != ''">
        customer_level,
      </if>
      <if test="customerParentCompany != null and customerParentCompany != ''">
        customer_parent_company,
      </if>
      <if test="customerUnifiedSocialCreditNo != null and customerUnifiedSocialCreditNo != ''">
        customer_unified_social_credit_no,
      </if>
      <if test="ownerInfo != null and ownerInfo != ''">
        owner_info,
      </if>
      <if test="subpackageInfo != null and subpackageInfo != ''">
        subpackage_info,
      </if>
      <if test="construction != null and construction != ''">
        construction,
      </if>
      <if test="supervise != null and supervise != ''">
        supervise,
      </if>
      <if test="design != null and design != ''">
        design,
      </if>
      <if test="deputy != null and deputy != ''">
        deputy,
      </if>
      <if test="displayInfo != null and displayInfo != ''">
        display_info,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="projectId != null">
        #{projectId},
      </if>
      <if test="customerNo != null and customerNo != ''">
        #{customerNo},
      </if>
      <if test="customerName != null and customerName != ''">
        #{customerName},
      </if>
      <if test="customerCompanyProperty != null and customerCompanyProperty != ''">
        #{customerCompanyProperty},
      </if>
      <if test="customerLevel != null and customerLevel != ''">
        #{customerLevel},
      </if>
      <if test="customerParentCompany != null and customerParentCompany != ''">
        #{customerParentCompany},
      </if>
      <if test="customerUnifiedSocialCreditNo != null and customerUnifiedSocialCreditNo != ''">
        #{customerUnifiedSocialCreditNo},
      </if>
      <if test="ownerInfo != null and ownerInfo != ''">
        #{ownerInfo},
      </if>
      <if test="subpackageInfo != null and subpackageInfo != ''">
        #{subpackageInfo},
      </if>
      <if test="construction != null and construction != ''">
        #{construction},
      </if>
      <if test="supervise != null and supervise != ''">
        #{supervise},
      </if>
      <if test="design != null and design != ''">
        #{design},
      </if>
      <if test="deputy != null and deputy != ''">
        #{deputy},
      </if>
      <if test="displayInfo != null and displayInfo != ''">
        #{displayInfo},
      </if>
      <if test="createdAt != null">
        #{createdAt},
      </if>
      <if test="updatedAt != null">
        #{updatedAt},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="projectId != null">
        project_id = #{projectId},
      </if>
      <if test="customerNo != null and customerNo != ''">
        customer_no = #{customerNo},
      </if>
      <if test="customerName != null and customerName != ''">
        customer_name = #{customerName},
      </if>
      <if test="customerCompanyProperty != null and customerCompanyProperty != ''">
        customer_company_property = #{customerCompanyProperty},
      </if>
      <if test="customerLevel != null and customerLevel != ''">
        customer_level = #{customerLevel},
      </if>
      <if test="customerParentCompany != null and customerParentCompany != ''">
        customer_parent_company = #{customerParentCompany},
      </if>
      <if test="customerUnifiedSocialCreditNo != null and customerUnifiedSocialCreditNo != ''">
        customer_unified_social_credit_no = #{customerUnifiedSocialCreditNo},
      </if>
      <if test="ownerInfo != null and ownerInfo != ''">
        owner_info = #{ownerInfo},
      </if>
      <if test="subpackageInfo != null and subpackageInfo != ''">
        subpackage_info = #{subpackageInfo},
      </if>
      <if test="construction != null and construction != ''">
        construction = #{construction},
      </if>
      <if test="supervise != null and supervise != ''">
        supervise = #{supervise},
      </if>
      <if test="design != null and design != ''">
        design = #{design},
      </if>
      <if test="deputy != null and deputy != ''">
        deputy = #{deputy},
      </if>
      <if test="displayInfo != null and displayInfo != ''">
        display_info = #{displayInfo},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt},
      </if>
    </trim>
  </insert>
</mapper>