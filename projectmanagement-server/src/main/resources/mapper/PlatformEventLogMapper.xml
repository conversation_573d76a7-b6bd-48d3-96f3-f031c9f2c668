<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.PlatformEventLogMapper">
  <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.PlatformEventLog">
    <!--@mbg.generated-->
    <!--@Table platform_event_log-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="msg_id" jdbcType="VARCHAR" property="msgId" />
    <result column="msg_type" jdbcType="VARCHAR" property="msgType" />
    <result column="msg_body" jdbcType="VARCHAR" property="msgBody" />
    <result column="decode_content" jdbcType="VARCHAR" property="decodeContent" />
    <result column="receive_time" jdbcType="BIGINT" property="receiveTime" />
    <result column="operation_record" jdbcType="VARCHAR" property="operationRecord" />
    <result column="response_result" jdbcType="VARCHAR" property="responseResult" />
    <result column="response_time" jdbcType="BIGINT" property="responseTime" />
    <result column="create_at" jdbcType="BIGINT" property="createAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, msg_id, msg_type, msg_body, decode_content, receive_time, operation_record, response_result, 
    response_time, create_at
  </sql>
</mapper>