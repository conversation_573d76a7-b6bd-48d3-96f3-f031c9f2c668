<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.HelpCenterMapper">
    <resultMap id="HelpCenterRespResultMap"
               type="com.cscec3b.iti.projectmanagement.api.dto.response.help.HelpCenterResp">
        <!--@mbg.generated-->
        <!--@Table help_center-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="publish_time" jdbcType="BIGINT" property="publishTime"/>
        <result column="update_at" jdbcType="BIGINT" property="updateAt"/>
        <collection property="attachments" column="{businessId = id, businessType = type}"
                    select="com.cscec3b.iti.projectmanagement.server.mapper.AttachmentMapper.getByBusinessIdAndType"/>
    </resultMap>

    <resultMap id="AttachmentResultMap" type="com.cscec3b.iti.projectmanagement.api.dto.dto.AttachmentDto">
        <!--@mbg.generated-->
        <!--@Table attachment-->
        <result column="original_name" jdbcType="VARCHAR" property="originalName"/>
        <result column="file_path" jdbcType="VARCHAR" property="filePath"/>
        <result column="file_size" jdbcType="INTEGER" property="fileSize"/>
        <result column="file_id" jdbcType="VARCHAR" property="fileId"/>
        <result column="file_md5" jdbcType="VARCHAR" property="fileMd5"/>
    </resultMap>

    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, title, `status`, publish_time, create_by, create_at, update_at, 2 as type
    </sql>

    <sql id="Attachment_Column_List">
        id,
        original_name,
        file_path,
        file_size,
        file_id,
        file_md5,
        create_at
    </sql>

    <select id="limit5" resultMap="HelpCenterRespResultMap">
        select
        <include refid="Base_Column_List">
        </include>
        from help_center
        where status = 1
        order by publish_time desc
        limit 5
    </select>


    <select id="listPage" resultMap="HelpCenterRespResultMap">
        select
        <include refid="Base_Column_List">
        </include>
        from help_center t
        <where>
            <if test="title != null and title != ''">
                and t.title like concat('%', #{title}, '%')
            </if>
            <if test="beginTime != null">
                and t.publish_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null">
                and t.publish_time &lt;= #{endTime}
            </if>
            <choose>
                <when test="status == null">
                    and (t.status = 1 or t.create_by = #{userId})
                </when>
                <when test="status == 1">
                    and t.status = 1
                </when>
                <when test="status == 0">
                    and t.status = 0 and t.create_by = #{userId}
                </when>
            </choose>
        </where>
        order by ifnull(publish_time, update_at) desc
    </select>

    <insert id="save" keyColumn="id" keyProperty="id"
            parameterType="com.cscec3b.iti.projectmanagement.server.entity.HelpCenter" useGeneratedKeys="true">
        <!--@mbg.generated-->
        insert into help_center (title, `status`, publish_time,
        create_by, create_at, update_at
        )
        values (#{title,jdbcType=VARCHAR}, #{status,jdbcType=TINYINT}, #{publishTime,jdbcType=BIGINT},
        #{createBy,jdbcType=VARCHAR}, #{createAt,jdbcType=BIGINT}, #{updateAt,jdbcType=BIGINT}
        )
    </insert>

    <delete id="delete" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete from help_center
        where id in
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="update" parameterType="com.cscec3b.iti.projectmanagement.server.entity.HelpCenter">
        <!--@mbg.generated-->
        update help_center
        set title = #{title},
        `status` = #{status},
        publish_time = #{publishTime},
        update_at = #{updateAt}
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="getByIdAndUserId" resultType="com.cscec3b.iti.projectmanagement.server.entity.HelpCenter">
        select
        <include refid="Base_Column_List">
        </include>
        from help_center
        where id = #{id} and create_by = #{userId}
    </select>

    <update id="updateById">
    </update>
</mapper>