<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.SmartsiteProjectInfoMapper">
  <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.SmartsiteProjectInfo">
    <!--@mbg.generated-->
    <!--@Table smartsite_project_info-->
    <id column="id" property="id" />
    <result column="project_apply_id" property="projectApplyId" />
    <result column="project_name" property="projectName" />
    <result column="standard_project_id" property="standardProjectId" />
    <result column="yunshu_org_id" property="yunshuOrgId" />
    <result column="yunshu_tree_id" property="yunshuTreeId" />
    <result column="yunshu_parent_org_id" property="yunshuParentOrgId" />
    <result column="yunshu_parent_org_name" property="yunshuParentOrgName" />
    <result column="department_id" property="departmentId" />
    <result column="modifier_id" property="modifierId" />
    <result column="modifier_name" property="modifierName" />
    <result column="approver_id" property="approverId" />
    <result column="approver_name" property="approverName" />
    <result column="status" property="status" />
    <result column="created_at" property="createdAt" />
    <result column="updated_at" property="updatedAt" />
    <result column="display_info" property="displayInfo" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, project_apply_id, project_name, standard_project_id, yunshu_org_id, yunshu_tree_id, 
    yunshu_parent_org_id, yunshu_parent_org_name, department_id, modifier_id, modifier_name, 
    approver_id, approver_name, `status`, created_at, updated_at, display_info
  </sql>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id" parameterType="com.cscec3b.iti.projectmanagement.server.entity.SmartsiteProjectInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into smartsite_project_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      project_apply_id,
      project_name,
      standard_project_id,
      yunshu_org_id,
      yunshu_tree_id,
      yunshu_parent_org_id,
      yunshu_parent_org_name,
      department_id,
      modifier_id,
      modifier_name,
      approver_id,
      approver_name,
      `status`,
      created_at,
      updated_at,
      display_info,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      #{projectApplyId},
      #{projectName},
      #{standardProjectId},
      #{yunshuOrgId},
      #{yunshuTreeId},
      #{yunshuParentOrgId},
      #{yunshuParentOrgName},
      #{departmentId},
      #{modifierId},
      #{modifierName},
      #{approverId},
      #{approverName},
      #{status},
      #{createdAt},
      #{updatedAt},
      #{displayInfo},
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      project_apply_id = #{projectApplyId},
      project_name = #{projectName},
      standard_project_id = #{standardProjectId},
      yunshu_org_id = #{yunshuOrgId},
      yunshu_tree_id = #{yunshuTreeId},
      yunshu_parent_org_id = #{yunshuParentOrgId},
      yunshu_parent_org_name = #{yunshuParentOrgName},
      department_id = #{departmentId},
      modifier_id = #{modifierId},
      modifier_name = #{modifierName},
      approver_id = #{approverId},
      approver_name = #{approverName},
      `status` = #{status},
      created_at = #{createdAt},
      updated_at = #{updatedAt},
      display_info = #{displayInfo},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id" parameterType="com.cscec3b.iti.projectmanagement.server.entity.SmartsiteProjectInfo" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into smartsite_project_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="projectApplyId != null">
        project_apply_id,
      </if>
      <if test="projectName != null and projectName != ''">
        project_name,
      </if>
      <if test="standardProjectId != null and standardProjectId != ''">
        standard_project_id,
      </if>
      <if test="yunshuOrgId != null and yunshuOrgId != ''">
        yunshu_org_id,
      </if>
      <if test="yunshuTreeId != null and yunshuTreeId != ''">
        yunshu_tree_id,
      </if>
      <if test="yunshuParentOrgId != null and yunshuParentOrgId != ''">
        yunshu_parent_org_id,
      </if>
      <if test="yunshuParentOrgName != null and yunshuParentOrgName != ''">
        yunshu_parent_org_name,
      </if>
      <if test="departmentId != null">
        department_id,
      </if>
      <if test="modifierId != null and modifierId != ''">
        modifier_id,
      </if>
      <if test="modifierName != null and modifierName != ''">
        modifier_name,
      </if>
      <if test="approverId != null and approverId != ''">
        approver_id,
      </if>
      <if test="approverName != null and approverName != ''">
        approver_name,
      </if>
      <if test="status != null">
        `status`,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="displayInfo != null and displayInfo != ''">
        display_info,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id},
      </if>
      <if test="projectApplyId != null">
        #{projectApplyId},
      </if>
      <if test="projectName != null and projectName != ''">
        #{projectName},
      </if>
      <if test="standardProjectId != null and standardProjectId != ''">
        #{standardProjectId},
      </if>
      <if test="yunshuOrgId != null and yunshuOrgId != ''">
        #{yunshuOrgId},
      </if>
      <if test="yunshuTreeId != null and yunshuTreeId != ''">
        #{yunshuTreeId},
      </if>
      <if test="yunshuParentOrgId != null and yunshuParentOrgId != ''">
        #{yunshuParentOrgId},
      </if>
      <if test="yunshuParentOrgName != null and yunshuParentOrgName != ''">
        #{yunshuParentOrgName},
      </if>
      <if test="departmentId != null">
        #{departmentId},
      </if>
      <if test="modifierId != null and modifierId != ''">
        #{modifierId},
      </if>
      <if test="modifierName != null and modifierName != ''">
        #{modifierName},
      </if>
      <if test="approverId != null and approverId != ''">
        #{approverId},
      </if>
      <if test="approverName != null and approverName != ''">
        #{approverName},
      </if>
      <if test="status != null">
        #{status},
      </if>
      <if test="createdAt != null">
        #{createdAt},
      </if>
      <if test="updatedAt != null">
        #{updatedAt},
      </if>
      <if test="displayInfo != null and displayInfo != ''">
        #{displayInfo},
      </if>
    </trim>
    on duplicate key update 
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id},
      </if>
      <if test="projectApplyId != null">
        project_apply_id = #{projectApplyId},
      </if>
      <if test="projectName != null and projectName != ''">
        project_name = #{projectName},
      </if>
      <if test="standardProjectId != null and standardProjectId != ''">
        standard_project_id = #{standardProjectId},
      </if>
      <if test="yunshuOrgId != null and yunshuOrgId != ''">
        yunshu_org_id = #{yunshuOrgId},
      </if>
      <if test="yunshuTreeId != null and yunshuTreeId != ''">
        yunshu_tree_id = #{yunshuTreeId},
      </if>
      <if test="yunshuParentOrgId != null and yunshuParentOrgId != ''">
        yunshu_parent_org_id = #{yunshuParentOrgId},
      </if>
      <if test="yunshuParentOrgName != null and yunshuParentOrgName != ''">
        yunshu_parent_org_name = #{yunshuParentOrgName},
      </if>
      <if test="departmentId != null">
        department_id = #{departmentId},
      </if>
      <if test="modifierId != null and modifierId != ''">
        modifier_id = #{modifierId},
      </if>
      <if test="modifierName != null and modifierName != ''">
        modifier_name = #{modifierName},
      </if>
      <if test="approverId != null and approverId != ''">
        approver_id = #{approverId},
      </if>
      <if test="approverName != null and approverName != ''">
        approver_name = #{approverName},
      </if>
      <if test="status != null">
        `status` = #{status},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt},
      </if>
      <if test="displayInfo != null and displayInfo != ''">
        display_info = #{displayInfo},
      </if>
    </trim>
  </insert>
</mapper>