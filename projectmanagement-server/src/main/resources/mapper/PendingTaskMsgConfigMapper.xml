<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.PendingTaskMsgConfigMapper">
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.TaskMsgConfig">
        <!--@mbg.generated-->
        <!--@Table task_msg_config-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="type_code" jdbcType="VARCHAR" property="typeCode"/>
        <result column="uc_msg_config_code" jdbcType="VARCHAR" property="ucMsgConfigCode"/>
        <result column="payload" jdbcType="VARCHAR" property="payload"/>
        <result column="app_link" jdbcType="VARCHAR" property="appLink"/>
        <result column="web_link" jdbcType="VARCHAR" property="webLink"/>
        <result column="billType" jdbcType="VARCHAR" property="billtype"/>
        <result column="bill_type_name" jdbcType="VARCHAR" property="billTypeName"/>
        <result column="retry_cycle" jdbcType="VARCHAR" property="retryCycle"/>
        <result column="deleted" jdbcType="TINYINT" property="deleted"/>
        <result column="create_at" jdbcType="BIGINT" property="createAt"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_at" jdbcType="BIGINT" property="updateAt"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, type_code, uc_msg_config_code, payload, app_link, web_link, billType,
        bill_type_name, retry_cycle, deleted, create_at, create_by, update_at, update_by
    </sql>

    <select id="match" resultMap="PendingTaskMsgConfigRespResultMap">
        select tcm.id,
               tcm.org_id,
               tcm.user_chose_type,
               tcm.target_users,
               tcm.create_at,
               tcm.create_by,
               tcm.update_at,
               tcm.update_by,
               tmc.type_code,
               tmc.uc_msg_config_code,
               tmc.payload,
               tmc.app_link,
               tmc.web_link,
               tmc.billType,
               tmc.bill_type_name,
               tmc.retry_cycle,
               yos.name         as yunshuExecuteUnit,
               yos.id_path      as yunshuExecuteUnitIdPath,
               yos.id_path_name as yunshuExecuteUnitIdPathName
        from task_msg_config_mapping tcm
                 inner join task_msg_config tmc on tcm.task_msg_config_id = tmc.id
                 inner join yunshu_org_sync yos on tcm.org_id = yos.dept_id
        where tcm.deleted = 0
          and tmc.deleted = 0
          and FIND_IN_SET(yos.id, (
        select REPLACE(id_path, '#', ',')
        from yunshu_org_sync
        where dept_id = #{orgId}
        ))
          and tmc.type_code = #{typeCode}
        order by length(yos.id_path) desc
        limit 1
    </select>

    <resultMap id="PendingTaskMsgConfigRespResultMap"
               type="com.cscec3b.iti.projectmanagement.api.dto.response.pendingtaskmsgconfig.PendingTaskMsgConfigResp">
        <id column="id" property="id"/>
        <result column="org_id" property="orgId"/>
        <result column="yunshuExecuteUnit" property="yunshuExecuteUnit"/>
        <result column="yunshuExecuteUnitIdPath" property="yunshuExecuteUnitIdPath"/>
        <result column="yunshuExecuteUnitIdPathName" property="yunshuExecuteUnitIdPathName"/>
        <result column="type_code" property="typeCode"/>
        <result column="uc_msg_config_code" property="ucMsgConfigCode"/>
        <result column="payload" property="payload"
                typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler"/>
        <result column="user_chose_type" property="userChoseType"/>
        <result column="target_users" property="targetUsers"
                typeHandler="com.cscec3b.iti.projectmanagement.server.handler.MsgNoticeTargetUserListTypeHandler"/>
        <result column="app_link" property="appLink"/>
        <result column="web_link" property="webLink"/>
        <result column="billType" property="billType"/>
        <result column="bill_type_name" property="billTypeName"/>
        <result column="retry_cycle" property="retryCycle"/>
        <result column="create_at" property="createAt"/>
        <result column="create_by" property="createBy"/>
        <result column="update_at" property="updateAt"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>
    <select id="pageList"
            resultMap="PendingTaskMsgConfigRespResultMap">
        select *
        from task_msg_config tcm
        where tcm.deleted = 0
        <if test="req.typeCode != null">
            and tcm.type_code = #{req.typeCode}
        </if>
        <if test="req.ucMsgConfigCode!= null">
            and tcm.uc_msg_config_code = #{req.ucMsgConfigCode}
        </if>
        order by tcm.update_at desc
    </select>
</mapper>
