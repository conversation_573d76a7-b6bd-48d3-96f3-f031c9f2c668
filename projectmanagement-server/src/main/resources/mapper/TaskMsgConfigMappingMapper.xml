<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cscec3b.iti.projectmanagement.server.mapper.TaskMsgConfigMappingMapper">
    <resultMap id="BaseResultMap" type="com.cscec3b.iti.projectmanagement.server.entity.TaskMsgConfigMapping">
        <!--@mbg.generated-->
        <!--@Table task_msg_config_mapping-->
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="task_msg_config_id" jdbcType="BIGINT" property="taskMsgConfigId"/>
        <result column="org_id" jdbcType="VARCHAR" property="orgId"/>
        <result column="user_chose_type" jdbcType="TINYINT" property="userChoseType"/>
        <result column="target_users" jdbcType="VARCHAR" property="targetUsers"/>
        <result column="retry_cycle" jdbcType="VARCHAR" property="retryCycle"/>
        <result column="deleted" jdbcType="BIGINT" property="deleted"/>
        <result column="create_at" jdbcType="BIGINT" property="createAt"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_at" jdbcType="BIGINT" property="updateAt"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        id, task_msg_config_id, org_id, user_chose_type, target_users, retry_cycle, deleted, create_at,
        create_by, update_at, update_by
    </sql>

    <select id="pageList"
            resultMap="com.cscec3b.iti.projectmanagement.server.mapper.PendingTaskMsgConfigMapper.PendingTaskMsgConfigRespResultMap">
        select tcm.id,tcm.org_id ,tcm.user_chose_type ,tcm.target_users, ifnull(tcm.retry_cycle, tmc.retry_cycle) as
        retry_cycle,
        tcm.create_at ,tcm.create_by ,tcm.update_at ,tcm.update_by,
        tmc.type_code ,tmc.uc_msg_config_code ,tmc.payload ,tmc.app_link ,tmc.web_link ,tmc.billType ,tmc.bill_type_name
        ,tmc.retry_cycle ,
        yos.name as yunshuExecuteUnit, yos.id_path as yunshuExecuteUnitIdPath, yos.id_path_name as
        yunshuExecuteUnitIdPathName
        from task_msg_config_mapping tcm
        inner join task_msg_config tmc on tcm.task_msg_config_id = tmc.id
        inner join yunshu_org_sync yos on tcm.org_id = yos.dept_id
        where tcm.deleted = 0 and tmc.deleted = 0
        and yos.id_path like concat(#{req.yunshuExecuteUnitIdPath}, '%')
        <if test="req.typeCode != null">
            and tmc.type_code = #{req.typeCode}
        </if>
        order by tcm.update_at desc
    </select>
</mapper>