#用户、组织相关
ExceptionStatusEnum.status.8010100=The operation failed:[{0}]
ExceptionStatusEnum.status.8010101=Handling user authentication information exceptions
ExceptionStatusEnum.status.8010102=Not matched to user-associated lean build organization
ExceptionStatusEnum.status.8010103=The target organization was not matched, please refresh the page and try again.
ExceptionStatusEnum.status.8010104=Standard organization information is not matched, please contact the administrator to process
ExceptionStatusEnum.status.8010105=Failed to obtain the cloud pivot entity
ExceptionStatusEnum.status.8010004={0} cannot be empty
ExceptionStatusEnum.status.8010005={0} failed to save
ExceptionStatusEnum.status.8010006=the project already exists, please do not repeat the project
ExceptionStatusEnum.status.8010007=if the project is empty or multiple projects using the same independent file exist, the attached file cannot be recorded. Contact the system administrator
ExceptionStatusEnum.status.8010008={0} update failed
ExceptionStatusEnum.status.8010009=The standalone contract type gets an exception
ExceptionStatusEnum.status.8010010=Bid summary storage failure
ExceptionStatusEnum.status.8010110=The pre-file associated with the contract does not exist. Please check
ExceptionStatusEnum.status.8010011=The project already exists, please do not duplicate the project
ExceptionStatusEnum.status.8010012=Project information failed to be stored
ExceptionStatusEnum.status.8010013=Project information failed to be stored
ExceptionStatusEnum.status.8010014=The final contract information entered failed
ExceptionStatusEnum.status.8010015=The final contract failed to update project information
ExceptionStatusEnum.status.8010016=The final contract has been entered, please do not enter it repeatedly
ExceptionStatusEnum.status.8010017=Bureau Contract has been entered. Do not repeat the entry
ExceptionStatusEnum.status.8010018=Bureau Supplementary Agreement has been recorded. Do not repeat the entry
ExceptionStatusEnum.status.8010019=The independent contract ID(associatedId) of the file must be the same as the independent file ID(originFileId)

ExceptionStatusEnum.status.8010021=Unable to enter the attached supplementary agreement, \
                                   the project is empty or multiple projects using the same independent file exist
ExceptionStatusEnum.status.8010022=The supplementary agreement failed to update the project information
ExceptionStatusEnum.status.8010023=The project is empty or multiple projects using the same independent file exist
ExceptionStatusEnum.status.8010024=The financial quotient system failed to update the project information
ExceptionStatusEnum.status.8010025=The supplementary agreement has been entered. Do not enter it again
ExceptionStatusEnum.status.8010026=The supplementary agreement fails to be imported into the database
ExceptionStatusEnum.status.8010027=Project information is incomplete
ExceptionStatusEnum.status.8010028=The first contract of the project has been entered, please do not enter it repeatedly
ExceptionStatusEnum.status.8010029=Details of the supplementary agreement are not available

ExceptionStatusEnum.status.8010031=cpm to finance push data exception



ExceptionStatusEnum.status.8010081=The queried {0} does not exist
ExceptionStatusEnum.status.8010032=requestId lost efficacy
ExceptionStatusEnum.status.8010033=projectId disagree
ExceptionStatusEnum.status.8010034=the project has been approved and completed, please do not submit it repeatedly
ExceptionStatusEnum.status.8010035=the task has been handled, please do not repeat submission
ExceptionStatusEnum.status.8010036=The bid summary has been entered, please do not enter it repeatedly
ExceptionStatusEnum.status.8010037=The ID of the source file cannot be empty
ExceptionStatusEnum.status.8010038=The bid summary failed to update the total project amount
ExceptionStatusEnum.status.8010039=Failed to open a bid record

ExceptionStatusEnum.status.8010041=The organization type is incorrect,it should be the command type
ExceptionStatusEnum.status.8010042=Failed to update information for the headquarters of the organization
ExceptionStatusEnum.status.8010043=Failed to update command information for the project
ExceptionStatusEnum.status.8010044=Failed to query command details
ExceptionStatusEnum.status.8010045=Failed to create the command. The command code and name already exist
ExceptionStatusEnum.status.8010046=Failed to query project department details
ExceptionStatusEnum.status.8010051=a8ProjectCode、projectFinanceCode、YunshuOrgId one and only one field has a value
ExceptionStatusEnum.status.8010052=the standard organization does not exist
ExceptionStatusEnum.status.8010053=there is no unit directly in charge of the project team
ExceptionStatusEnum.status.8010055=The to-do information for the project is empty
ExceptionStatusEnum.status.8010056=Winning unestablished project hookup id and project center project id cannot be empty
ExceptionStatusEnum.status.8010057=Non-independent mount file type error
ExceptionStatusEnum.status.8010061=There are no projects in the CPM. Please contact the CPM for handling with the assistance of the information officer of your unit.
ExceptionStatusEnum.status.8010062=There are multiple projects in the project center,Please contact the CPM for handling with the assistance of the information officer of your unit.;
ExceptionStatusEnum.status.8010063=The current project has not been initiated in the Financial Quotient System. Please go to the Financial Quotient System to complete the project initiation and fill out the basic information form.
ExceptionStatusEnum.status.8010064=The financial quotient code of the current project is suspected to have been removed,Please contact the CPM for handling with the assistance of the information officer of your unit;
ExceptionStatusEnum.status.8010065=The project lacks a financial quotient code. Please provide the financial quotient code and contact the project center for supplementary entry with the assistance of the information officer in your unit.
ExceptionStatusEnum.status.8010201=Failed to save the project progress procedure
ExceptionStatusEnum.status.8010202=Failed to update the project progress
ExceptionStatusEnum.status.8010203=The independent file ID cannot be the same as the mount ID
ExceptionStatusEnum.status.8010204=The belongId cannot be null
ExceptionStatusEnum.status.8010205=The project progress is null
ExceptionStatusEnum.status.8010206=The organization name of the UC response is empty
ExceptionStatusEnum.status.8010207=The independent file type does not exist
ExceptionStatusEnum.status.8010301=Bucket does not exist, please check;
ExceptionStatusEnum.status.8010302=The file is empty
ExceptionStatusEnum.status.8010303=File read exception
ExceptionStatusEnum.status.8010304=The OSS upload or download task does not exist or has already completed
ExceptionStatusEnum.status.8010305=Bucket does not exist, please check;

ExceptionStatusEnum.status.8010320=Data does not exist, please refresh and try again;
ExceptionStatusEnum.status.8010401=Failed to insert notice;
ExceptionStatusEnum.status.8010402=Failed to publish the notice;
ExceptionStatusEnum.status.8010403=The notification is null and the notice id {0} is invalid;
ExceptionStatusEnum.status.8010404=Failed to delete the attachments of notification {0};
ExceptionStatusEnum.status.8010405=Failed to delete the notification {0};
ExceptionStatusEnum.status.8010406=Failed to update the notice;
ExceptionStatusEnum.status.8010501=There is no special project details with id {0};
ExceptionStatusEnum.status.8010502=There is no special project with id {0};
ExceptionStatusEnum.status.8010503=Failed to update the special project whose id is {0};
ExceptionStatusEnum.status.8010601=Name already exists, please check
ExceptionStatusEnum.status.8010602=Created failed, please check configuration items;
ExceptionStatusEnum.status.8010603=Updated failed, please check configuration items;
ExceptionStatusEnum.status.8010604=Subscription system configuration for which the consumption object id {0} is not queried;
ExceptionStatusEnum.status.8010605=The event does not exist, please check;
ExceptionStatusEnum.status.8010606=the event consumption object ID is configured abnormally, contact the administrator to check;
ExceptionStatusEnum.status.8010607=The event configuration format is abnormal, please contact the administrator to check;
ExceptionStatusEnum.status.80106010=The interface request protocol did not match the push url;
ExceptionStatusEnum.status.80106011=The system has been registered, please do not repeat registration;
ExceptionStatusEnum.status.80106015=Failed to obtain standard organization to cloud pivot organization mapping relationship via remote service
ExceptionStatusEnum.status.80106016=429 Too Many Requests
ExceptionStatusEnum.status.80106050=There are also subcontracted projects under the current bureau nominal total contract projects, which are not allowed to be changed to non-bureau nominal projects;;
ExceptionStatusEnum.status.80106051=Item status error, please check;
ExceptionStatusEnum.status.80106052=Match to more than one item, please revise before proceeding ;

ExceptionStatusEnum.status.80107000=Failed to get yunshu organization
ExceptionStatusEnum.status.80107004=organization switching failure;

ExceptionStatusEnum.status.80107001=Failed to get project information data;
ExceptionStatusEnum.status.80107002=Project logo(cpmProjectKey) already exists and no changes are allowed
ExceptionStatusEnum.status.80107003=The project key (cpmProjectKey) already exists and is not allowed to be added repeatedly.
ExceptionStatusEnum.status.80107005=The service system does not exist. Contact the administrator to register it.
ExceptionStatusEnum.status.80107006=The business system does not have the information of the subscription {0} and cannot receive the push, please contact the administrator for configuration.
ExceptionStatusEnum.status.80107007=The current project does not meet the push conditions and cannot be pushed;
ExceptionStatusEnum.status.80107008=The current project has a cloud pivot id, can not be added, please make a revision;
ExceptionStatusEnum.status.80107009=Duplicate configuration items, please check;
ExceptionStatusEnum.status.80108001=The process instance does not exist. Please check
ExceptionStatusEnum.status.80108002=The process instance is abnormal. Refresh and try again
ExceptionStatusEnum.status.80108003=The associated form information is abnormal. Check it；
ExceptionStatusEnum.status.80108004=The main data field information has changed, and the current approval process is automatically discarded
ExceptionStatusEnum.status.80108005=Duplicate information, please check
ExceptionStatusEnum.status.80108006=Child nodes exist. Check
ExceptionStatusEnum.status.80108007=Independent judgment is incomplete or non-independent project, and the project type cannot be carried out
ExceptionStatusEnum.status.80108008=The approval status does not meet the requirements. Please check
ExceptionStatusEnum.status.80108009=The flow template id cannot be empty
ExceptionStatusEnum.status.80108010=The result of independence judgment cannot be empty
ExceptionStatusEnum.status.80108011=The process instance id cannot be empty
ExceptionStatusEnum.status.80108012=The project type information is incomplete, please check
ExceptionStatusEnum.status.80108013=The independence judgment result cannot be empty. Please check
ExceptionStatusEnum.status.80108014=The current data is not received
ExceptionStatusEnum.status.80108015=The linked file has been judged to be independent, so please check