CREATE TABLE `sys_api_log`
(
    `id`              bigint(11) NOT NULL AUTO_INCREMENT,
    `request_url`     varchar(255) DEFAULT NULL COMMENT '接口请求全路径url',
    `api_name`        varchar(255) DEFAULT NULL COMMENT '接口controller层名称',
    `request_method`  varchar(10)  DEFAULT NULL COMMENT 'http请求方式',
    `request_param`   longtext COMMENT '接口请求参数',
    `response_json`   longtext COMMENT '接口响应报文',
    `response_status` bigint(20) DEFAULT '0' COMMENT '请求结果 1:成功; 2:失败',
    `error_msg`       longtext COMMENT '接口错误报文',
    `operator_type`   int(11) DEFAULT '0' COMMENT '操作类型（0其它 1新增 2更新）',
    `request_time`    bigint(20) DEFAULT NULL COMMENT '请求接口时间',
    `create_at`       bigint(20) DEFAULT NULL COMMENT '创建时间',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1211 DEFAULT CHARSET=utf8mb4 COMMENT='接口日志记录表';