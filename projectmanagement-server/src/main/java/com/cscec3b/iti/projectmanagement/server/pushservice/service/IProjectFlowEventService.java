package com.cscec3b.iti.projectmanagement.server.pushservice.service;

import com.cscec3b.iti.model.resp.ProjectArchiveResp;
import com.cscec3b.iti.model.resp.open.ProjectAssociationStatusResp;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventPushRecord;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectFlowEventRecord;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ProjectFlowEventSubscribeDto;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;

import java.util.List;

/**
 * 项目生命周期流程服务
 *
 * <AUTHOR>
 * @date 2023/09/15 17:38
 **/

public interface IProjectFlowEventService {

	void handler(Long projectId, FlowNodeEnum nodeEnum, FlowNodeHandlerEnum handlerEnum, String msgId,
				 List<ProjectFlowEventSubscribeDto> subscribers);

	void handlerV2(Long projectId, FlowNodeEnum nodeEnum, FlowNodeHandlerEnum handlerEnum, String msgId,
			List<ProjectFlowEventSubscribeDto> subscribers);

	void handlerV3(Long projectId, FlowNodeEnum nodeEnum, FlowNodeHandlerEnum handlerEnum, String msgId,
			List<ProjectFlowEventSubscribeDto> subscribers);

	ProjectEventPushRecord eventPush(String msgId, ProjectFlowEventSubscribeDto subscriber, Project project,
			ProjectEventPushRecord pushRecord);

	/**
     * 获取事件触发时的项目信息
     *
     * @param projectId 项目id
     * @return {@link ProjectArchiveResp }
     * <AUTHOR>
     * @date 2023/09/16
     */
	ProjectArchiveResp getProjectArchive(Long projectId);

	/**
	 * 保存事件信息 息
	 *
	 * @param flowEventRecord record
	 * @return
	 * <AUTHOR>
	 * @date 2023/09/16
	 */
	Integer saveFlowEventRecord(ProjectFlowEventRecord flowEventRecord);


    /**
     * 通过id获取业务系统的项目关联状态
     *
     * @param id 项目id
     * @return {@link List }<{@link ProjectAssociationStatusResp }>
     */
    List<ProjectAssociationStatusResp> associationStatus(Long id);

    /**
     * 通过项目标识获取业务系统的项目关联状态
     *
     * @param key 项目标识
     * @return {@link List }<{@link ProjectAssociationStatusResp }>
     */
    List<ProjectAssociationStatusResp> associationStatusByKey(String key);

	/**
	 * 通过项目标识和子系统id获取业务系统的项目关联状态
	 *
	 * @param cpmProjectKey 项目标识
	 * @param subId         子系统id
	 * @return {@link ProjectAssociationStatusResp }
	 */
	List<ProjectAssociationStatusResp> getProjectAssociationByCpmProjectKeyAndSubId(String cpmProjectKey, Long subId);
}
