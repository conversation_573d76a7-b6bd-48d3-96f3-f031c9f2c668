package com.cscec3b.iti.projectmanagement.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.api.dto.request.engineeringproject.CompanyViewEngineerProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.engineeringproject.EngineerProjectPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenEngineerProjectPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenEngineeringArchiveReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenEngineeringMappingArchiveReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.CompanyViewEngineeringProjectResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.CompanyViewEngineeringProjectTreeResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.EngineerProjectPageResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.MappingStandardProjectResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineerProjectTreeResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineeringArchiveResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineeringProjectMappingResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenEngineeringProjectPageResp;
import com.cscec3b.iti.projectmanagement.server.entity.EngineeringProject;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EngineeringProjectMapper extends BaseMapper<EngineeringProject> {

    /**
     * 获取工程项目列表
     *
     * @param engineerProjectPageReq 请求参数
     * @return {@link List }<{@link EngineerProjectPageResp }>
     */
    List<EngineerProjectPageResp> getList(@Param("req") EngineerProjectPageReq engineerProjectPageReq);

    /**
     * 获取工程项目列表
     *
     * @param engineerProjectPageReq 请求参数
     * @return {@link List }<{@link EngineerProjectPageResp }>
     */
    List<EngineerProjectPageResp> getAllList(@Param("req") EngineerProjectPageReq engineerProjectPageReq);

    /**
     * 获取工程项目列表
     * 
     * @param engineeringProjectId 工程项目id
     * @return {@link MappingStandardProjectResp }
     */
    MappingStandardProjectResp getStandardProjectByEngineeringId(Long engineeringProjectId);

    /**
     * 获取所有子节点id
     * 
     * @param currentEngineeringProjectId 当前节点id
     * @return {@link List }<{@link Long }>
     */
    List<String> getAllDescendantIds(@Param("currentId") Long currentEngineeringProjectId);

    /**
     * 获取所有父节点id
     * 
     * @param currentEngineeringProjectId 当前节点id
     * @return {@link List }<{@link Long }>
     */
    List<Long> getAllAncestorIds(@Param("currentId") Long currentEngineeringProjectId);

    /**
     * 获取当前工程项目所在全量树的所有ID（包括上级和下级）
     *
     * @param engineerKey 工程key
     * @return {@link List }<{@link Long }>
     */
    List<Long> getFullTreeIds(@Param("engineerKey") String engineerKey);

    List<EngineerProjectPageResp> getHookList(@Param("hookLevel") Integer hookLevel,
        @Param("searchKeyWord") String searchKeyWord, @Param("excludeIds") List<Long> excludeIds);

    List<OpenEngineeringProjectPageResp> getOpenEngineeringProjectPage(@Param("effectiveIdPath") String effectiveIdPath,
        @Param("req") OpenEngineerProjectPageReq pageReq);

    /**
     * 获取树列表
     *
     * @param fullTreeIds 完整树 ID
     * @return {@link List }<{@link OpenEngineerProjectTreeResp }>
     */
    List<OpenEngineerProjectTreeResp> getTreeList(List<Long> fullTreeIds);

    /**
     * 获取公司视图列表
     *
     * @param req 要求
     * @return {@link List }<{@link CompanyViewEngineeringProjectResp }>
     */
    List<CompanyViewEngineeringProjectResp> getCompanyViewList(@Param("req") CompanyViewEngineerProjectReq req);

    /**
     * 获取公司视图工程项目树
     *
     * @param engineerId 工程项目 ID
     * @return {@link CompanyViewEngineeringProjectResp }
     */
    List<CompanyViewEngineeringProjectTreeResp> getCompanyViewEngineeringProjectTree(Long engineerId);

    /**
     * @param dataIdPath 数据id路径
     * @param pageReq    分页请求
     * @return {@link List }<{@link OpenEngineeringArchiveResp }>
     */
    List<OpenEngineeringArchiveResp> getEngineeringArchivedPage(@Param("dataIdPath") String dataIdPath, @Param("req") OpenEngineeringArchiveReq pageReq);

    List<OpenEngineeringProjectMappingResp> getEnginAndStandardProjectMappingInfo(@Param("dataIdPath") String dataIdPath, @Param("req") OpenEngineeringMappingArchiveReq req);
}