package com.cscec3b.iti.projectmanagement.server.controller;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.ITaskMsgConfigMappingApi;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.TaskMsgConfigMappingReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.TaskMsgConfigMappingUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.pendingtaskmsgconfig.PendingTaskMsgConfigResp;
import com.cscec3b.iti.projectmanagement.server.service.ITaskMsgConfigMappingService;

import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;

@Validated
@RestController
@RequestMapping(ITaskMsgConfigMappingApi.PATH)
@RequiredArgsConstructor
@Api(tags = "待办任务配置与人员关联")
public class TaskMsgConfigMappingController implements ITaskMsgConfigMappingApi {

    private final ITaskMsgConfigMappingService taskMsgConfigMappingService;

    @Override
    public GenericityResponse<Boolean> create(TaskMsgConfigMappingReq saveReq) {
        return ResponseBuilder.fromData(taskMsgConfigMappingService.create(saveReq));
    }

    @Override
    public GenericityResponse<Boolean> update(TaskMsgConfigMappingUpdateReq updateReq) {
        return ResponseBuilder.fromData(taskMsgConfigMappingService.updateMapping(updateReq));
    }

    @Override
    public GenericityResponse<Page<PendingTaskMsgConfigResp>> page(TaskMsgConfigMappingReq pageReq) {
        return ResponseBuilder.fromData(taskMsgConfigMappingService.pageList(pageReq));
    }

    @Override
    public GenericityResponse<Boolean> delete(Long id) {
        return ResponseBuilder.fromData(taskMsgConfigMappingService.removeById(id));
    }
}
