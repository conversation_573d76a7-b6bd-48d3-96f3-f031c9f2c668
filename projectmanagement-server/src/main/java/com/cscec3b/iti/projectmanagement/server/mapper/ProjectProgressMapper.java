package com.cscec3b.iti.projectmanagement.server.mapper;

import com.cscec3b.iti.projectmanagement.api.dto.request.warn.ProjectMonitorQueryParams;
import com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.MainDataPushInfoResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.warn.AgencyNoProjectResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.warn.ProjectMonitorResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.warn.ProjectNoAgencyResp;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectProgress;
import com.cscec3b.iti.projectmanagement.server.enums.ProjectProgressEnum;
import com.cscec3b.iti.projectmanagement.server.scheduled.dto.WarnInfoDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

@Mapper
public interface ProjectProgressMapper {

    List<AgencyNoProjectResp> agencyPageList(@Param("id") String id);

    List<AgencyNoProjectResp> getEarlyWarningById(@Param("idSet") Set independIdSet);

    List<ProjectNoAgencyResp> projectPageList(@Param("id") String id);

    List<WarnInfoDto> qryWarnInfos();

    int batchUpdateById(@Param("list") List<Long> resEntityList, @Param("status") Integer status);

    int insert(@Param("vo") ProjectProgress projectProgress);

    ProjectProgress select(Long projectId);

    int update(@Param("vo") ProjectProgress projectProgress);

    List<ProjectMonitorResp> pageProjectMonitorList(@Param("pm") ProjectMonitorQueryParams params);

    /**
     * 智慧工地项目进度信息
     *
     * @param projectId      项目id
     * @param smartQueryTime 发起智慧工地立项时间
     * @param toUcTime       立项完成时间
     * @param progressEnum   立项状态 1 进行中， 2已完成
     * @param smartRemarks   智慧工地备注
     * @return int
     * <AUTHOR>
     * @date 2023/08/02 10:40
     */
    int smartsiteProjectApprovalUpdate(@Param("id") Long projectId, @Param("smartQueryTime") Long smartQueryTime,
            @Param("toUcTime") Long toUcTime, @Param("progressEnum") ProjectProgressEnum progressEnum,
            @Param("smartRemarks") String smartRemarks);

    /**
     * 财商项目进度信息<br>
     * 财商立项完成时需要解析签约未立项的预警状态<br>
     * 财商修订时，如果 财商已立项完成，则不需要修改财商立项进度状态
     *
     * @param projectId         项目id
     * @param toFinanceTime     发起财商立项时间
     * @param approveFinishTime 财商立项完成
     * @param progressEnum      财商立项状态 0：未开始; 1 进行中；2 已完成
     * @param financeRemarks    财商立项备注
     * @return int
     * <AUTHOR>
     * @date 2023/08/02 14:39
     */
    int financeProjectApprovalUpdate(@Param("id") Long projectId, @Param("toFinanceTime") Long toFinanceTime,
            @Param("approveFinishTime") Long approveFinishTime, @Param("progressEnum") ProjectProgressEnum progressEnum,
            @Param("financeRemarks") String financeRemarks);

    /**
     * UC项目部进度信息
     *
     * @param projectId    项目id
     * @param toUcTime     发起项目部创建时间
     * @param progressEnum UC创建项目状态： 0未开始 1:创建中; 2:完成
     * @param ucRemarks    uc立项备注
     * @return int
     * <AUTHOR>
     * @date 2023/08/02 14:46
     */
    int ucProjectApprovalUpdate(@Param("id") Long projectId, @Param("toUcTime") Long toUcTime,
            @Param("progressEnum") ProjectProgressEnum progressEnum, @Param("ucRemarks") String ucRemarks);

    /**
     * 更新签约状态
     *
     * @param projectId    项目中心项目id
     * @param signTime     签约时间
     * @param progressEnum 签约状态 0:未签约；2：已签约
     * @return int
     * <AUTHOR>
     * @date 2023/08/02 15:04
     */
    int updateSignStatus(@Param("id") Long projectId, @Param("signTime") Long signTime,
            @Param("progressEnum") ProjectProgressEnum progressEnum);

    /**
     * 更新项目预警状态
     *
     * @param projectId    项目id
     * @param progressEnum 预警状态 态 0:未预警； 1: 预警中,2:已解除
     * @return int
     * <AUTHOR>
     * @date 2023/08/02 15:06
     */
    int updateWarnStatus(@Param("id") Long projectId, @Param("progressEnum") ProjectProgressEnum progressEnum);

    /**
     * 更新项目中心备注信息
     *
     * @param projectId  项目id
     * @param cpmRemarks 项目中心备注
     * @return int
     * <AUTHOR>
     * @date 2023/08/02 15:37
     */
    int updateCpmRemarks(@Param("id") Long projectId, @Param("cpmRemarks") String cpmRemarks);

    /**
     * 指挥部进度信息
     *
     * @param projectId              项目id
     * @param progressEnum           指挥部创建状态 0未开始，1进行中，2已完成
     * @param createCommandStartTime 指挥部创建开始时间
     * @param createCommandEndTime   指挥部创建结束时间
     * @return int
     * <AUTHOR>
     * @date 2023/08/02 16:24
     */
    int createCommandInit(@Param("projectId") Long projectId, @Param("progressEnum") ProjectProgressEnum progressEnum,
            @Param("createCommandStartTime") Long createCommandStartTime,
            @Param("createCommandEndTime") Long createCommandEndTime);

    int exists(@Param("projectId") Long projectId);

    List<AgencyNoProjectResp> agencyPageCloudPivot(@Param("treeId") String id);

    List<ProjectNoAgencyResp> projectPageCloudPivot(@Param("treeId") String id);

    List<ProjectMonitorResp> pageProjectMonitorCloudPivot(@Param("pm") ProjectMonitorQueryParams params);


    MainDataPushInfoResp mainDataInfo(@Param("id") Long projectId);
}
