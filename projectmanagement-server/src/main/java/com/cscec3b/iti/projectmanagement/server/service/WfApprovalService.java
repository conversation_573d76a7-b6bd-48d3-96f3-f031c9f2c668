package com.cscec3b.iti.projectmanagement.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.workflow.WfApprovalPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.WfApprovalTodoPageResp;
import com.cscec3b.iti.projectmanagement.server.entity.WfApproval;

import java.util.List;

public interface WfApprovalService extends IService<WfApproval> {


    /**
     * 获取审批列表
     *
     * @param pageListReq 查询参数
     * @return {@link Page}<{@link WfApprovalTodoPageResp}>
     */
    Page<WfApprovalTodoPageResp> getApprovalPageList(WfApprovalPageReq pageListReq);


    /**
     * 简易审批不通过
     *
     * @param procInstId
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    Boolean unPass(String procInstId);

    /**
     * 简易审批通过
     * <br> 需要更新主数据 ，更新前比对主数据对应字段的值，及审批表单里记录的发起审批时的字段值，不一致，则抛出字段已被更新的异常
     *
     * @param procInstId
     * @return {@link Boolean}
     */
    Boolean pass(String procInstId);

    /**
     * 获取进行中的流程
     *
     * @param businessDataType 业务板块
     * @param fieldId          字段名
     * @param cpmProjectId     项目中心项目id
     * @return {@link WfApproval}
     */
    List<WfApproval> getInProcessApproval(String businessDataType, String fieldId, Long cpmProjectId);

    /**
     * 获取进行中的流程
     *
     * @param scopeType 业务板块
     * @param belongId  表单id
     * @return {@link WfApproval}
     */
    WfApproval getInProcessApproval(String scopeType, String belongId);

}
