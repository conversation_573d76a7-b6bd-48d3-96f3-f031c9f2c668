package com.cscec3b.iti.projectmanagement.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.api.dto.request.ContractFilePageReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.ContractFilePageResp;
import com.cscec3b.iti.projectmanagement.server.entity.SecrecyProjectInfo;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SecrecyProjectInfoMapper extends BaseMapper<SecrecyProjectInfo> {
    /**
     * @param pageReq       分页参数
     * @param scopeTypeEnum 文件类型
     * @return List<ContractFIlePageResp>
     */
    List<ContractFilePageResp> pageList(@Param("req") ContractFilePageReq pageReq,
            @Param("scopeTypeEnum") IndContractsTypeEnum scopeTypeEnum);
}