package com.cscec3b.iti.projectmanagement.server.service;

import java.util.List;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cscec3b.iti.projectmanagement.api.dto.response.ProjectApprovalTypeResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.SubscriberForApprovalTypeResp;
import com.cscec3b.iti.projectmanagement.server.entity.ApprovalType;

public interface IApprovalTypeService extends IService<ApprovalType> {

    /**
     * 根据根节点查询列表
     *
     * @param rootCod 根节点code
     * @return {@link List}<{@link ProjectApprovalTypeResp}>
     */
    List<ProjectApprovalTypeResp> typeList(String rootCod);

    List<SubscriberForApprovalTypeResp> getSubscribeForApprovalTypeList(String subscriber,
            String appCode, String executeUnitQueryCode);

    /**
     * 获取下级列表
     *
     * @param typeId  id
     * @param typeIds
     */
    void getChildList(Long typeId, List<Long> typeIds);

    /**
     * 匹配业务部门和执行单位
     * 获取最匹配的一条数据(本上)
     *
     * @param businessSegmentCodePath 业务段代码路径
     * @param treeIds                 树 ID
     * @return {@link ApprovalType }
     */
    ApprovalType matchBusinessSegmentAndExecuteUnit(String businessSegmentCodePath, List<String> treeIds);

    List<ApprovalType> matchBusinessSegmentAndExecuteUnitV1(List<String> busCodePaths, List<String> treeIds);
}
