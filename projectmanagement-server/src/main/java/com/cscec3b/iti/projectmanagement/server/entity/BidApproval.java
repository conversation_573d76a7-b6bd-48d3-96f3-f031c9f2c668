package com.cscec3b.iti.projectmanagement.server.entity;

import com.baomidou.mybatisplus.annotation.*;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 中标未立项表
 */
@ApiModel(description = "中标未立项表")
@Data
@Accessors(chain = true)
@TableName(value = "bid_approval")
public class BidApproval {
    /**
     * id
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 原文件id
     */
    @TableField(value = "belong_id")
    @ApiModelProperty(value = "原文件id")
    private Long belongId;

    /**
     * 立项数据来源，tender_summary：投标总结，presentation：合同定案，agreement：补充协议，internal_presentation：局内部合同定案，internal_agreement
     * ：局内部补充协议
     */
    @TableField(value = "`type`")
    @ApiModelProperty(value = "立项数据来源，tender_summary：投标总结，presentation：合同定案，agreement：补充协议，internal_presentation" +
            "：局内部合同定案，internal_agreement：局内部补充协议, secrecy: 保密文件, civil_military:军民融合项目信息")
    private String type;

    /**
     * 补充协议编号
     */
    @TableField(value = "agreement_code")
    @ApiModelProperty(value = "补充协议编号")
    private String agreementCode;

    /**
     * 局内部合同定案编号
     */
    @TableField(value = "presentation_code")
    @ApiModelProperty(value = "局内部合同定案编号")
    private String presentationCode;

    /**
     * 工程编号
     */
    @TableField(value = "project_code")
    @ApiModelProperty(value = "工程编号")
    private String projectCode;

    @TableField(value = "investment_code")
    @ApiModelProperty(value = "投资编码")
    private String investmentCode;

    /**
     * 工程名称
     */
    @TableField(value = "project_name")
    @ApiModelProperty(value = "工程名称")
    private String projectName;

    /**
     * 业主名称
     */
    @TableField(value = "customer_name")
    @ApiModelProperty(value = "业主名称")
    private String customerName;

    /**
     * Y:国内，N：海外
     */
    @TableField(value = "project_belong")
    @ApiModelProperty(value = "Y:国内，N：海外")
    private String projectBelong;

    /**
     * 省
     */
    @TableField(value = "province")
    @ApiModelProperty(value = "省")
    private String province;

    /**
     * 市
     */
    @TableField(value = "city")
    @ApiModelProperty(value = "市")
    private String city;

    /**
     * 区
     */
    @TableField(value = "region")
    @ApiModelProperty(value = "区")
    private String region;

    @TableField(value = "region_id_path")
    @ApiModelProperty(value = "行政区划全路径，用/分割")
    private String regionIdPath;

    @TableField(value = "region_names")
    @ApiModelProperty(value = "行政区划全路径名称")
    private String regionNames;

    /**
     * 所属行政架构主体名称
     */
    @ApiModelProperty(value = "所属行政架构主体名称")
    @TableField(value = "belong_org_name")
    private String belongOrgName;

    /**
     * 所属行政架构主体编码
     */
    @ApiModelProperty(value = "所属行政架构主体编码")
    @TableField(value = "belong_org_code")
    private String belongOrgCode;

    /**
     * 国别
     */
    @TableField(value = "country")
    @ApiModelProperty(value = "国别")
    private String country;

    /**
     * 具体地址
     */
    @TableField(value = "address")
    @ApiModelProperty(value = "具体地址")
    private String address;

    /**
     * 工程类型（国家标准）
     */
    @TableField(value = "country_project_type")
    @ApiModelProperty(value = "工程类型（国家标准）")
    private String countryProjectType;

    /**
     * 工程类型（总公司市场口径）
     */
    @TableField(value = "market_project_type")
    @ApiModelProperty(value = "工程类型（总公司市场口径）")
    private String marketProjectType;

    /**
     * 工程类型（总公司市场口径）2
     */
    @TableField(value = "market_project_type2")
    @ApiModelProperty(value = "工程类型（总公司市场口径）2")
    private String marketProjectType2;

    /**
     * 工程类型(总公司综合口径)
     */
    @TableField(value = "project_type")
    @ApiModelProperty(value = "工程类型(总公司综合口径)")
    private String projectType;

    /**
     * 工程类型(总公司综合口径)2
     */
    @TableField(value = "project_type2")
    @ApiModelProperty(value = "工程类型(总公司综合口径)2")
    private String projectType2;

    /**
     * 工程类型(总公司综合口径)3
     */
    @TableField(value = "project_type3")
    @ApiModelProperty(value = "工程类型(总公司综合口径)3")
    private String projectType3;

    /**
     * 工程类型(总公司综合口径)4
     */
    @TableField(value = "project_type4")
    @ApiModelProperty(value = "工程类型(总公司综合口径)4")
    private String projectType4;

    /**
     * 是否创建指挥部,Y:是，N：否
     */
    @TableField(value = "is_create_head")
    @ApiModelProperty(value = "是否创建指挥部,Y:是，N：否")
    private String createHead;

    /**
     * 独立性判断，Y：是，N：否，D：不予立项
     */
    @TableField(value = "is_independ_project")
    @ApiModelProperty(value = "独立性判断，Y：是，N：否，D：不予立项")
    private String independentProject;


    @TableField(value = "is_engineering_project")
    @ApiModelProperty(value = "是否工程性项目，Y：是，N：否")
    private String engineeringProject;

    /**
     * 发起人
     */
    @TableField(value = "submit_person")
    @ApiModelProperty(value = "发起人")
    private String submitPerson;

    /**
     * 发起人姓名
     */
    @TableField(value = "submit_person_name")
    @ApiModelProperty(value = "发起人姓名")
    private String submitPersonName;

    /**
     * 复核人
     */
    @TableField(value = "approval_person")
    @ApiModelProperty(value = "复核人")
    private String approvalPerson;

    /**
     * 复核人姓名
     */
    @TableField(value = "approval_person_name")
    @ApiModelProperty(value = "复核人姓名")
    private String approvalPersonName;

    /**
     * 状态
     */
    @TableField(value = "`status`")
    @ApiModelProperty(value = "状态")
    private String status;

    /**
     * 挂接id
     */
    @TableField(value = "associated_id")
    @ApiModelProperty(value = "挂接id")
    private Long associatedId;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_at", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "修改时间")
    private Long updateAt;

    /**
     * 是否删除：0： 未删除； 时间戳为删除时间
     */
    @TableField(value = "deleted")
    @ApiModelProperty(value = "是否删除：0： 未删除； 时间戳为删除时间")
    private Long deleted;

    /**
     * 扩展字段
     */
    @TableField(value = "extension")
    @ApiModelProperty(value = "扩展字段")
    private String extension;

    /**
     * 云枢执行单位id
     */
    @TableField(value = "yunshu_execute_unit_id")
    @ApiModelProperty(value = "云枢执行单位id")
    private String yunshuExecuteUnitId;

    /**
     * 云枢执行单位code
     */
    @TableField(value = "yunshu_execute_unit_code")
    @ApiModelProperty(value = "云枢执行单位code")
    private String yunshuExecuteUnitCode;

    /**
     * 云枢执行单位
     */
    @TableField(value = "yunshu_execute_unit")
    @ApiModelProperty(value = "云枢执行单位")
    private String yunshuExecuteUnit;

    /**
     * 云枢执行单位idPath
     */
    @TableField(value = "yunshu_execute_unit_id_path")
    @ApiModelProperty(value = "云枢执行单位idPath")
    private String yunshuExecuteUnitIdPath;

    /**
     * 项目中心id
     */
    @TableField(value = "cpm_project_id")
    @ApiModelProperty(value = "项目中心id")
    private Long cpmProjectId;

    /**
     * 前置文件id
     */
    @TableField(value = "pre_file_id")
    @ApiModelProperty(value = "前置文件id")
    private Long preFileId;

    /**
     * 立项步骤列表
     */
    @TableField(value = "step_list")
    @ApiModelProperty(value = "立项步骤列表")
    private String stepList;

    /**
     * 立项项目类型
     */
    @TableField(value = "approval_type_id")
    @ApiModelProperty(value = "立项项目类型")
    private Long approvalTypeId;


    /**
     * 当前步骤id
     */
    @TableField(value = "current_step_no")
    @ApiModelProperty(value = "当前步骤id")
    private Integer currentStepNo;
    /**
     * 发起立项时间
     */
    @TableField(value = "approval_begin_time")
    @ApiModelProperty(value = "发起立项时间")
    private Long approvalBeginTime;

    /**
     * 项目部创建类型： 0:挂接项目部； 1: 创建项目部
     */
    @TableField(value = "dept_create_type")
    @ApiModelProperty(value = "项目部创建类型： 0:挂接项目部； 1: 创建项目部")
    private Integer deptCreateType;

    /**
     * 独立时新增项目部的名称
     */
    @TableField(value = "dept_name")
    @ApiModelProperty(value = "独立时新增项目部的名称")
    private String deptName;

    /**
     * 新增项目部的简称
     */
    @TableField(value = "dept_abbr")
    @ApiModelProperty(value = "新增项目部的简称")
    private String deptAbbr;

    /**
     * 独立时新增项目的上级部门id
     */
    @TableField(value = "parent_dept_id")
    @ApiModelProperty(value = "独立时新增项目的上级部门id")
    private String parentDeptId;

    /**
     * 独立立项挂接的项目部id, 或创建成功后的项目部id
     */
    @TableField(value = "dept_id")
    @ApiModelProperty(value = "项目部id", notes = "独立立项挂接的项目部id, 或创建成功后的项目部id")
    private String deptId;

    /**
     * 部门创建者
     */
    @TableField(value = "dept_create_by")
    @ApiModelProperty(value = "项目部创建人")
    private String deptCreateBy;

    /**
     * 步骤版本
     */
    @TableField(value = "step_version")
    @ApiModelProperty(value = "项目立项类型与步骤映射版本")
    private Integer stepVersion;

    //以下字段为新增工程项目使用字段
    @TableField(value = "worker_begin_time")
    @ApiModelProperty(value = "计划开工日期")
    private Long workerBeginTime;

    @TableField(value = "worker_end_time")
    @ApiModelProperty(value = "计划竣工日期")
    private Long workerEndTime;

    @TableField(value = "project_contractor_type")
    @ApiModelProperty(value = "项目承接主体类型")
    private String projectContractorType;

    @TableField(value = "fabricated")
    @ApiModelProperty(value = "是否装配式")
    private String fabricated;

    @TableField(value = "project_category")
    @ApiModelProperty(value = "项目分类")
    private String projectCategory;

    @TableField(value = "project_category_code_path")
    @ApiModelProperty(value = "项目分类codePath")
    private String projectCategoryCodePath;

    @TableField(value = "custom_code")
    @ApiModelProperty(value = "业主单位")
    private String customCode;

    @TableField(value = "custom_code_name")
    @ApiModelProperty(value = "业主单位名称")
    private String customCodeName;

    @TableField(value = "contract_mode")
    @ApiModelProperty(value = "承建模式")
    private String contractMode;

    @TableField(value = "is_same_section_as_gc")
    @ApiModelProperty(value = "是否与总承包同一标段")
    private String sameSectionAsGc;

    @TableField(value = "is_internal_gc")
    @ApiModelProperty(value = "是否为内部总承包单位")
    private String internalGc;

    @TableField(value = "parent_managed_project_name")
    @ApiModelProperty(value = "上级管理总承包项目名称")
    private String parentManagedProjectName;

    @TableField(value = "parent_managed_project_code")
    @ApiModelProperty(value = "上级管理总承包项目编码")
    private String parentManagedProjectCode;

    @TableField(value = "joint_project_with_csc_lead")
    @ApiModelProperty(value = "是否为联合体项目（牵头方为中建内部）")
    private String jointProjectWithCscLead;

    @TableField(value = "internal_joint_project_lead")
    @ApiModelProperty(value = "是否为内部联合体牵头项目")
    private String internalJointProjectLead;

    @TableField(value = "internal_joint_project_lead_name")
    @ApiModelProperty(value = "内部联合体牵头项目名称")
    private String internalJointProjectLeadName;

    @TableField(value = "internal_joint_project_lead_code")
    @ApiModelProperty(value = "内部联合体牵头项目编码")
    private String internalJointProjectLeadCode;

    @TableField(value = "project_importance_class")
    @ApiModelProperty(value = "项目重要性类别")
    private String projectImportanceClass;

    @TableField(value = "signed_subject_value")
    @ApiModelProperty(value = "签约主体名称")
    private String signedSubjectValue;

    @TableField(value = "signed_subject_code")
    @ApiModelProperty(value = "签约主体代码")
    private String signedSubjectCode;

    @TableField(value = "build_unit")
    @ApiModelProperty(value = "建设单位")
    private String buildUnit;

    @TableField(value = "build_unit_name")
    @ApiModelProperty(value = "建设单位名称")
    private String buildUnitName;

    @TableField(value = "design_unit")
    @ApiModelProperty(value = "设计单位")
    private String designUnit;

    @TableField(value = "survey_unit")
    @ApiModelProperty(value = "勘察单位")
    private String surveyUnit;

    @TableField(value = "supervision_unit")
    @ApiModelProperty(value = "监理单位")
    private String supervisionUnit;

    /**
     * 信息确认时间
     */
    @TableField(value = "confirm_time")
    @ApiModelProperty(value = "信息确认时间")
    private Long confirmTime;

    @TableField(value = "saved")
    @ApiModelProperty(value = "是否保存", notes = "Y:否，N：是")
    private String saved;

    /**
     * 是否工程性项目，Y：是，N：否
     */
    @TableField(value = "engineering_code")
    @ApiModelProperty(value = "是否工程性项目，Y：是，N：否")
    private String engineeringCode;

    /**
     * 工程项目上行结果，1：是，0：否
     */
    @TableField(value = "mdm_push_result")
    @ApiModelProperty(value = "工程项目上行结果，1：是，0：否")
    private Integer mdmPushResult;

    /**
     * 上行错误信息
     */
    @TableField(value = "mdm_push_err_msg")
    private String mdmPushErrMsg;

    /**
     * 是否为内部拆分的上级项目（0否1是）
     */
    @TableField(value = "is_internal_split_parent_project")
    @ApiModelProperty(value = "是否为内部拆分的上级项目（N否Y是）")
    private String internalSplitParentProject;

    /**
     * 是否为内部拆分项目（0否1是）
     */
    @TableField(value = "is_internal_split_project")
    @ApiModelProperty(value = "是否为内部拆分项目（N否Y是）")
    private String internalSplitProject;

    /**
     * 内部拆分上级项目编码
     */
    @TableField(value = "internal_split_parent_project_code")
    @ApiModelProperty(value = "内部拆分上级项目编码")
    private String internalSplitParentProjectCode;

    /**
     * 内部拆分上级项目名称
     */
    @TableField(value = "internal_split_parent_project_name")
    @ApiModelProperty(value = "内部拆分上级项目名称")
    private String internalSplitParentProjectName;

    // /**
    // * 项目经理（执行）编码
    // */
    // @TableField(value = "executive_pm_code")
    // @ApiModelProperty(value = "项目经理（执行）编码")
    // private String executivePmCode;
    //
    // /**
    // * 项目经理（执行）姓名
    // */
    // @TableField(value = "executive_pm_name")
    // @ApiModelProperty(value = "项目经理（执行）姓名")
    // private String executivePmName;
    //
    // /**
    // * 项目经理（执行）联系电话
    // */
    // @TableField(value = "executive_pm_phone")
    // @ApiModelProperty(value = "项目经理（执行）联系电话")
    // private String executivePmPhone;

    /**
     * 项目地址
     */
    @TableField(value = "project_address")
    @ApiModelProperty(value = "项目地址")
    private String projectAddress;
    //
    // /**
    // * 项目经度
    // */
    // @TableField(value = "project_longitude")
    // @ApiModelProperty(value = "项目经度")
    // private BigDecimal projectLongitude;
    //
    // /**
    // * 项目纬度
    // */
    // @TableField(value = "project_latitude")
    // @ApiModelProperty(value = "项目纬度")
    // private BigDecimal projectLatitude;
    //
    // /**
    // * 工程项目状态:1.1:开工准备,1.2: 正常施工, 1.3: 完工待竣; 2已竣未结, 3: 已竣已结4: 停工）
    // */
    // @TableField(value = "engine_project_status")
    // @ApiModelProperty(value = "工程项目状态:1.1:开工准备,1.2: 正常施工, 1.3: 完工待竣; 2已竣未结, 3: 已竣已结4: 停工）")
    // private String engineProjectStatus;
    //
    // /**
    // * 项目规模（建筑面积/造价分类）
    // */
    // @TableField(value = "project_scale")
    // @ApiModelProperty(value = "项目规模（建筑面积/造价分类）")
    // private String projectScale;
    //
    // /**
    // * 项目建筑面积（平方米）
    // */
    // @TableField(value = "project_building_area")
    // @ApiModelProperty(value = "项目建筑面积（平方米）")
    // private BigDecimal projectBuildingArea;
    //
    // /**
    // * 项目里程（公里）
    // */
    // @TableField(value = "project_mileage")
    // @ApiModelProperty(value = "项目里程（公里）")
    // private BigDecimal projectMileage;
    //
    // /**
    // * 实际开工日期
    // */
    // @TableField(value = "actual_worker_begin_time")
    // @ApiModelProperty(value = "实际开工日期")
    // private Long actualWorkerBeginTime;
    //
    // /**
    // * 实际竣工日期
    // */
    // @TableField(value = "actual_worker_end_time")
    // @ApiModelProperty(value = "实际竣工日期")
    // private Long actualWorkerEndTime;
}