package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.projectmanagement.api.IAuthController;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.SwitchOrgResp;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.UserInfo;
import com.cscec3b.iti.projectmanagement.server.service.IAuthService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户认证管理
 */
@Api(tags = {"新版本(六统一)用户认证管理"})
@RestController
@RequestMapping(IAuthController.PATH)
@Deprecated
public class AuthController implements IAuthController {

	@Resource
	private IAuthService xindunAuthService;

	@Override
	public GenericityResponse<UserInfo> getUserInfo() {
		return ResponseBuilder.fromData(xindunAuthService.getUserInfo());
	}

	@Override
	public GenericityResponse<SwitchOrgResp> switchOrg(String orgId) {
		return ResponseBuilder.fromData(xindunAuthService.switchOrg(orgId));
	}
}
