package com.cscec3b.iti.projectmanagement.server.projectapproval.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ApprovalStepReq;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.BidApprovalHandlerFactory;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.BidFilePreHandler;
import com.cscec3b.iti.projectmanagement.server.config.UcOpenApiProperties;
import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.enums.ApprovalStepEnumV2;
import com.cscec3b.iti.projectmanagement.server.enums.EngineProjectEnum;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.IndependTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.workflow.WfTaskStateEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.ApprovalTypeStepMappingMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.BidApprovalMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.projectapproval.AbstractApprovalStep;
import com.cscec3b.iti.projectmanagement.server.projectapproval.StepServiceFactory;
import com.cscec3b.iti.projectmanagement.server.service.IEngineeringProjectService;
import com.cscec3b.iti.projectmanagement.server.service.IMDMService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import com.cscec3b.iti.projectmanagement.server.service.WfApprovalService;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import com.cscec3b.iti.taskmesage.service.TaskAndMessageService;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Objects;

/**
 * 立项步骤-立项复核
 *
 * <AUTHOR>
 * @date 2024/01/03
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class StepOfReCheckServiceImpl extends AbstractApprovalStep {

    private final BidApprovalMapper bidApprovalMapper;


    private final ProjectService projectService;

    private final ProjectMapper projectMapper;

    private final IMDMService mdmService;

    private IEngineeringProjectService engineeringProjectService;

    protected StepOfReCheckServiceImpl(ProjectMapper projectMapper, BidApprovalMapper bidApprovalMapper,
                                       StepServiceFactory stepServiceFactory, BidApprovalMapper bidApprovalMapper1,
                                       WfApprovalService wfApprovalService, ProjectService projectService, ProjectMapper projectMapper1,
                                       BidApprovalHandlerFactory bidApprovalHandlerFactory, ApprovalTypeStepMappingMapper typeStepMappingMapper,
                                       TaskAndMessageService taskAndMessageService, UcOpenApiProperties ucOpenApiProperties,
                                       IMDMService mdmService, IEngineeringProjectService engineeringProjectService) {
        super(projectMapper, bidApprovalMapper, stepServiceFactory, typeStepMappingMapper, taskAndMessageService,
              ucOpenApiProperties);
        this.bidApprovalMapper = bidApprovalMapper1;
        this.projectService = projectService;
        this.projectMapper = projectMapper1;
        this.bidApprovalHandlerFactory = bidApprovalHandlerFactory;
        this.mdmService = mdmService;
        this.engineeringProjectService = engineeringProjectService;
    }

    /**
     * 合同文件处理工厂
     */
    private final BidApprovalHandlerFactory bidApprovalHandlerFactory;

    @Override
    public Integer currentStepNo() {
        return ApprovalStepEnumV2.RE_CHECK.getNo();
    }


    /**
     * 立项完成后根据独立性状态、项目部创建类型来提交以初始化项目、创建/挂接项目部 或关联项目
     *
     * @param req
     * @return boolean
     */
    @Override
    public BidApproval submit(ApprovalStepReq req) {
        final BidApproval bidApproval = super.submit(req);
        // 只初始化项目，不进行组织创建及财商推送
        Long projectId = createOrHookProject(bidApproval);
        // 如果独立性判断为非独立项目，移除当前步骤之后的步骤信息
        final String stepList = bidApproval.getStepList();
        if (Objects.equals(IndependTypeEnum.NON_INDEPENDENT.getCode(), bidApproval.getIndependentProject())) {
            List<Integer> stepNos = JsonUtils.readValue(stepList, new TypeReference<List<Integer>>() {
            });
            if (stepNos != null) {
                stepNos = stepNos.subList(0, stepNos.indexOf(currentStepNo()) + 1);
                stepNos.add(ApprovalStepEnumV2.END.getNo());
            }
            bidApproval.setStepList(JsonUtils.toJsonStr(stepNos));
            log.info("非独立项目，移除当前步骤之后的步骤信息:{}", JsonUtils.toJsonStr(stepNos));
        }
        bidApproval.setCpmProjectId(projectId).setUpdateAt(Instant.now().toEpochMilli());
        bidApproval.setUpdateBy(LoginUserUtil.userCode());
        bidApproval.setUpdateAt(Instant.now().toEpochMilli());
        bidApprovalMapper.updateById(bidApproval);
        gotoNextStep(bidApproval);
        return bidApproval;
    }

    /**
     * 项目创建
     *
     * @param bidApproval 中标未立项信息
     * @return {@link Boolean}
     */
    // @Override
    public Long createOrHookProject(BidApproval bidApproval) {
        final String independentProject = bidApproval.getIndependentProject();
        final IndependTypeEnum independTypeEnum = IndependTypeEnum.getEnumByCode(independentProject);
        if (IndependTypeEnum.INDEPENDENT.equals(independTypeEnum)) {
            // 独立立项
            log.info("独立立项，bidApproval:{}", JsonUtils.toJsonStr(bidApproval));
            Long projectId = createProject(bidApproval);
            // 是否是工程项目
            String engineeringProject = bidApproval.getEngineeringProject();
            // 工程项目需要去创建工程项目
            if (EngineProjectEnum.ENGINE_PROJECT.getCode().equals(engineeringProject)) {
                // 创建工程项目
                String mdmCode = null;
                try {
                    mdmCode = mdmService.pushEngineProject(bidApproval);
                    // 初始化工程项目
                    mdmService.initEngineProject(projectId, mdmCode, bidApproval);
                    bidApproval.setEngineeringCode(mdmCode).setMdmPushResult(1);
                } catch (Exception e) {
                    log.error("推送工程项目异常", e);
                    // 记录异常状态
                    bidApproval.setMdmPushResult(0).setMdmPushErrMsg(e.getMessage());
                }

            }
            return projectId;
        } else if (IndependTypeEnum.NON_INDEPENDENT.equals(independTypeEnum)) {
            log.info("非独立立项，bidApproval:{}", JsonUtils.toJsonStr(bidApproval));
            // 关联项目
            return hookProject(bidApproval);
        }
        return null;
    }

    /**
     * 关联项目:<br>
     * 1. 更新文件挂接信息<br>
     * 2. 更新项目金额<br>
     *
     * @param bidApproval 中标未立项
     * @return {@link Long}
     */
    private Long hookProject(BidApproval bidApproval) {
        final Project project = projectService.selectById(bidApproval.getCpmProjectId());
        final Long independentContractId = project.getIndependentContractId();
        final Integer independentContractType = project.getIndependentContractType();
        final String type = bidApproval.getType();
        final IndContractsTypeEnum indContractsTypeEnum = IndContractsTypeEnum.getEnumCode(type);
        final Long belongId = bidApproval.getBelongId();
        // 更新文件挂接信息
        final BidFilePreHandler fileHandler = bidApprovalHandlerFactory.getFileHandler(indContractsTypeEnum);
        fileHandler.hookProject(independentContractId, independentContractType, belongId,
                IndependTypeEnum.NON_INDEPENDENT.getCode());
        // 更新项目金额
        projectService.recalculationProjectAmountsV3(project);
        projectService.updateProject(project);
        return project.getId();
    }

    /**
     * 项目初始化
     *
     * @param bidApproval 中标未立项
     * @return {@link Long}
     */
    private Long createProject(BidApproval bidApproval) {
        // 创建项目：
        final String type = bidApproval.getType();
        final IndContractsTypeEnum indContractsTypeEnum = IndContractsTypeEnum.getEnumCode(type);
        if (ObjectUtils.isEmpty(indContractsTypeEnum)) {
            throw new BusinessException(8010320);
        }
        // 检查是否重复立项
        final int count =
                projectMapper.selectCount(Wrappers.<Project>lambdaQuery().eq(Project::getIndependentContractId,
                                bidApproval.getId())
                        .eq(Project::getIndependentContractType, indContractsTypeEnum.getDictCode()));
        if (count > 0) {
            throw new BusinessException(8010006);
        }
        final BidFilePreHandler<?> fileHandler = bidApprovalHandlerFactory.getFileHandler(indContractsTypeEnum);
        return fileHandler.createProject(bidApproval);
    }

    /**
     * 是否完成
     * 当前需要审核通过才能进入到下一步
     * todo 如何判断当前步骤已经走完？
     *
     * @param bidApproval 中标未立项
     * @return boolean
     */
    @Override
    public boolean isComplete(BidApproval bidApproval) {
        return Objects.equals(bidApproval.getStatus(), String.valueOf(WfTaskStateEnum.AGREE.getDictCode()));
    }
}
