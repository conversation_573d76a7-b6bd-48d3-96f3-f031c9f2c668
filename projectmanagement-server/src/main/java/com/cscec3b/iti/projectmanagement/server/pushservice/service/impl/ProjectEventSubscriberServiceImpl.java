package com.cscec3b.iti.projectmanagement.server.pushservice.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.request.dict.SysDictDataReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.dict.SysDictTypeReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.CreateSubscriberReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.QuerySubscribersListReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.QuerySubscribersReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.UpdateSubscriberReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectFlowNodeHandlerList;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectFlowNodeListResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.SubscriberListResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.SubscriberPageResp;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEventSubscribe;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ProjectFlowEventSubscribeDto;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectEventSubscribeMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectFlowEventSubscribeMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.service.IProjectEventSubscriberService;
import com.cscec3b.iti.projectmanagement.server.service.SysDictDataService;
import com.cscec3b.iti.projectmanagement.server.service.SysDictTypeService;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import com.github.pagehelper.PageHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description ProjectEventSubscriberServiceImpl
 * @date 2023/04/19 09:34
 */
@Slf4j
@Service
public class ProjectEventSubscriberServiceImpl implements IProjectEventSubscriberService {

    private final ProjectEventSubscribeMapper eventSubscribeMapper;

    @Resource
    private ProjectFlowEventSubscribeMapper projectFlowEventSubscribeMapper;

    @Resource
    private SysDictTypeService dictTypeService;

    @Resource
    private SysDictDataService dictDataService;

    public ProjectEventSubscriberServiceImpl(ProjectEventSubscribeMapper subscribeMapper) {
        eventSubscribeMapper = subscribeMapper;
    }

    /**
     * 分页查询
     * @param subscribersReq
     * @return
     */
    @Override
    public Page<SubscriberPageResp> getSubscriberPages(QuerySubscribersReq subscribersReq) {
        Integer pageNum = subscribersReq.getCurrent();
        Integer pageSize = subscribersReq.getSize();
        final com.github.pagehelper.Page<ProjectEventSubscribe> projectPage = PageHelper.startPage(pageNum, pageSize)
                .doSelectPage(() -> eventSubscribeMapper.getSubscriberPages(subscribersReq));
        List<ProjectEventSubscribe> subscribes = projectPage.getResult();
        List<SubscriberPageResp> subscriberPageResps = BeanUtil.copyToList(subscribes, SubscriberPageResp.class);

        Page<SubscriberPageResp> page = new Page<>(projectPage.getTotal(), projectPage.getPageNum(), projectPage.getPageSize());
        page.setRecords(subscriberPageResps);
        return page;
    }

    /**
     * @param subscriberReq
     * @return
     */
    @Override
    public Boolean createSubscriber(CreateSubscriberReq subscriberReq) {
        String protocol = subscriberReq.getProtocol();
        String pushUrl = subscriberReq.getPushUrl();
        //接口请求协议类型与推送url地址不匹配
        if (Constants.HTTP.equals(protocol) && !StringUtils.startsWithIgnoreCase(pushUrl, Constants.HTTP_PREFIX) ||
                Constants.HTTPS.equals(protocol) && !StringUtils.startsWithIgnoreCase(pushUrl, Constants.HTTPS_PREFIX)) {
            throw new FrameworkException(80106010,"接口请求协议类型与推送url地址不匹配");
        }
        String appCode = subscriberReq.getAppCode();
        ProjectEventSubscribe projectEventSubscribe = eventSubscribeMapper.selectByAppCode(appCode);
        if (Objects.nonNull(projectEventSubscribe)){
            throw new FrameworkException(80106012,"系统标识重复");
        }
        try {
            ProjectEventSubscribe subscriber = new ProjectEventSubscribe();
            BeanUtil.copyProperties(subscriberReq, subscriber);
            subscriber.setStatus(1);
            // 默认添加系统结算级别key
            subscriber.setDictType(subscriber.getAppCode());
            eventSubscribeMapper.insert(subscriber);
            // 创建默认字典
            final SysDictTypeReq sysDictTypeReq =
                    new SysDictTypeReq().setDictName("结算级别").setDictType(subscriber.getAppCode()).setRemark("业务系统添加");
            dictTypeService.insertDictType(sysDictTypeReq);
            // 创建默认字典数据
            final SysDictDataReq sysDictDataReq =
                    new SysDictDataReq().setDictLabel("一级结算").setDictValue("01")
                            .setDictType(subscriber.getAppCode()).setDictSort(1).setRemark("业务系统添加");
            dictDataService.insertDictData(sysDictDataReq);
        } catch (Exception e) {
            //订阅系统名称已经存在
            if (e instanceof DuplicateKeyException) {
                throw new FrameworkException(80106011,"订阅系统名称已经存在");
            } else {
                //抛出其它业务异常
                log.error("系统异常：{}",e.getMessage());
                throw new BusinessException(8010603);
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 更新订阅系统配置信息
     *
     * @param subscriberReq
     * @return
     */
    @Override
    public Boolean updateSubscriber(UpdateSubscriberReq subscriberReq) {
        Long consumerId = subscriberReq.getConsumerId();
        String protocol = subscriberReq.getProtocol();
        String pushUrl = subscriberReq.getPushUrl();
        ProjectEventSubscribe subscribe = eventSubscribeMapper.selectByPrimaryKey(consumerId);
        if (Objects.isNull(subscribe)) {
            throw new BusinessException(8010604,new String[]{consumerId.toString()});
        }
        //接口请求协议类型与推送url地址不匹配
        if (Constants.HTTP.equals(protocol) && !StringUtils.startsWithIgnoreCase(pushUrl, Constants.HTTP_PREFIX) ||
                Constants.HTTPS.equals(protocol) && !StringUtils.startsWithIgnoreCase(pushUrl, Constants.HTTPS_PREFIX)) {
            throw new FrameworkException(80106010,"接口请求协议类型与推送url地址不匹配");
        }
        if (!subscribe.getAppCode().equals(subscriberReq.getAppCode())){
            String appCode = subscriberReq.getAppCode();
            ProjectEventSubscribe projectEventSubscribe = eventSubscribeMapper.selectByAppCode(appCode);
            if (Objects.nonNull(projectEventSubscribe)){
                throw new FrameworkException(80106012,"系统标识重复");
            }
        }
        try {
            BeanUtil.copyProperties(subscriberReq,subscribe);
            subscribe.setStatus(1);
            subscribe.setUpdateAt(Instant.now().toEpochMilli()).setUpdateBy(LoginUserUtil.userCode());
            eventSubscribeMapper.updateByPrimaryKey(subscribe);
        } catch (Exception e) {
            //订阅系统名称已经存在
            if (e instanceof DuplicateKeyException) {
                throw new FrameworkException(80106011,"订阅系统名称已经存在");
            } else {
                //抛出其它业务异常
                log.error("系统异常：{}",e.getMessage());
                throw new BusinessException(8010603);
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 变更业务系统订阅配置的状态，1：正常，0：删除
     *
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeSubscriberStatus(UpdateSubscriberReq subscriberReq) {
        Long consumerId = subscriberReq.getConsumerId();
        ProjectEventSubscribe subscribe = eventSubscribeMapper.selectByPrimaryKey(consumerId);
        if (Objects.isNull(subscribe)) {
            throw new BusinessException(8010604,new String[]{consumerId.toString()});
        }
        subscribe.setUpdateAt(Instant.now().toEpochMilli()).setUpdateBy(LoginUserUtil.userCode());
        //真实删除
        Integer result = eventSubscribeMapper.deleteByPrimaryKey(consumerId);
        projectFlowEventSubscribeMapper.deleteByConsumerId(consumerId);
        if (!Constants.NUMBER_ONE.equals(result)) {
            throw new BusinessException(8010603);
        }
        return Boolean.TRUE;
    }

    /**
     * 通过过flownodecode、flowhandlercode、flowdatatypecode获取目标业务类型的订阅配置
     *
     * @param nodeEnum    flownode编码
     * @param handlerEnum flowhandler编码
     * @return {@link List }<{@link ProjectEventSubscribe }>
     * <AUTHOR>
     * @date 2023/09/15
     */
    @Override
    public List<ProjectFlowEventSubscribeDto> getFlowEventSubscribers(FlowNodeEnum nodeEnum, FlowNodeHandlerEnum handlerEnum) {
        return eventSubscribeMapper.getFlowEventSubscribers(nodeEnum, handlerEnum, null, null);
    }

    @Override
    public List<ProjectFlowEventSubscribeDto> getFlowEventSubscribers(FlowNodeEnum nodeEnum,
            FlowNodeHandlerEnum handlerEnum, FlowNodeDataTypeEnum dataTypeEnum, String customerId) {
        return eventSubscribeMapper.getFlowEventSubscribers(nodeEnum, handlerEnum, dataTypeEnum, customerId);
    }

    @Override
    public ProjectEventSubscribe getById(Long subscribeId) {
        return eventSubscribeMapper.selectByPrimaryKey(subscribeId);
    }

    @Override
    public List<ProjectFlowNodeListResp> getFlowNodeEnumInfo() {
        final FlowNodeEnum[] enums = FlowNodeEnum.values();
        //List<FlowNodeEnum> enums = Arrays.asList(
        //        FlowNodeEnum.MARKETING_SEGMENT,
        //        FlowNodeEnum.FINANCE_SEGMENT,
        //        FlowNodeEnum.SMART_SITE_SEGMENT,
        //        FlowNodeEnum.CPM_SEGMENT,
        //        FlowNodeEnum.FINANCE_SMART_SITE_APPROVAL,
        //        FlowNodeEnum.SUPPLY_SEGMENT,
        //        FlowNodeEnum.SPECIAL_PROJECT
        //);
        List<ProjectFlowNodeListResp> projectFlowNodeList = new ArrayList<>();
        for (FlowNodeEnum anEnum : enums) {
            ProjectFlowNodeListResp projectFlowNodeListResp = new ProjectFlowNodeListResp();
            BeanUtils.copyProperties(anEnum, projectFlowNodeListResp,new String("handlers"));
            List<ProjectFlowNodeHandlerList> list = BeanUtil.copyToList(anEnum.getHandlers(), ProjectFlowNodeHandlerList.class);
            projectFlowNodeListResp.setHandlers(list);
            projectFlowNodeList.add(projectFlowNodeListResp);
        }
        return projectFlowNodeList;
    }

    @Override
    public List<SubscriberListResp> getSubscriberList(QuerySubscribersListReq subscribersReq) {
        QuerySubscribersReq querySubscribersReq = BeanUtil.copyProperties(subscribersReq, QuerySubscribersReq.class);
        List<ProjectEventSubscribe> subscriberPages = eventSubscribeMapper.getSubscriberPages(querySubscribersReq);
        return BeanUtil.copyToList(subscriberPages, SubscriberListResp.class);
    }

    @Override
    public List<ProjectEventSubscribe> getList() {
        return eventSubscribeMapper.selectList(Wrappers.lambdaQuery());
    }
}
