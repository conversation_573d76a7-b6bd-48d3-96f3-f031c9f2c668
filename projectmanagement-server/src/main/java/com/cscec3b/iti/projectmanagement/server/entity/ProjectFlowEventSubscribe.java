package com.cscec3b.iti.projectmanagement.server.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
    * 项目流转事件配置表
    */
@ApiModel(description="项目流转事件配置表")
@Data
public class ProjectFlowEventSubscribe {
    /**
    * 主键
    */
    @ApiModelProperty(value="主键")
    private Long id;

    /**
     * 消费者-事件配置编码
     */
    @ApiModelProperty(value = "消费者-事件配置编码")
    private String code;

    /**
    * 配置名称
    */
    @ApiModelProperty(value="配置名称")
    private String name;

    /**
    * 项目流转节点
    */
    @ApiModelProperty(value="项目流转节点")
    private String flowNodeCode;

    /**
    * 项目流转节点监听切点：pre: 节点后;  post:节点后;
    */
    @ApiModelProperty(value="项目流转节点监听切点：pre: 节点后;  post:节点后;")
    private String flowHandlerCode;

    /**
    * 项目信息类型：insert:新增; update:更新;
    */
    @ApiModelProperty(value="项目信息类型：insert:新增; update:更新;")
    private String flowDataTypeCode;

    /**
    * 触发时间
    */
    @ApiModelProperty(value="触发时间")
    private Long createAt;

    /**
    * 推送系统id
    */
    @ApiModelProperty(value="推送系统id")
    private Integer consumerId;

    /**
    * 更新时间
    */
    @ApiModelProperty(value="更新时间")
    private Long updateAt;

    /**
    * 状态 0:禁用; 1:启用;
    */
    @ApiModelProperty(value="状态 0:禁用; 1:启用;")
    private Byte status;

    @ApiModelProperty(value="事件编码")
    private String flowCode;

    @ApiModelProperty(value = "节点项")
    private String flowItems;

}