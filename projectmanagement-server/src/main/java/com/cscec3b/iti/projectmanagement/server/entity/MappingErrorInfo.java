package com.cscec3b.iti.projectmanagement.server.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

/**
 * 智慧工地和财商映射异常记录
 */
@ApiModel(description = "智慧工地和财商映射异常记录")
@Data
@Accessors(chain = true)
@SuperBuilder
@TableName(value = "mapping_error_info")
public class MappingErrorInfo {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "")
    private Long id;

    /**
     * 异常类型：1：工地；2:财商
     */
    @TableField(value = "`type`")
    @ApiModelProperty(value = "异常类型：1：工地；2:财商")
    private Integer type;

    /**
     * 项目id
     */
    @TableField(value = "project_id")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    /**
     * 项目中心原始值 ：type=1时为yunshuOrgId; type=2时为财商编码
     */
    @TableField(value = "original_value")
    @ApiModelProperty(value = "项目中心原始值 ：type=1时为yunshuOrgId; type=2时为财商编码")
    private String originalValue;

    /**
     * 变更值：type=1时为yunshuOrgId;type=2时为projectFinanceCode
     */
    @TableField(value = "revised_value")
    @ApiModelProperty(value = "变更值：type=1时为yunshuOrgId;type=2时为projectFinanceCode")
    private String revisedValue;

    /**
     * 请求变更时间
     */
    @TableField(value = "request_time")
    @ApiModelProperty(value = "请求变更时间")
    private LocalDateTime requestTime;

    /**
     * project_event_receive_record表主键id
     */
    @TableField(value = "event_receive_record_id")
    @ApiModelProperty(value = "project_event_receive_record表主键id")
    private Long eventReceiveRecordId;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_at", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 更新时间
     */
    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Long updateAt;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 状态：0：正常； 时间戳为删除时间
     */
    @TableField(value = "deleted")
    @TableLogic(value = "0", delval = "UNIX_TIMESTAMP() * 1000")
    @ApiModelProperty(value = "状态：0：正常； 时间戳为删除时间")
    private Long deleted;
}