package com.cscec3b.iti.projectmanagement.server.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cscec3b.iti.projectmanagement.server.entity.MdmCallbackRecord;
import com.cscec3b.iti.projectmanagement.server.mapper.MdmCallbackRecordMapper;
import com.cscec3b.iti.projectmanagement.server.service.IMdmCallbackRecordService;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class MdmCallbackRecordServiceImpl extends ServiceImpl<MdmCallbackRecordMapper, MdmCallbackRecord> implements IMdmCallbackRecordService {

    @Override
    @Cacheable(cacheNames = "mdmCallbackRecord:check:msgId:", key = "#msgId")
    public List<MdmCallbackRecord> getByMsgId(String msgId) {
        return this.list(Wrappers.<MdmCallbackRecord>lambdaQuery().eq(MdmCallbackRecord::getMsgId, msgId));
    }
}
