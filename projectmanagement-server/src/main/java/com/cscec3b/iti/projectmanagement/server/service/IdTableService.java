package com.cscec3b.iti.projectmanagement.server.service;

public interface IdTableService{


    /**
     * 获取自增id
     * @param prefix 前缀
     * @param curDate 当前日期
     * @return {@link Long}
     */
    Long getIncrementId(String prefix, String curDate);

    /**
     * 获取cpmProjectKey
     *
     * @return {@link String}
     */
    String getCpmProjectKey();


    /**
     * 获取通用key
     *
     * @param prefix 前缀
     * @return {@link String}
     */
    String getCommonKey(String prefix);
}
