package com.cscec3b.iti.projectmanagement.server.pushservice.filter;

import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Component
public class FilterChainBuilder {

    private final List<EventMsgFilter> filters;

    public FilterChainBuilder(List<EventMsgFilter> filters) {
        this.filters =
                filters.stream().sorted(Comparator.comparingInt(Ordered::getOrder)).collect(Collectors.toList());
    }

    public EventMsgFilter buildFilterChain() {
        for (int i = 0; i < filters.size() - 1; i++) {
            filters.get(i).setNextFilter(filters.get(i + 1));
        }
        return filters.get(0);
    }
    
}
