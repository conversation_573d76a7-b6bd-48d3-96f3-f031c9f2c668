package com.cscec3b.iti.projectmanagement.server.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.cscec3b.iti.projectmanagement.api.dto.response.project.RevisionRecordResp;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectRevisionRecord;

/**
 * 项目修订记录映射器
 *
 * <AUTHOR>
 * @date 2023/08/21
 */
@Mapper
public interface ProjectRevisionRecordMapper {
    
    /**
     * 插入
     *
     * @param revisionRecord 修订记录
     * @return int
     * <AUTHOR>
     * @date 2023/08/21
     */
    int insert(ProjectRevisionRecord revisionRecord);
    
    /**
     * 查询修订列表
     * Xinfa
     * 2023/08/21
     *
     * @param projectId          项目id
     * @param revisionType       修改类型
     * @param projectFinanceCode 项目财商代码
     * @param projectFinanceName 项目财商名字
     * @param originalValue      原始值
     * @param revisedValue       修改后值
     * @param userId             用户id
     * @param username           用户名
     * @param startTime          开始时间
     * @param endTme             结束时间
     * @return {@link List }<{@link RevisionRecordResp }>
     */
    List<RevisionRecordResp> getRecodrPageList(@Param("projectId") Long projectId,
        @Param("revisionType") Integer revisionType, @Param("projectFinanceCode") String projectFinanceCode,
        @Param("projectFinanceName") String projectFinanceName, @Param("originalValue") String originalValue,
        @Param("revisedValue") String revisedValue, @Param("userId") String userId, @Param("username") String username,
        @Param("startTime") Long startTime, @Param("endTime") Long endTme, @Param("remark") String remark);
}
