package com.cscec3b.iti.projectmanagement.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.api.dto.request.workflow.WfApprovalPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.WfApprovalTodoPageResp;
import com.cscec3b.iti.projectmanagement.server.entity.WfApproval;
import com.cscec3b.iti.projectmanagement.server.enums.workflow.WfTaskStateEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface WfApprovalMapper extends BaseMapper<WfApproval> {


    /**
     * 个人待办
     *
     * @param req       查询参数
     * @return {@link List}<{@link WfApprovalTodoPageResp}>
     */
    List<WfApprovalTodoPageResp> getTodoPageList(@Param("req") WfApprovalPageReq req);

    /**
     * 获取进行中的流程
     *
     * @param businessDataType 业务板块
     * @param fieldId          字段id
     * @param cpmProjectId     项目id
     * @param taskStateEnum    状态枚举
     * @return {@link WfApproval}
     */
    List<WfApproval> getInProcessApproval(@Param("businessDataType") String businessDataType,
            @Param("fieldId") String fieldId, @Param("cpmProjectId") Long cpmProjectId,
            @Param("taskStateEnum") WfTaskStateEnum taskStateEnum);
}