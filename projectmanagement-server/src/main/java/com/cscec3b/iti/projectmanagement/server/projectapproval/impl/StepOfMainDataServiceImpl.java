package com.cscec3b.iti.projectmanagement.server.projectapproval.impl;

import com.cscec3b.iti.projectmanagement.server.config.UcOpenApiProperties;
import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
import com.cscec3b.iti.projectmanagement.server.enums.ApprovalStepEnumV2;
import com.cscec3b.iti.projectmanagement.server.mapper.ApprovalTypeStepMappingMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.BidApprovalMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.projectapproval.AbstractApprovalStep;
import com.cscec3b.iti.projectmanagement.server.projectapproval.StepServiceFactory;
import com.cscec3b.iti.taskmesage.service.TaskAndMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 立项步骤-集团主数据同步
 *
 * <AUTHOR>
 * @date 2024/01/03
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class StepOfMainDataServiceImpl extends AbstractApprovalStep {

    protected StepOfMainDataServiceImpl(ProjectMapper projectMapper, BidApprovalMapper bidApprovalMapper,
                                        StepServiceFactory stepServiceFactory,
            ApprovalTypeStepMappingMapper typeStepMappingMapper,
            TaskAndMessageService taskAndMessageService, UcOpenApiProperties ucOpenApiProperties) {
        super(projectMapper, bidApprovalMapper, stepServiceFactory, typeStepMappingMapper, taskAndMessageService,
              ucOpenApiProperties);
    }

    @Override
    public Integer currentStepNo() {
        return ApprovalStepEnumV2.MAIN_DATA.getNo();
    }


    @Override
    public boolean isComplete(BidApproval bidApproval) {
        return Boolean.TRUE;
    }


}
