package com.cscec3b.iti.projectmanagement.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.request.dict.SysDictTypeReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.dict.SysDictDataResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.dict.SysDictTypeResp;
import com.cscec3b.iti.projectmanagement.server.constant.CacheNames;
import com.cscec3b.iti.projectmanagement.server.entity.SysDictData;
import com.cscec3b.iti.projectmanagement.server.entity.SysDictType;
import com.cscec3b.iti.projectmanagement.server.mapper.SysDictDataMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.SysDictTypeMapper;
import com.cscec3b.iti.projectmanagement.server.service.SysDictTypeService;
import com.cscec3b.iti.projectmanagement.server.util.CacheUtils;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import com.github.pagehelper.page.PageMethod;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CachePut;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class SysDictTypeServiceImpl extends ServiceImpl<SysDictTypeMapper, SysDictType> implements SysDictTypeService {

    private final SysDictDataMapper dictDataMapper;

    @Override
    public Page<SysDictTypeResp> selectPageDictTypeList(SysDictTypeReq dictType) {
        final com.github.pagehelper.Page<SysDictType> pageInfo = PageMethod.startPage(dictType.getCurrent(),
                dictType.getSize()).doSelectPage(() ->
                this.list(Wrappers.<SysDictType>lambdaQuery()
                        .like(StringUtils.isNotBlank(dictType.getDictName()), SysDictType::getDictName,
                                dictType.getDictName()).like(StringUtils.isNotBlank(dictType.getDictType()),
                                SysDictType::getDictType, dictType.getDictType()).orderByDesc(SysDictType::getUpdateAt)));
        final List<SysDictType> result = pageInfo.getResult();
        final List<SysDictTypeResp> sysDictTypeResps = BeanUtil.copyToList(result, SysDictTypeResp.class);
        return new Page<SysDictTypeResp>(pageInfo.getTotal(), pageInfo.getPageNum(), pageInfo.getPageSize()).setRecords(sysDictTypeResps);
    }


    /**
     * 根据所有字典类型
     *
     * @return 字典类型集合信息
     */
    @Override
    public List<SysDictTypeResp> selectDictTypeAll() {
        final List<SysDictType> sysDictTypeList = this.list();
        return BeanUtil.copyToList(sysDictTypeList, SysDictTypeResp.class);
    }


    /**
     * 根据字典类型ID查询信息
     *
     * @param dictId 字典类型ID
     * @return 字典类型
     */
    @Override
    public SysDictTypeResp selectDictTypeById(Long dictId) {
        return BeanUtil.copyProperties(getById(dictId), SysDictTypeResp.class);
    }

    /**
     * 根据字典类型查询信息
     *
     * @param dictType 字典类型
     * @return 字典类型
     */
    @Override
    public SysDictTypeResp selectDictTypeByType(String dictType) {
        return BeanUtil.copyProperties(getOne(Wrappers.<SysDictType>lambdaQuery().eq(SysDictType::getDictType,
                dictType)), SysDictTypeResp.class);
    }

    /**
     * 批量删除字典类型信息
     *
     * @param dictIds 需要删除的字典ID
     */
    @Override
    public Boolean deleteDictTypeByIds(Long[] dictIds) {
        final List<SysDictType> sysDictTypeList = list(Wrappers.<SysDictType>lambdaQuery().in(SysDictType::getDictId,
                dictIds));
        for (final SysDictType sysDictType : sysDictTypeList) {
            final Integer count =
                    dictDataMapper.selectCount(Wrappers.<SysDictData>lambdaQuery().eq(SysDictData::getDictType,
                            sysDictType.getDictType()));
            if (count > 0) {
                throw new FrameworkException(-1, "该字典类型下有字典数据，不允许删除");
            }

        }
        return this.removeByIds(Arrays.asList(dictIds));
    }

    /**
     * 重置字典缓存数据
     */
    @Override
    public Boolean resetDictCache() {
        CacheUtils.clear(CacheNames.SYS_DICT);
        return true;
    }

    /**
     * 新增保存字典类型信息
     *
     * @param bo 字典类型信息
     * @return 结果
     */
    @CachePut(cacheNames = CacheNames.SYS_DICT, key = "#bo.dictType")
    @Override
    public List<SysDictDataResp> insertDictType(SysDictTypeReq bo) {
        final String newDictType = bo.getDictType();
        if (this.count(Wrappers.<SysDictType>lambdaQuery().eq(SysDictType::getDictType, newDictType)) > 0) {
            // todo 字典类型已存在
            throw new BusinessException(-1);
        }
        final SysDictType sydDictType = BeanUtil.copyProperties(bo, SysDictType.class);
        int row = baseMapper.insert(sydDictType);
        if (row > 0) {
            // 新增 type 下无 data 数据 返回空防止缓存穿透
            return new ArrayList<>();
        }
        throw new BusinessException(-1);
    }

    /**
     * 修改保存字典类型信息
     *
     * @param typeReq 字典类型信息
     * @return 结果
     */
    @CachePut(cacheNames = CacheNames.SYS_DICT, key = "#typeReq.dictType")
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SysDictDataResp> updateDictType(SysDictTypeReq typeReq) {
        SysDictType oldDict = baseMapper.selectById(typeReq.getDictId());
        final String newDictType = typeReq.getDictType();
        if (this.count(Wrappers.<SysDictType>lambdaQuery().eq(SysDictType::getDictType, newDictType)
                .ne(SysDictType::getDictType, newDictType)) > 1) {
            // todo 字典类型已存在
            throw new BusinessException(-1);
        }
        SysDictType dict = BeanUtil.copyProperties(typeReq, SysDictType.class);
        int row = baseMapper.updateById(dict);
        if (row > 0) {
            CacheUtils.evict(CacheNames.SYS_DICT, oldDict.getDictType());
            if (!Objects.equals(oldDict.getDictType(), dict.getDictType())) {
                dictDataMapper.update(null, new LambdaUpdateWrapper<SysDictData>()
                        .set(SysDictData::getDictType, dict.getDictType())
                        .set(SysDictData::getUpdateAt, Instant.now().toEpochMilli())
                        .set(SysDictData::getUpdateBy, LoginUserUtil.userId())
                        .eq(SysDictData::getDictType, oldDict.getDictType()));
            }
            return BeanUtil.copyToList(dictDataMapper.selectList(Wrappers.<SysDictData>lambdaQuery()
                    .eq(SysDictData::getDictType, typeReq.getDictType())), SysDictDataResp.class);
        }
        throw new BusinessException(-1);
    }

    /**
     * 校验字典类型称是否唯一
     *
     * @param dictType 字典类型
     * @return 结果
     */
    @Override
    public boolean checkDictTypeUnique(SysDictTypeReq dictType) {
        boolean exist = baseMapper.selectCount(new LambdaQueryWrapper<SysDictType>()
                .eq(SysDictType::getDictType, dictType.getDictType())
                .ne(ObjectUtil.isNotNull(dictType.getDictId()), SysDictType::getDictId, dictType.getDictId())) > 0;
        return !exist;
    }

}
