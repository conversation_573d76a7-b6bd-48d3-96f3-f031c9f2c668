//package com.cscec3b.iti.projectmanagement.server.service.impl;
//
//import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ApprovalStepReq;
//import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.StepOfAssignmentTypeReq;
//import com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.ApprovalTypeSubscribeMappingResp;
//import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
//import com.cscec3b.iti.projectmanagement.server.enums.ApprovalStepEnum;
//import com.cscec3b.iti.projectmanagement.server.mapper.BidApprovalMapper;
//import com.cscec3b.iti.projectmanagement.server.service.AbstractApprovalStepService;
//import com.cscec3b.iti.projectmanagement.server.service.ApprovalTypeSubscribeMappingService;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.List;
//
/// **
// * 立项步骤-业务系统数据同步
// *
// * <AUTHOR>
// * @date 2024/01/03
// */
//@Slf4j
//@Service("endService")
//@AllArgsConstructor
//@Transactional(rollbackFor = Exception.class)
//public class ApprovalStepOfEndServiceImpl extends AbstractApprovalStepService {
//
//    /**
//     * 中标未立项服务
//     */
//    private final BidApprovalMapper bidApprovalMapper;
//
//    /**
//     * 业务系统-类型订阅映射服务
//     */
//    private final ApprovalTypeSubscribeMappingService subscribeMappingService;
//
//    @Override
//    public Integer currentStep() {
//        return ApprovalStepEnum.END.getNo();
//    }
//
//
//    /**
//     * 结束步骤不需要再往下了
//     *
//     * @param bidApproval 中标未立项
//     */
//    @Override
//    public void submitApprovalStep(BidApproval bidApproval) {
//        getBidApprovalMapper().updateById(bidApproval);
//    }
//
//    /**
//     * 提交中标未立项项目类型信息
//     *
//     * @param stepReq 请求参数
//     * @return boolean
//     */
//
//    @Override
//    public boolean saveCurrentStep(ApprovalStepReq stepReq) {
//        final BidApproval bidApproval = this.bidApprovalMapper.selectById(stepReq.getBidApprovalId());
//        this.submitApprovalStep(bidApproval);
//        return true;
//    }
//
//    public List<ApprovalTypeSubscribeMappingResp> getSubscribeMappingByTypeId(
//            StepOfAssignmentTypeReq stepOfAssignmentTypeReq) {
//        final Long approvalTypeId = stepOfAssignmentTypeReq.getApprovalTypeId();
//        return subscribeMappingService.getSubscribeMappingByTypeId(approvalTypeId);
//    }
//
//
//}
