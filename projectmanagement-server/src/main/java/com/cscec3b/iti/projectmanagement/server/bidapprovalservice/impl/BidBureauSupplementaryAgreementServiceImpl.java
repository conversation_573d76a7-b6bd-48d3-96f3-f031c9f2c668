package com.cscec3b.iti.projectmanagement.server.bidapprovalservice.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.model.resp.ContractFileRelationResp;
import com.cscec3b.iti.projectmanagement.api.bidapproval.dto.request.BidBureauSupplementaryAgreementFileReq;
import com.cscec3b.iti.projectmanagement.api.dto.dto.CommonProjectFieldDto;
import com.cscec3b.iti.projectmanagement.api.dto.request.ContractFilePageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ContractFileUpdateParamReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.ContractFileDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.ContractFilePageResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.EngineeringProjectBaseResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.BureauSupplementaryAgreementResp;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.BidFilePreHandler;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidBureauSupplementaryAgreementService;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
import com.cscec3b.iti.projectmanagement.server.entity.BureauSupplementaryAgreement;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.BureauSupplementaryAgreementMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.service.*;
import com.github.pagehelper.page.PageMethod;
import com.odin.freyr.common.orika.BeanMapUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 局内补充协议/局内无合同续签服务
 *
 * <AUTHOR>
 * @date 2023/12/12
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class BidBureauSupplementaryAgreementServiceImpl extends ServiceImpl<BureauSupplementaryAgreementMapper,
        BureauSupplementaryAgreement> implements BidFilePreHandler<BidBureauSupplementaryAgreementFileReq>,
        IBidBureauSupplementaryAgreementService {

    /**
     * 项目service
     */
    private final ProjectService projectService;

    /**
     * 项目mapper
     */
    private final ProjectMapper projectMapper;

    /**
     * id生成器
     */
    private final IdTableService idService;

    /**
     * 项目进度service
     */
    private final ProjectProgressService projectProgressService;

    /**
     * 事件发布器
     */
    private final ApplicationEventPublisher publisher;

    /**
     * 任务service
     */
    private final ITaskService taskService;

    /**
     * 三局通service
     */
    private final IPmPortalMsgService pmPortalMsgService;

    @Override
    public IndContractsTypeEnum doSaveFile(final String preFileId, final String curFileType,
            final BidBureauSupplementaryAgreementFileReq data) {
        log.info("市场营销局内补充协议录入entry=====>>MarketProReq<BureauSupplementaryAgreementReq>: {}", data);
        // 1、将传入的市场营销数据转换成补充协议对象并插入补充协议表

        // 合同数据
        BureauSupplementaryAgreement bsa = new BureauSupplementaryAgreement();
        // 有前置文件则是局内部无合同续签补充协议，有前置文件则为局内部补充协议
        int belongFileType;
        if (StringUtils.isNotBlank(preFileId)) {
            belongFileType = IndContractsTypeEnum.INTERNAL_AGREEMENT.getDictCode();
            bsa.setPreFileId(Long.valueOf(preFileId));
            // 如果是局内补充协议，则前置文件为局内部合同定案
            bsa.setPreFileType(IndContractsTypeEnum.INTERNAL_PRESENTATION.getDictCode());
        } else {
            belongFileType = IndContractsTypeEnum.NO_CONTRACT_INTERNAL_AGREEMENT.getDictCode();
        }
        bsa.setBelongFileType(belongFileType);
        BeanUtils.copyProperties(data, bsa);
        bsa.setBureauSupplementaryAgreementCode(bsa.getAgreementCode()).setSource(Constants.NUMBER_ONE);
        // 填充业务板块
        // 局内部文件暂无局标准分类及综合口径
//        final String standardType = Joiner.on(Constants.ID_PATH_CONNECTOR).skipNulls().join(entity.getStandardType1(),
//                bsa.getStandardType2(), bsa.getStandardType3(), bsa.getStandardType4());
//        final String projectType = StringUtils.isEmpty(bsa.getProjectType()) ? "" : bsa.getProjectType()
//                + (StringUtils.isEmpty(
//                bsa.getProjectType2()) ? "" : Constants.MODE_CONNECTOR + bsa.getProjectType2())
//                + (StringUtils.isEmpty(
//                bsa.getProjectType3()) ? "" : Constants.MODE_CONNECTOR + bsa.getProjectType3())
//                + (StringUtils.isEmpty(
//                bsa.getProjectType4()) ? "" : Constants.MODE_CONNECTOR + bsa.getProjectType4());
//        String businessSegmentCodePath = genBusinessSegmentCodePathInFile(standardType, projectType);
//        bsa.setBusinessSegmentCodePath(businessSegmentCodePath);
        bsa.setUpdateAt(Instant.now().toEpochMilli());
        saveOrUpdate(bsa,
                Wrappers.<BureauSupplementaryAgreement>lambdaUpdate().eq(BureauSupplementaryAgreement::getBelongId,
                        bsa.getBelongId()));
        // 中标未立项完全迁移后 启用
        // if (Boolean.TRUE.equals(existFileByBelongId(bsa.getBelongId()))) {
        //     throw new BusinessException(8010018);
        // }
        // if (this.baseMapper.insert(bsa) != Constants.NUMBER_ONE) {
        //     throw new BusinessException(8010008, new String[]{"局内补充协议"});
        // }
        return IndContractsTypeEnum.getEnumByCode(belongFileType);
    }

    @Override
    public Boolean existFileByBelongId(final Long belongId) {
        return this.baseMapper.selectCountByBelongId(belongId) > Constants.NUMBER_ZERO;
    }


    @Override
    public List<ContractFileRelationResp> getNextFileListByPreFileId(Long preFileId, Integer preFileType) {
        final List<BureauSupplementaryAgreement> supplementaryAgreements =
                this.baseMapper.selectList(new LambdaQueryWrapper<BureauSupplementaryAgreement>()
                        .eq(BureauSupplementaryAgreement::getPreFileId, preFileId)
                        .eq(BureauSupplementaryAgreement::getPreFileType, preFileType));
        return supplementaryAgreements.stream().map(BidBureauSupplementaryAgreementServiceImpl::genContractFileRelationResp)
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 局内补充协议/局内无合同续签转换为中标未立项信息
     *
     * @param bidApproval 中标未立项信息
     * @param data        请求参数
     */
    @Override
    public void convertToBidApproval(BidApproval bidApproval, final BidBureauSupplementaryAgreementFileReq data) {
        BeanUtil.copyProperties(data, bidApproval);
    }

    @Override
    public Page<ContractFilePageResp> getContractFilePageList(final ContractFilePageReq pageReq,
            IndContractsTypeEnum scopeType) {
        final com.github.pagehelper.Page<ContractFilePageResp> page =
                PageMethod.startPage(pageReq.getCurrent(), pageReq.getSize()).doSelectPage(() ->
                        this.baseMapper.pageList(pageReq, scopeType));
        final List<ContractFilePageResp> result = page.getResult();
        return new Page<ContractFilePageResp>(page.getTotal(), pageReq.getCurrent(), pageReq.getSize()).setRecords(result);
    }

    /**
     * 局内部合同定案没有前置文件
     *
     * @param id 文件id
     * @return {@link ContractFileRelationResp}
     */
    @Override
    public ContractFileRelationResp getFileInfoById(final Long id) {
        final BureauSupplementaryAgreement bureauSupplementaryAgreement = this.baseMapper.selectById(id);
        return genContractFileRelationResp(bureauSupplementaryAgreement);
    }


    @Override
    public ContractFileRelationResp getFileInfoByBelongId(Integer belongFileType, final Long belongId, Long excludeId) {
        final BureauSupplementaryAgreement bureauSupplementaryAgreement =
                this.baseMapper.selectOne(new LambdaQueryWrapper<BureauSupplementaryAgreement>()
                        .eq(BureauSupplementaryAgreement::getBelongFileType, belongFileType)
                        .eq(BureauSupplementaryAgreement::getBelongId, belongId)
                        .ne(Objects.nonNull(excludeId), BureauSupplementaryAgreement::getId, excludeId));
        return genContractFileRelationResp(bureauSupplementaryAgreement);
    }

    /**
     * 填充ContractFileRelationResp
     *
     * @param bureauSupplementaryAgreement 局内补充协议/局内无合同续签
     * @return {@link ContractFileRelationResp}
     */
    private static ContractFileRelationResp genContractFileRelationResp(
            final BureauSupplementaryAgreement bureauSupplementaryAgreement) {
        return Optional.ofNullable(bureauSupplementaryAgreement).map(bc -> {
            final ContractFileRelationResp contractFileRelationResp = new ContractFileRelationResp();
            contractFileRelationResp.setBelongFileType(bc.getBelongFileType())
                    .setBelongFileTypeCode(IndContractsTypeEnum.getEnumByCode(bc.getBelongFileType()).getEnUS())
                    .setBelongFileTypeName(IndContractsTypeEnum.getEnumByCode(bc.getBelongFileType()).getZhCN())
                    .setBelongId(bc.getBelongId()).setFileCode(bc.getAgreementCode()).setId(bc.getId())
                    .setPreFileId(bc.getPreFileId()).setPreFileType(bc.getPreFileType()).setProjectName(bc.getProjectName());
            return contractFileRelationResp;
        }).orElse(null);
    }

    @Override
    public Long createProject(BidApproval bidApproval) {
        log.info("局内(无合同续签)补充协议立项");
        final IndContractsTypeEnum contractsTypeEnum = IndContractsTypeEnum.getEnumCode(bidApproval.getType());
        if (Objects.isNull(contractsTypeEnum)) {
            throw new FrameworkException(-1, "文件类型错误");
        }
        // 检查是否重复立项
        final Project projectByBidId =
                projectMapper.selectOne(Wrappers.<Project>lambdaQuery().eq(Project::getIndependentContractId,
                        bidApproval.getId()).eq(Project::getIndependentContractType,
                        IndContractsTypeEnum.INTERNAL_PRESENTATION.getDictCode()));
        if (Objects.nonNull(projectByBidId)) {
            throw new BusinessException(8010006);
        }
        final BureauSupplementaryAgreement bureauSupplementaryAgreement =
                this.getOne(Wrappers.<BureauSupplementaryAgreement>lambdaQuery().eq(BureauSupplementaryAgreement::getBelongId,
                        bidApproval.getBelongId()));
        if (Objects.isNull(bureauSupplementaryAgreement)) {
            throw new BusinessException(8010320);
        }

        // 更新文件信息
        bureauSupplementaryAgreement.setIndependentContractId(bidApproval.getId())
                .setIndependentContractType(contractsTypeEnum.getDictCode())
                .setIndependent(bidApproval.getIndependentProject());
        this.updateById(bureauSupplementaryAgreement);

        // 创建项目
        final Project project = BeanUtil.copyProperties(bureauSupplementaryAgreement, Project.class);
        project.setIndependentContractId(bidApproval.getId())
                .setIndependentContractType(contractsTypeEnum.getDictCode())
                .setIndependentContractNo(bureauSupplementaryAgreement.getAgreementCode());

        final String cpmMark = idService.getCpmProjectKey();
        project.setCpmProjectKey(cpmMark).setCpmProjectName(bureauSupplementaryAgreement.getProjectName())
                .setCpmProjectAbbreviation(bureauSupplementaryAgreement.getProjectName());
        this.setProjectProperties(project, BeanUtil.copyProperties(bureauSupplementaryAgreement,
                        CommonProjectFieldDto.class),
                bidApproval.getId(), contractsTypeEnum);
        // 质量奖罚条款 付款进度字段不同,单独设置
        project.setAdvancesWay(bureauSupplementaryAgreement.getAdvancesWay())
                .setPaymentTypeCode(bureauSupplementaryAgreement.getAdvancesWayCode());
        // 计算金额
        // projectService.recalculationProjectAmounts(project);
        projectService.recalculationProjectAmountsV3(project);
        projectMapper.insert(project);

        // 同步创建项目进度
        projectProgressService.createProgressByApproval(contractsTypeEnum, project, true);

        // log.info("市场营销补充协议立项:1.推送财商系统立项--->2.推送智慧工地立项");
        // // 触发项目流转事件(市场营销板块立项 后置事件)
        // publisher.publishEvent(new CpmProjectFlowEvent(this, project.getId(), FlowNodeEnum.MARKETING_SEGMENT,
        //         FlowNodeHandlerEnum.POST));

        return project.getId();

    }

    @Override
    public ContractFileDetailResp<?> getFileDetailByBelongId(Long belongId) {
        final BureauSupplementaryAgreement bsa =
                this.baseMapper.selectOne(new LambdaQueryWrapper<BureauSupplementaryAgreement>()
                        .eq(BureauSupplementaryAgreement::getBelongId, belongId));
        if (null == bsa) {
            throw new BusinessException(8010081, new String[]{"局内补充协议"});
        }
        BureauSupplementaryAgreementResp bsaResp = new BureauSupplementaryAgreementResp();
        BeanUtils.copyProperties(bsa, bsaResp);
        return new ContractFileDetailResp<>(bsaResp);
    }

    /**
     * 挂接项目
     *
     * @param independentContractId   独立id
     * @param independentContractType 合同类型
     * @param belongId                文件所属id
     * @param independentProject      独立项目
     * @return {@link Boolean}
     */
    @Override
    public Boolean hookProject(Long independentContractId, Integer independentContractType,
            Long belongId, String independentProject) {
        this.baseMapper.update(null,
                Wrappers.<BureauSupplementaryAgreement>lambdaUpdate().set(BureauSupplementaryAgreement::getIndependentContractId,
                                independentContractId).set(BureauSupplementaryAgreement::getIndependentContractType,
                                independentContractType)
                        .set(BureauSupplementaryAgreement::getUpdateAt, System.currentTimeMillis())
                        .eq(BureauSupplementaryAgreement::getBelongId, belongId));
        return Boolean.TRUE;
    }

    @Override
    public List<BureauSupplementaryAgreementResp> getFileDetail(Integer belongFileType, String belongId,
                                                                String fileCode, Long id,
                                                                String yunshuExecuteUnitIdPath) {
        List<BureauSupplementaryAgreement> bureauSupplementaryAgreement = this.baseMapper.selectList(Wrappers.<BureauSupplementaryAgreement>lambdaQuery()
                .likeRight(BureauSupplementaryAgreement::getYunshuExecuteUnitIdPath, yunshuExecuteUnitIdPath)
                .likeRight(StringUtils.isNotBlank(fileCode), BureauSupplementaryAgreement::getBureauSupplementaryAgreementCode, fileCode)
                .eq(StringUtils.isNotBlank(belongId), BureauSupplementaryAgreement::getBelongId, belongId)
                .eq(BureauSupplementaryAgreement::getBelongFileType, belongFileType)
                .eq(Objects.nonNull(id), BureauSupplementaryAgreement::getId, id)
                .orderByDesc(BureauSupplementaryAgreement::getUpdateAt)
                .last("limit 20"));
        return BeanMapUtils.mapList(bureauSupplementaryAgreement, BureauSupplementaryAgreement.class, BureauSupplementaryAgreementResp.class);
    }

    @Override
    public boolean updateRelationFileByBelongId(String curFileBelongId, Integer curFileBelongType, String preFileId,
                                                Integer preFileType) {
        BureauSupplementaryAgreement bureauSupplementaryAgreement = Optional.ofNullable(
                        this.baseMapper.selectOne(Wrappers.<BureauSupplementaryAgreement>lambdaQuery()
                                .eq(BureauSupplementaryAgreement::getBelongId, curFileBelongId)
                                .eq(BureauSupplementaryAgreement::getBelongFileType, curFileBelongType)))
                .orElseThrow(() -> new BusinessException(8010320));
        if (Objects.nonNull(bureauSupplementaryAgreement.getPreFileId())
                && !Objects.equals(String.valueOf(bureauSupplementaryAgreement.getPreFileId()), preFileId)) {
            throw new FrameworkException(-1, "关联文件已经关联了其他文件，无法再次关联");
        }
        bureauSupplementaryAgreement.setPreFileId(Long.valueOf(preFileId)).setPreFileType(preFileType);
        return this.updateById(bureauSupplementaryAgreement);
    }

    @Override
    public boolean commonUpdateData(ContractFileUpdateParamReq paramReq, Long BelongId) {

        return false;
    }

    @Override
    public Map<String, Object> getMapByBelongId(Long belongId) {
        return getMap(Wrappers.<BureauSupplementaryAgreement>lambdaQuery()
                .eq(BureauSupplementaryAgreement::getBelongId, belongId));
    }

    @Override
    public EngineeringProjectBaseResp getEngineeringProjectBaseInfo(Long belongId) {
        BureauSupplementaryAgreement bureauSupplementaryAgreement = this.baseMapper.selectOne(Wrappers.<BureauSupplementaryAgreement>lambdaQuery()
                .eq(BureauSupplementaryAgreement::getBelongId, belongId));
        EngineeringProjectBaseResp resp = new EngineeringProjectBaseResp();
        resp.setProjectCategory(bureauSupplementaryAgreement.getBusinessType());
        return resp;
    }
}
