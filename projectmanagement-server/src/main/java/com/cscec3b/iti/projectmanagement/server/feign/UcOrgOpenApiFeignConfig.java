package com.cscec3b.iti.projectmanagement.server.feign;

import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.server.config.UcOpenApiProperties;
import com.fasterxml.jackson.databind.JsonNode;
import feign.FeignException;
import feign.Logger;
import feign.RequestInterceptor;
import feign.Response;
import feign.RetryableException;
import feign.Retryer;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.time.Instant;
import java.util.Collection;
import java.util.Collections;

import static cn.hutool.crypto.SecureUtil.md5;

/**
 * 六统一 feign config
 *
 * <AUTHOR>
 * @date 2023/10/23
 */


@Slf4j
public class UcOrgOpenApiFeignConfig {


    /**
     * feign 日志级别
     *
     * @return {@link Logger.Level }
     * <AUTHOR>
     * @date 2023/10/23
     */
    @Bean
    public Logger.Level xindunLogger() {
        return Logger.Level.FULL;
    }

    /**
     * 重试策略
     *
     * @return {@link Retryer }
     * <AUTHOR>
     * @date 2023/10/23
     */
    @Bean
    public Retryer xindunRetryer() {
        return new Retryer.Default(2 * 60 * 1000L, 2 * 60 * 1000L, 3);
    }

    // 透传请求头
    @Bean
    @Primary
    public RequestInterceptor xindunRequestInterceptor() {
        return requestTemplate -> {
            final Collection<String> treeTypes = requestTemplate.headers().getOrDefault("tree-type",
                    Collections.singleton(UcOpenApiProperties.MARKETING));

            final String treeType = treeTypes.iterator().next();

            final String clientId = UcOpenApiProperties.appTable.get(treeType, "clientId");
            final String clientSecret = UcOpenApiProperties.appTable.get(treeType, "clientSecret");
            final String currentTimes = String.valueOf(Instant.now().toEpochMilli());
            final String sign = generateSign(currentTimes, clientId, clientSecret);
            requestTemplate.header("X-APP-ID", clientId);
            requestTemplate.header("X-REQUEST-TIMESTAMP", currentTimes);
            requestTemplate.header("X-REQUEST-SIGN", sign);
        };
    }

    public String generateSign(String timestamp, String clientId, String clientSecret) {
        // appId={{应用ID}}&timeStamp={{时间戳}}&key={{应用密钥}}
        // 按以上格式拼接字符串
        final String formatStr = String.format("appId=%s&timeStamp=%s&key=%s", clientId, timestamp, clientSecret);
        return md5(formatStr).toUpperCase();
    }


    /**
     * 返回异常处理
     *
     * @return {@link ErrorDecoder }
     * <AUTHOR>
     * @date 2023/10/23
     */
    @Bean
    public ErrorDecoder xindunFeignErrorDecoder() {
        return new UcOrgOpenApiFeignConfig.FeignErrorDecoder();
    }

    /**
     * <AUTHOR>
     * @date 2023/10/23
     */// 处理返回的异常
    public static class FeignErrorDecoder extends ErrorDecoder.Default {
        @Override
        public Exception decode(String methodKey, Response response) {

            log.error("【错误】 has exception");
            Exception exception = super.decode(methodKey, response);
            // RetryableException 异常不作处理
            if (exception instanceof RetryableException) {
                return exception;
            }
            try {
                // 如果是FeignException，则对其进行处理，并抛出BusinessException
                if (exception instanceof FeignException && ((FeignException) exception).responseBody().isPresent()) {
                    ByteBuffer responseBody = ((FeignException) exception).responseBody().get();
                    String bodyText =
                            StandardCharsets.UTF_8.newDecoder().decode(responseBody.asReadOnlyBuffer()).toString();
                    final JsonNode jsonNode = JsonUtils.parseStringToJson(bodyText);
                    if (jsonNode != null && jsonNode.has("code") && jsonNode.has("message")) {
                        return new FrameworkException(jsonNode.get("code").asInt(), jsonNode.get("message").asText());
                    } else {
                        return new FrameworkException(-1, bodyText);
                    }
                }
            } catch (Exception ex) {
                log.error(ex.getMessage(), ex);
            }
            return exception;
        }

    }

}
