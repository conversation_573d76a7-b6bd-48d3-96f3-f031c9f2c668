package com.cscec3b.iti.projectmanagement.server.config;


import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import javax.annotation.PreDestroy;
import javax.annotation.Resource;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

import brave.Span;
import brave.Tracer;
import brave.propagation.CurrentTraceContext;
import brave.propagation.ThreadLocalCurrentTraceContext;
import brave.propagation.TraceContext;
import io.netty.util.concurrent.DefaultThreadFactory;
import lombok.extern.slf4j.Slf4j;

/**
 * 自定义线程池配置
 *
 * <AUTHOR>
 * @date 2023/1/11 10:45
 */

@Slf4j
@Configuration
public class CusExecutorConfig {

    private final List<ExecutorService> executors = new ArrayList<>();

    @Resource
    private Tracer tracer;

    /**
     * 全局线程池倒置
     *
     * @return Executor
     */
    @Bean
    public Executor cpmTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程池大小
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2);
        // 最大线程数
        executor.setMaxPoolSize(64);
        // 队列容量
        executor.setQueueCapacity(200);
        // 活跃时间
        executor.setKeepAliveSeconds(120);
        // 线程名字前缀
        executor.setThreadNamePrefix("cpmTask-");

        // setRejectedExecutionHandler：当pool已经达到max size的时候，如何处理新任务
        // CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadFactory(new TracingThreadFactory("cpmTask-", new DefaultThreadFactory("cpmTaskExecutor"),tracer));
        // executor.setTaskDecorator(new MdcTaskDecorator());
        executor.initialize();
        executors.add(executor.getThreadPoolExecutor());
        return executor;
    }

    /**
     * 消息推送线程池配置
     * @return {@link Executor }
     * <AUTHOR>
     * @date 2023/10/12
     */
    @Bean
    public Executor cpmPushTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        // 核心线程池大小
        executor.setCorePoolSize(32);
        // 最大线程数
        executor.setMaxPoolSize(256);
        // 队列容量
        executor.setQueueCapacity(800);
        // 活跃时间
        executor.setKeepAliveSeconds(60);
        // 线程名字前缀
        executor.setThreadNamePrefix("cpmPushTask-");

        // setRejectedExecutionHandler：当pool已经达到max size的时候，如何处理新任务
        // CallerRunsPolicy：不在新线程中执行任务，而是由调用者所在的线程来执行
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setThreadFactory(new TracingThreadFactory("cpmPushTask-", new DefaultThreadFactory(
                "cpmPushTaskExecutor"), tracer));
        // executor.setTaskDecorator(new MdcTaskDecorator());
        executor.initialize();
        executors.add(executor.getThreadPoolExecutor());
        return executor;
    }

    /**
     * 单线程池配置
     *
     * @return executor
     */
    @Bean
    public Executor cpmSingleTaskExecutor() {
        ThreadFactory threadFactory =
                new ThreadFactoryBuilder().setNameFormat("cpmSingleTask-%d").setPriority(Thread.NORM_PRIORITY).build();
        final TracingThreadFactory factory = new TracingThreadFactory("cpmSingleTask-", threadFactory, tracer);
        final ExecutorService executor = Executors.newSingleThreadExecutor(factory);
        executors.add(executor);
        return executor;

    }

    /**
     * 延时线程池配置
     * @return ScheduledExecutorService
     */
    @Bean
    public ScheduledExecutorService cpmScheduledExecutor() {
        ThreadFactory threadFactory = new ThreadFactoryBuilder()
                .setNameFormat("cpmScheduledTask-%d")
                .setPriority(Thread.NORM_PRIORITY)
                .setDaemon(true)
                .build();
        final TracingThreadFactory factory = new TracingThreadFactory("cpmScheduledTask-", threadFactory, tracer);

        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(
                Runtime.getRuntime().availableProcessors() * 2,
                factory,
                new ThreadPoolExecutor.CallerRunsPolicy()
        );

        // 配置核心线程超时
        executor.setKeepAliveTime(60, TimeUnit.SECONDS);
        executor.allowCoreThreadTimeOut(true);

        // 配置任务完成后移除
        executor.setRemoveOnCancelPolicy(true);

        // 预启动所有核心线程
        executor.prestartAllCoreThreads();

        // 设置最大线程数
        executor.setMaximumPoolSize(200);
        executors.add(executor);
        return executor;
    }

    /**
     * 定时任务线程池配置
     * @return {@link TaskScheduler }
     */
    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(8); // 设置线程池大小
        // scheduler.setThreadNamePrefix("TaskScheduler-"); // 设置线程名称前缀
        final TracingThreadFactory factory = new TracingThreadFactory("TaskScheduler", new DefaultThreadFactory(
                "taskScheduler"), tracer);
        scheduler.setThreadFactory(factory);
        scheduler.initialize();
        executors.add(scheduler.getScheduledExecutor());
        return scheduler;
    }

    /**
     *
     */
    @PreDestroy
    public void shutdownExecutors() {
        log.info("Shutting down executor services");
        for (ExecutorService executor : executors) {
            shutdownExecutor(executor);
        }
    }

    private void shutdownExecutor(ExecutorService executor) {
        try {
            log.info("Shutting down executor service: {}", executor.toString());
            executor.shutdown();
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow();
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    log.error("Thread pool did not terminate");
                }
            }
        } catch (InterruptedException ie) {
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }


    public static class TracingThreadFactory implements ThreadFactory {

        private final Tracer tracer;

        private final ThreadFactory backingThreadFactory;
        private final AtomicInteger threadNumber = new AtomicInteger(1);
        private final CurrentTraceContext currentTraceContext = ThreadLocalCurrentTraceContext.create();
        private final String namePrefix;

        public TracingThreadFactory(String namePrefix, ThreadFactory backingThreadFactory, Tracer tracer) {
            this.backingThreadFactory = backingThreadFactory;
            this.namePrefix = namePrefix;
            this.tracer = tracer;
        }

        @Override
        public Thread newThread(Runnable r) {
            Span currentSpan = tracer.currentSpan();
            if (currentSpan != null) {
                final TraceContext context = currentSpan.context();
                Runnable runnableWithTraceContext = () -> {
                    try (CurrentTraceContext.Scope scope = currentTraceContext.newScope(context)) {
                        r.run();
                    }
                };
                Thread thread = backingThreadFactory.newThread(runnableWithTraceContext);
                thread.setName(namePrefix + threadNumber.getAndIncrement());
                return thread;
            } else {
                log.warn("Current Span is null, cannot propagate TraceContext.");
                return backingThreadFactory.newThread(r);
            }
        }
    }


    // public class MdcTaskDecorator implements TaskDecorator {
    //
    //     @Override
    //     public Runnable decorate(Runnable runnable) {
    //         // 获取当前线程的 MDC 上下文
    //         Map<String, String> contextMap = MDC.getCopyOfContextMap();
    //         return () -> {
    //             try {
    //                 // 将 MDC 上下文设置到新线程中
    //                 MDC.setContextMap(contextMap);
    //                 runnable.run();
    //             } finally {
    //                 // 清理 MDC 上下文
    //                 MDC.clear();
    //             }
    //         };
    //     }
    // }


}
