//package com.cscec3b.iti.projectmanagement.server.service.impl;
//
//import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ApprovalStepReq;
//import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.StepOfAssignmentTypeReq;
//import com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.ApprovalTypeSubscribeMappingResp;
//import com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.BusinessSystemDataPushInfoResp;
//import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
//import com.cscec3b.iti.projectmanagement.server.enums.ApprovalStepEnum;
//import com.cscec3b.iti.projectmanagement.server.enums.IndependTypeEnum;
//import com.cscec3b.iti.projectmanagement.server.mapper.BidApprovalMapper;
//import com.cscec3b.iti.projectmanagement.server.service.AbstractApprovalStepService;
//import com.cscec3b.iti.projectmanagement.server.service.ApprovalTypeSubscribeMappingService;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.List;
//import java.util.Objects;
//
/// **
// * 立项步骤-业务系统数据同步
// *
// * <AUTHOR>
// * @date 2024/01/03
// */
//@Slf4j
//@Service("businessSysDataService")
//@AllArgsConstructor
//@Transactional(rollbackFor = Exception.class)
//public class ApprovalStepOfBusinessSysDataServiceImpl extends AbstractApprovalStepService {
//
//    /**
//     * 中标未立项服务
//     */
//    private final BidApprovalMapper bidApprovalMapper;
//
//    /**
//     * 业务系统-类型订阅映射服务
//     */
//    private final ApprovalTypeSubscribeMappingService subscribeMappingService;
//
//    @Override
//    public Integer currentStep() {
//        return ApprovalStepEnum.BUSINESS_DATA.getNo();
//    }
//
//    /**
//     * 提交中标未立项项目类型信息
//     *
//     * @param stepReq 请求参数
//     * @return boolean
//     */
//
//    @Override
//    public boolean saveCurrentStep(ApprovalStepReq stepReq) {
//        final BidApproval bidApproval = this.bidApprovalMapper.selectById(stepReq.getBidApprovalId());
//        submitApprovalStep(bidApproval);
//        return true;
//    }
//
//    @Override
//    public boolean isAutoSubmit() {
//        final String name = this.getClass().getName();
//        log.info("isAutoSubmit_name : [{}], isAutoSubmit: {}", name, true);
//        return true;
//    }
//
//    public List<ApprovalTypeSubscribeMappingResp> getSubscribeMappingByTypeId(
//            StepOfAssignmentTypeReq stepOfAssignmentTypeReq) {
//        final Long approvalTypeId = stepOfAssignmentTypeReq.getApprovalTypeId();
//        return subscribeMappingService.getSubscribeMappingByTypeId(approvalTypeId);
//    }
//
//    /**
//     * 集团主数据状态：财商信息，项目部信息
//     *
//     * @param bidApprovalId 中标未立项id
//     * @return {@link List}<{@link ApprovalTypeSubscribeMappingResp}>
//     */
//    public List<BusinessSystemDataPushInfoResp> businessSystemDataPushInfo(Long bidApprovalId) {
//        final BidApproval bidApproval = bidApprovalMapper.selectById(bidApprovalId);
//        // 不存在或者不是独立立项，或者没有项目id则返回null,
//        if (Objects.isNull(bidApproval) || !Objects.equals(bidApproval.getIndependentProject(),
//                IndependTypeEnum.INDEPENDENT.getCode()) || Objects.isNull(bidApproval.getCpmProjectId())) {
//            return null;
//        }
//        return bidApprovalMapper.businessSystemDataPushInfo(bidApproval.getCpmProjectId());
//    }
//}
