package com.cscec3b.iti.projectmanagement.server.util;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.Map;

import com.cscec3b.iti.projectmanagement.server.config.SmartSiteProperties;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import cn.pinming.openapi.client.invoke.EncryptUtils;
import cn.pinming.openapi.client.invoke.FormData;
import lombok.extern.slf4j.Slf4j;

/**
 * 智慧工地请求接口参数
 */
@Slf4j
public class OpenApiInvoker {


    /**
     * @param smartSiteProperties 接口属性
     * @param smartSiteReq        请求数据
     * @return 加密结果
     */
    public static Map<String, Object> encryptData(SmartSiteProperties smartSiteProperties, Object smartSiteReq) {
        log.info("encryptData==>>smartSiteProperties:{}, smartSiteReq:{}", smartSiteProperties, smartSiteReq);
        //1.组装业务数据对象
        //2.对象转json
        String jsonStr = JSONUtil.toJsonStr(smartSiteReq);
        //3.字符串编码
        String encodeStr = encode(jsonStr);
        //4.aes加密
        String encryptStr = EncryptUtils.aesEncrypt(smartSiteProperties.getSecretKey(), encodeStr);
        //5.组装请求对象
        FormData data = new FormData();
        data.setItype(smartSiteProperties.getEndPoint());
        data.setTime(DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN));
        data.setData(encryptStr);
        data.setApiGroup(smartSiteProperties.getGroup());
        //加密类型：1-AES; 2-BASE64
        data.setEtype(1);
        // 开发者类型： 1 企业开发者;2 平台开发者，默认为空代表企业开发者
        data.setDtype(2);
        data.setDkey(smartSiteProperties.getAppKey());
        //6.按字段顺序加密
        String originalString = data.getItype() + data.getTime() + data.getData() + smartSiteProperties.getSecretKey();
        String sign = EncryptUtils.md5Encrypt(originalString);
        data.setSign(sign);
        //7.对象转map
        return BeanUtil.beanToMap(data);
    }


    /**
     * 加密数据
     *
     * @param smartSiteProperties smartSiteProperties
     * @param requestObj          requestObj
     * @param endpoint            端点
     * @return {@link Map}<{@link String}, {@link Object}>
     */
    public static Map<String, Object> encryptData(SmartSiteProperties smartSiteProperties, Object requestObj, String endpoint) {
        smartSiteProperties.setEndPoint(endpoint);
        return encryptData(smartSiteProperties, requestObj);
    }

    private static String encode(String s) {
        try {
            if (s == null) {
                return null;
            }
            return URLEncoder.encode(s, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }
    }

}
