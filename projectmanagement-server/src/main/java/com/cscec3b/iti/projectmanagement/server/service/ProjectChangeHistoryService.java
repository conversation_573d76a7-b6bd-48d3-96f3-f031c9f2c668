package com.cscec3b.iti.projectmanagement.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cscec3b.iti.projectmanagement.api.dto.request.changehistory.ProjectChangeHistoryCreateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.changehistory.ProjectChangeHistoryUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.changehistory.ProjectChangeHistoryResp;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectChangeHistory;

import java.util.List;

public interface ProjectChangeHistoryService extends IService<ProjectChangeHistory>{


    /**
     * 保存项目变更历史
     * @param createReq 变更历史
     * @return {@link Boolean}
     */
    Long saveProjectChangeHistory(ProjectChangeHistoryCreateReq createReq);

    /**
     * 更新项目变更历史
     * @param updateReq 变更历史
     * @return {@link Boolean}
     */
    Boolean updateProjectChangeHistory(ProjectChangeHistoryUpdateReq updateReq);

    /**
     * 删除项目变更历史
     * @param id 变更历史id
     * @return {@link Boolean}
     */
    Boolean deleteProjectChangeHistory(Long id);

    /**
     * 查询项目变更历史详情
     * @param id 变更历史id
     * @return {@link ProjectChangeHistoryResp}
     */
    ProjectChangeHistoryResp queryProjectChangeHistory(Long id);

    /**
     * 查询项目变更历史列表
     * @param id 变更历史id
     * @return {@link List}<{@link ProjectChangeHistoryResp}>
     */
    List<ProjectChangeHistoryResp> queryChangeHistoryListByProjectId(Long id);
}
