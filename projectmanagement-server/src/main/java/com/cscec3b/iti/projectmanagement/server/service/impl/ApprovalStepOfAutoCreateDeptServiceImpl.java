//package com.cscec3b.iti.projectmanagement.server.service.impl;
//
//import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
//import com.cscec3b.iti.projectmanagement.server.enums.ApprovalStepEnum;
//import com.cscec3b.iti.projectmanagement.server.mapper.BidApprovalMapper;
//import com.cscec3b.iti.projectmanagement.server.service.AbstractApprovalStepService;
//import com.cscec3b.iti.projectmanagement.server.service.WfApprovalService;
//import lombok.AllArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
/// **
// * 立项步骤-自动创建项目部(六统一创建)
// *
// * <AUTHOR>
// * @date 2024/01/03
// */
//@Slf4j
//@Service("autoCreateDeptService")
//@AllArgsConstructor
//@Transactional(rollbackFor = Exception.class)
//public class ApprovalStepOfAutoCreateDeptServiceImpl extends AbstractApprovalStepService {
//
//    private final BidApprovalMapper bidApprovalMapper;
//
//    private final WfApprovalService wfApprovalService;
//
//
//    @Override
//    public Integer currentStep() {
//        return ApprovalStepEnum.RE_CHECK.getNo();
//    }
//
//    @Override
//    public void submitApprovalStep(BidApproval bidApproval) {
//    }
//
//
//}
