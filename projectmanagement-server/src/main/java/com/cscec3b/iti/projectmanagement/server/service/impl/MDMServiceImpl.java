package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.enignproject.*;
import com.cscec3b.iti.projectmanagement.api.dto.request.FinanceReq;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidApprovalService;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.converter.mapstruct.IMdmMerchantConverter;
import com.cscec3b.iti.projectmanagement.server.converter.mapstruct.IMdmOrgConverter;
import com.cscec3b.iti.projectmanagement.server.entity.*;
import com.cscec3b.iti.projectmanagement.server.enums.EngineProjectEnum;
import com.cscec3b.iti.projectmanagement.server.enums.EngineProjectSourceEnum;
import com.cscec3b.iti.projectmanagement.server.feign.MDMOpenApiFeign;
import com.cscec3b.iti.projectmanagement.server.mapper.EngineeringProjectMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.MdmMerchantInfoMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.MdmOrgInfoMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.CpmProjectFlowEvent;
import com.cscec3b.iti.projectmanagement.server.service.EngineeringStandardProjectMappingService;
import com.cscec3b.iti.projectmanagement.server.service.IMDMService;
import com.cscec3b.iti.projectmanagement.server.service.IMdmCodeFinanceMappingService;
import com.cscec3b.iti.projectmanagement.server.service.ProjectService;
import com.cscec3b.iti.projectmanagement.server.util.MDMDataConverter;
import com.g3.G3OrgService;
import com.g3.org.api.dto.resp.GetUserInfoResponse;
import com.g3.org.api.dto.resp.user.UserInfo;

import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * MDM集成服务实现类
 *
 * <AUTHOR>
 * @date 2025/01/03
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class MDMServiceImpl implements IMDMService {

    private final MDMOpenApiFeign mdmOpenApiFeign;
    private final IBidApprovalService bidApprovalService;
    private final EngineeringProjectMapper engineeringProjectMapper;
    private final ProjectMapper projectMapper;
    private final EngineeringStandardProjectMappingService standardProjectMappingService;
    private final ProjectService projectService;
    private final ApplicationEventPublisher publisher;
    private final IMdmMerchantConverter mdmMerchantConverter;
    private final IMdmCodeFinanceMappingService mdmCodeFinanceMappingService;
    private final MdmMerchantInfoMapper mdmMerchantInfoMapper;
    private final G3OrgService g3OrgService;
    private final MdmOrgInfoMapper mdmOrgInfoMapper;
    private final IMdmOrgConverter mdmOrgConverter;

    @Override
    public MDMGateWayResponse validateEngineProject(BidApproval bidApproval) {
        log.info("开始校验工程项目数据，BidApproval ID: {}", bidApproval.getId());
        try {
            MdmApi<MdmPushEntity> pushDto = buildPushDtoFromBidApproval(bidApproval);
            log.info("校验工程项目数据 pushDto：{}", JSONUtil.toJsonStr(pushDto));
            MDMGateWayResponse response = mdmOpenApiFeign.validateEngineProject(pushDto);
            log.info("校验工程项目数据完成，BidApproval ID: {}, 响应: {}", bidApproval.getId(), response);
            checkResponse(response);
            return response;
        } catch (Exception e) {
            log.error("校验工程项目数据失败，BidApproval ID: {}", bidApproval.getId(), e);
            throw new FrameworkException(500, "校验工程项目数据失败: " + e.getMessage());
        }
    }

    @Override
//    @PmReTry
    public String pushEngineProject(BidApproval bidApproval) {
        log.info("开始推送工程项目数据到MDM，BidApproval ID: {}", bidApproval.getId());
        try {
            // 再次判断是否工程项目
            if (EngineProjectEnum.NON_ENGINE_PROJECT.getCode().equals(bidApproval.getEngineeringProject())) {
                throw new FrameworkException(500, "非工程项目,无需推送");
            }
            MdmApi<MdmPushEntity> pushDto = buildPushDtoFromBidApproval(bidApproval);
            MDMGateWayResponse response = mdmOpenApiFeign.pushEngineProject(pushDto);
            log.info("推送工程项目数据到MDM完成，BidApproval ID: {}, 响应: {}", bidApproval.getId(), response);
            checkResponse(response);
            return response.getData().getMdmCode();
        } catch (Exception e) {
            log.error("推送工程项目数据到MDM失败，BidApproval ID: {}", bidApproval.getId(), e);
            throw new FrameworkException(500, "推送工程项目数据到MDM失败: " + e.getMessage());
        }
    }

    @Override
    public MDMGateWayResponse validateEngineProjectByBidApprovalId(Long bidApprovalId) {
        BidApproval bidApproval = bidApprovalService.getById(bidApprovalId);
        if (bidApproval == null) {
            throw new FrameworkException(500, "中标未立项不存在，ID: " + bidApprovalId);
        }
        return validateEngineProject(bidApproval);
    }

    @Override
    public String pushEngineProjectByBidApprovalId(Long bidApprovalId) {
        BidApproval bidApproval = bidApprovalService.getById(bidApprovalId);
        if (bidApproval == null) {
            throw new FrameworkException(500, "中标未立项不存在，ID: " + bidApprovalId);
        }
        return pushEngineProject(bidApproval);
    }

    @Override
    public void initEngineProject(Long projectId, String mdmCode, BidApproval bidApproval) {
        // 将中标未立项bidApproval中的 工程项目信息转换为engineeringproject 并插入到数据库
        // 1. 先根据projectId查询engineeringproject是否存在
        EngineeringProject engineeringProject = engineeringProjectMapper.selectOne(Wrappers.<EngineeringProject>lambdaQuery()
                .eq(EngineeringProject::getMainProjectId, projectId));
        if (engineeringProject != null) {
            log.error("工程项目已存在，projectId: {}", projectId);
            return;
        }
        // 2. 如果不存在，插入engineeringproject
        engineeringProject = MDMDataConverter.convertFromBidApproval2EngineeringProject(bidApproval);
        Project project = projectMapper.selectById(projectId);
        engineeringProject.setMainProjectId(projectId);
        engineeringProject.setInitProjectId(projectId);
        engineeringProject.setEngineeringKey("G" + project.getCpmProjectKey());
        engineeringProject.setEngineeringCode(mdmCode);
        engineeringProject.setProjectSource(EngineProjectSourceEnum.CPM.getCode());
        engineeringProjectMapper.insert(engineeringProject);
    }

    /**
     * 工程项目回调信息 <BR>
     * 1. 按工程编码查询工程，如果不存在，插入engineeringproject <BR>
     * 2. 获取工程项目关联的施工项目信息，进行反转， <BR>
     * 3. 遍历，第一组为主施工项目，其他为非主施工 <BR>
     * 4. 如果是主施工项目，查询主施工是否存在，存在则写入关联关系，并触发工程立项 <BR>
     * 5. 如果不是主施工项目，查询主施工是否存在，存在则写入关联关系，并触发非主施工立项 <BR>
     * 
     * @param data
     * @param mdmCallbackRecord
     */
    @Override
    public void mdmEngineProjectCallback(List<MDMEngineProjectCallbackDto> data, MdmCallbackRecord mdmCallbackRecord) {
        log.info("MDM工程项目回调，回调数据: {}", JsonUtils.toJsonStr(data));
        // 获取工程项目关联的施工项目信息
        // 1. 获取工程项目名称与code
        data.forEach(ep -> {
            final String code = ep.getCode();
            final String name = ep.getName();
            if (StringUtils.isBlank(code)) {
                throw new FrameworkException(HttpStatus.BAD_REQUEST, -1, "未获取到工程项目code");
            }
            EngineeringProject engineeringProject = engineeringProjectMapper
                .selectOne(Wrappers.<EngineeringProject>lambdaQuery().eq(EngineeringProject::getEngineeringCode, code));
            if (engineeringProject == null) {
                log.warn("未获取到工程项目信息，工程项目编码: {}, 进行初始化", code);
                // 将 ep 转换为engineeringProject
                engineeringProject = MDMDataConverter.convertFromMDMEngineProjectCallbackDto(ep);
                // 生成 key
                engineeringProject.setEngineeringKey("GP" + code);
                engineeringProject.setProjectSource(EngineProjectSourceEnum.MDM.getCode());
                engineeringProjectMapper.insert(engineeringProject);
            } else {
                // 更新工程项目信息, 集团只会下发安监信息及 工程与核算项目的绑定关系
                // 比较安监信息 -所属项目部
                if (StringUtils.isNotBlank(ep.getProjectDepartment())
                    && !ep.getProjectDepartment().equalsIgnoreCase(engineeringProject.getProjectDepartment())) {
                    log.warn("工程项目：{}-{} 所属项目部已变更, 原项目部：{}，现项目部：{}", code, name,
                        engineeringProject.getProjectDepartment(), ep.getProjectDepartment());
                    engineeringProject.setProjectDepartment(ep.getProjectDepartment());
                    engineeringProjectMapper.updateById(engineeringProject);
                    // todo 触发工程项目更新事件
                }
            }
            // 2. 获取最新一对施工项目信息  (集团会按映射顺序返回)
            List<MDMEngineProjectCallbackDto.Sgxm> sgxmList = Optional.of(ep)
                    .map(MDMEngineProjectCallbackDto::getSgxm).filter(CollectionUtils::isNotEmpty)
                    .orElseThrow(() -> new FrameworkException(500, "未获取到工程项目关联的施工项目信息"));
            // 反转list, 现在第一条为主施工项目关系
            Collections.reverse(sgxmList);

            for (int i = 0; i < sgxmList.size(); i++) {
                MDMEngineProjectCallbackDto.Sgxm sgxm = sgxmList.get(i);
                String sgCode = sgxm.getSCode();
                String sgName = sgxm.getSName();
                // 暂存绑定关系
                int count = mdmCodeFinanceMappingService.count(
                    Wrappers.<MdmCodeFinanceMapping>lambdaQuery().eq(MdmCodeFinanceMapping::getEngineeringCode, code)
                        .eq(MdmCodeFinanceMapping::getFinanceCode, sgCode));
                if (count > 0) {
                    log.warn("工程项目：{}-{} 施工项目：{} - {}  关系已存在,跳过", code, name, sgCode, sgName);
                    continue;
                }
                // 暂存关系
                MdmCodeFinanceMapping mdmCodeFinanceMapping = new MdmCodeFinanceMapping().setEngineeringCode(code)
                    .setFinanceCode(sgCode).setUsed(0).setDeleted(0);
                mdmCodeFinanceMappingService.batchInsertOrUpdate(Collections.singletonList(mdmCodeFinanceMapping));
                // 工程项目与施工项目关联关系
                EngineeringStandardProjectMapping projectMapping =
                    new EngineeringStandardProjectMapping().setEngineeringProjectId(engineeringProject.getId())
                        .setMain(true).setInitFinanceCode(sgCode).setParentCode("0");

                // 3. 查询施工项目是否存在
                Project project = null;
                List<Project> projectList = projectMapper.selectList(Wrappers.<Project>lambdaQuery()
                    .eq(Project::getProjectFinanceCode, sgCode).orderByDesc(Project::getId));
                if (CollectionUtils.isNotEmpty(projectList)) {
                    project = projectList.get(0);
                }
                // 取主施工项目
                if (i == 0) {
                    // 主施工项目处理
                    // 判断工程项目来源
                    String projectSource = engineeringProject.getProjectSource();
                    // 由项目中心上行的工程项目 一定会有主施工项目id
                    if (EngineProjectSourceEnum.CPM.getCode().equals(projectSource)) {
                        final Long projectId = engineeringProject.getMainProjectId();
                        // 手动触发财商立项
                        FinanceReq financeReq =
                            new FinanceReq().setProjectId(String.valueOf(projectId)).setProjectFinanceCode(sgCode)
                                .setProjectFinanceName(sgName).setProjectFinanceAbbreviation(ep.getStname());
                        projectService.financeUpdateData(financeReq);
                        // 工程与主施工的关联关系由财商立项接口里处理
                        // 工程与施工编码的暂存关系变化由财商立项接口处理
                        // 后续移除财商后放回此处处理
                    } else {
                        // 由集团主动下发，或初始化项目，施工项目不一定存在，不存在则不接收
                        if (project == null) {
                            log.error("未获取到施工项目信息，施工项目编码: {}", sgCode);
                            throw new FrameworkException(HttpStatus.BAD_REQUEST, -1, "主施工项目不存在，请前往项目中心系统初始化施工项目");
                        }
                        // 补充 关联关系 ，更新暂存关系
                        projectMapping.setStandardProjectId(project.getId());
                        projectMapping.setInitFinanceStatus(project.getProjectStatusFin());
                        standardProjectMappingService.saveOrUpdate(projectMapping);
                        mdmCodeFinanceMapping.setUsed(Constants.NUMBER_ONE);
                        mdmCodeFinanceMappingService.updateById(mdmCodeFinanceMapping);
                        // 更新工程项目
                        engineeringProject.setMainProjectId(project.getId()).setInitProjectId(project.getId())
                            .setProjectDeptId(project.getYunshuOrgId())
                            .setMappingExecuteUnitId(project.getYunshuExecuteUnitId())
                            .setExecuteUnitId(project.getYunshuExecuteUnitId());
                        engineeringProjectMapper.updateById(engineeringProject);
                    }
                    // 触发工程项目立项事件
                    publisher.publishEvent(new CpmProjectFlowEvent(this, project.getId(),
                        FlowNodeEnum.ENGINE_PROJECT_APPROVAL, FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.CREATE));
                } else {
                    // 非主施工项目
                    // 存在则触发 非主施工立项事件
                    if (Objects.nonNull(project)) {
                        projectMapping.setStandardProjectId(project.getId())
                            .setInitFinanceStatus(project.getProjectStatusFin()).setMain(false);
                        standardProjectMappingService.saveOrUpdate(projectMapping);
                        mdmCodeFinanceMapping.setUsed(Constants.NUMBER_ONE);
                        mdmCodeFinanceMappingService.updateById(mdmCodeFinanceMapping);
                        // 非主施工项目立项事件(带绑定关系)
                        publisher.publishEvent(
                            new CpmProjectFlowEvent(this, project.getId(), FlowNodeEnum.NON_MAIN_STANDARD_PROJECT,
                                FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.CREATE));
                    }
                }

            }
        });

        // // 反转 sgxmList
        // MDMEngineProjectCallbackDto.Sgxm sgxm = sgxmList.get(0);
        // // 2. 获取最新一对关联关系
        // String sgCode = sgxm.getSCode();
        // String sgName = sgxm.getSName();
        // // 3.暂存绑定关系
        // int count = mdmCodeFinanceMappingService.count(Wrappers.<MdmCodeFinanceMapping>lambdaQuery()
        // .eq(MdmCodeFinanceMapping::getEngineeringCode, code).eq(MdmCodeFinanceMapping::getFinanceCode, sgCode));
        // if (count > 0) {
        // throw new FrameworkException(400, "工程项目：" + code + "-" + name + " 施工项目：" + sgCode + " - " + sgName + "
        // 关系已存在");
        // }
        // MdmCodeFinanceMapping mdmCodeFinanceMapping = new
        // MdmCodeFinanceMapping().setEngineeringCode(code).setFinanceCode(sgCode).setUsed(0).setDeleted(0);
        // EngineeringProject engineeringProject =
        // engineeringProjectMapper.selectOne(Wrappers.<EngineeringProject>lambdaQuery()
        // .eq(EngineeringProject::getEngineeringCode, code));
        // if (engineeringProject == null) {
        // log.error("未获取到工程项目信息，工程项目编码: {}", code);
        // throw new FrameworkException(500, "未获取到工程项目信息");
        // }
        // final Long projectId = engineeringProject.getMainProjectId();
        // EngineeringStandardProjectMapping projectMapping = new EngineeringStandardProjectMapping()
        // .setEngineeringProjectId(engineeringProject.getId())
        // .setInitFinanceCode(sgCode).setParentCode("0");
        // // 保存绑定关系
        // mdmCodeFinanceMappingService.batchInsertOrUpdate(Collections.singletonList(mdmCodeFinanceMapping));
        //
        // // 4. 判断是否主施工项目(集团下发的第一个施工项目, 关联表里无数据)
        // if (sgxmList.size() == 1) {
        // // 手动触发财商立项
        // FinanceReq financeReq = new
        // FinanceReq().setProjectId(String.valueOf(projectId)).setProjectFinanceCode(sgCode)
        // .setProjectFinanceName(engineProjectCallbackDto.getName())
        // .setProjectFinanceAbbreviation(engineProjectCallbackDto.getStname());
        // // todo 后续补充
        //// .setProjectSource().setBusinessType().setBusinessTypeCode();
        // projectService.financeUpdateData(financeReq);
        // // 为保证事件触发顺序, 保证工程项目立项在标准立项后面，将关联关系放在此处保存
        //// projectMapping.setMain(true).setStandardProjectId(engineeringProject.getMainProjectId());
        //// standardProjectMappingService.saveOrUpdate(projectMapping);
        // // 补充工程项目信息
        // engineeringProject.setInitFinanceCode(sgCode);
        // engineeringProjectMapper.updateById(engineeringProject);
        // //触发工程项目立项事件
        //// publisher.publishEvent(new CpmProjectFlowEvent(this, projectId, FlowNodeEnum.ENGINE_PROJECT_APPROVAL,
        //// FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.CREATE));
        // // 变更使用状态
        //// mdmCodeFinanceMapping.setUsed(Constants.NUMBER_ONE);
        //// mdmCodeFinanceMappingService.updateById(mdmCodeFinanceMapping);
        // } else {
        // // 通过施工项目编码 查询施工项目
        // Project project = projectMapper.selectOne(Wrappers.<Project>lambdaQuery()
        // .eq(Project::getProjectFinanceCode, sgCode));
        // // 存在则触发 工程项目绑定事件 /更新事件
        // if (Objects.nonNull(project)) {
        // projectMapping.setStandardProjectId(project.getId()).setInitFinanceStatus(project.getProjectStatusFin())
        // .setMain(false);
        // standardProjectMappingService.saveOrUpdate(projectMapping);
        // mdmCodeFinanceMapping.setUsed(Constants.NUMBER_ONE);
        // mdmCodeFinanceMappingService.updateById(mdmCodeFinanceMapping);
        // // 非主施工项目立项事件(带绑定关系)
        // publisher.publishEvent(new CpmProjectFlowEvent(this, project.getId(), FlowNodeEnum.NON_MAIN_STANDARD_PROJECT,
        // FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.CREATE));
        // }
        // }
        // });

    }

    @Override
    public void mdmEngineMerchantCallback(List<MdmMerchantCallBackDto> data, MdmCallbackRecord mdmCallbackRecord) {
        List<MdmMerchantInfo> mdmMerchantInfos = mdmMerchantConverter.voList2EntityList(data);
        // 根据 merchantInfo 去重
        List<MdmMerchantInfo> distinctList = new ArrayList<>(mdmMerchantInfos.stream()
                .collect(Collectors.toMap(MdmMerchantInfo::getMerchantCode, o -> o, (o1, o2) -> o1)).values());
        log.info("去重前有 {} 条数据, 去重后有 {} 条数据", mdmMerchantInfos.size(), distinctList.size());
        // 分批按每次1000条 分批入库
        for (int i = 0; i < distinctList.size(); i += 1000) {
            List<MdmMerchantInfo> batch = distinctList.subList(i, Math.min(i + 1000, mdmMerchantInfos.size()));
            try {
                mdmMerchantInfoMapper.batchInsertOrUpdate(batch);
            } catch (Exception e) {
                log.warn("批次处理失败, 当前批次: {} -{}", i, Math.min(i + 1000, mdmMerchantInfos.size()), e);
                if (batch.size() > 1) {
                    batch.forEach(merchantInfo -> {
                        try {
                            mdmMerchantInfoMapper.batchInsertOrUpdate(Collections.singletonList(merchantInfo));
                        } catch (Exception ex) {
                            log.error("客商数据保存失败: {} -{}", merchantInfo.getMerchantCode(), merchantInfo.getMerchantName(), ex);
                        }
                    });
                }
            }
        }
    }

    @Override
    public void mdmEngineCompanyCallback(List<MdmCompanyCallBackDto> companyCallBack, MdmCallbackRecord mdmCallbackRecord) {
        log.info("MDM组织架构回调，回调数据: {}", JsonUtils.toJsonStr(companyCallBack));
        List<MdmOrgInfo> mdmOrgInfos = mdmOrgConverter.voList2EntityList(companyCallBack);
        // 根据code ☁去重
        List<MdmOrgInfo> distinctList = new ArrayList<>(mdmOrgInfos.stream()
                .collect(Collectors.toMap(MdmOrgInfo::getOrgCode, o -> o, (o1, o2) -> o1)).values());
        log.info("去重前有 {} 条数据, 去重后有 {} 条数据", companyCallBack.size(), distinctList.size());
        // 分批按每次1000条 分批入库
        for (int i = 0; i < distinctList.size(); i += 1000) {
            List<MdmOrgInfo> batch = distinctList.subList(i, Math.min(i + 1000, distinctList.size()));
            try {
                mdmOrgInfoMapper.batchInsertOrUpdate(batch);
            } catch (Exception e) {
                log.warn("批次处理失败, 当前批次: {} -{}", i, Math.min(i + 1000, distinctList.size()), e);
                if (batch.size() > 1) {
                    batch.forEach(orgInfo -> {
                        try {
                            mdmOrgInfoMapper.batchInsertOrUpdate(Collections.singletonList(orgInfo));
                        } catch (Exception ex) {
                            log.error("组织架构数据保存失败: {} -{}", orgInfo.getOrgCode(), orgInfo.getOrgName(), ex);
                        }
                    });
                }
            }
        }

    }

    /**
     * 构建MDM推送数据（基于BidApproval）
     *
     * @param bidApproval 中标未立项实体
     * @return MDM推送DTO
     */
    private MdmApi<MdmPushEntity> buildPushDtoFromBidApproval(BidApproval bidApproval) {
        MdmApi<MdmPushEntity> pushDto = new MdmApi<>();
        pushDto.setMsgId(generateMsgId());
        pushDto.setDataType("Engproject");

        MdmPushEntity entity = MDMDataConverter.convertFromBidApproval(bidApproval);

        GetUserInfoResponse getUserInfoResponse = g3OrgService.executeForGetUserInfo(entity.getCreateUser());
        if (getUserInfoResponse.isSuccess() && Objects.nonNull(getUserInfoResponse.getData())) {
            UserInfo userInfo = getUserInfoResponse.getData();
            // 取 uc 用户信息中的 集团主数据编码
            entity.setCreateUser(userInfo.getCompanyPersonCode());
        }


        pushDto.setData(Collections.singletonList(entity));

        return pushDto;
    }


    /**
     * 生成消息ID
     */
    private String generateMsgId() {
        return UUID.randomUUID().toString().replace("-", "");
    }
}
