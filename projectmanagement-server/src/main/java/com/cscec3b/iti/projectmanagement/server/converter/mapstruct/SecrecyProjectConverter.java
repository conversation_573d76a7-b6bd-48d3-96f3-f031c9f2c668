package com.cscec3b.iti.projectmanagement.server.converter.mapstruct;

import com.cscec3b.iti.projectmanagement.api.dto.response.secrecy.SecrecyProjectDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.secrecy.SecrecyProjectResp;
import com.cscec3b.iti.projectmanagement.server.entity.SecrecyProjectInfo;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;

@Mapper(componentModel = ComponentModel.SPRING)
public interface SecrecyProjectConverter extends IConverter<SecrecyProjectDetailResp, SecrecyProjectResp,
        SecrecyProjectInfo> {
}
