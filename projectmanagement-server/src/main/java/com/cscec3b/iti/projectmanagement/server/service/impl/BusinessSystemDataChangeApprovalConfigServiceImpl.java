package com.cscec3b.iti.projectmanagement.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ColumnDetail;
import com.cscec3b.iti.projectmanagement.api.dto.dto.EventBusinessColumn;
import com.cscec3b.iti.projectmanagement.api.dto.request.BusSysDataChangeConfigEditReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.BusSysDataChangeConfigPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.BusSysDataChangeConfigReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.BusSysDataChangeConfigResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.BusSysDataListResp;
import com.cscec3b.iti.projectmanagement.server.annotation.EnumValues;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.BusSysDataChangeApprovalConfig;
import com.cscec3b.iti.projectmanagement.server.entity.BusinessSystemDataApprovalForm;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.WfApproval;
import com.cscec3b.iti.projectmanagement.server.entity.YunshuOrgSync;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ComparableResult;
import com.cscec3b.iti.projectmanagement.server.enums.BusSysDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.workflow.ProcessTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.workflow.WfTaskStateEnum;
import com.cscec3b.iti.projectmanagement.server.enums.workflow.WorkFlowScopeTypeEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.BusinessSystemDataChangeApprovalConfigMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.EventBusinessColumnMapper;
import com.cscec3b.iti.projectmanagement.server.service.BusinessSystemDataApprovalFormService;
import com.cscec3b.iti.projectmanagement.server.service.BusinessSystemDataChangeApprovalConfigService;
import com.cscec3b.iti.projectmanagement.server.service.IYunshuOrgSyncService;
import com.cscec3b.iti.projectmanagement.server.service.WfApprovalService;
import com.cscec3b.iti.projectmanagement.server.util.LoginUserUtil;
import com.github.pagehelper.page.PageMethod;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@AllArgsConstructor
public class BusinessSystemDataChangeApprovalConfigServiceImpl extends ServiceImpl<BusinessSystemDataChangeApprovalConfigMapper,
        BusSysDataChangeApprovalConfig> implements BusinessSystemDataChangeApprovalConfigService {


    /**
     * 业务表单服务类
     */
    private final BusinessSystemDataApprovalFormService businessSystemDataApprovalFormService;


    /**
     * 流程服务类
     */
    private final WfApprovalService wfApprovalService;

    /**
     * 组织同步服务类
     */
    private final IYunshuOrgSyncService yunshuOrgSyncService;

    /**
     * 事件业务字段服务类
     */
    private final EventBusinessColumnMapper eventBusinessColumnMapper;


    /**
     * 匹配审批流信息
     *
     * @param project            项目中心项目信息
     * @param busSysDataTypeEnum   业务板块
     * @param compareResult        有变更的字段信息
     * @return {@link List}<{@link ComparableResult}> 有变化，但未配置审批流的字段
     */
    @Override
    public List<ComparableResult> matchApprovalProcess(final Project project,
            final BusSysDataTypeEnum busSysDataTypeEnum, final List<ComparableResult> compareResult) {
        // 获取配置信息
        final List<BusSysDataChangeApprovalConfig> changeApprovalConfigList =
                getChangeApprovalConfigList(busSysDataTypeEnum, compareResult);
        // 未查询到则直接返回
        if (CollectionUtils.isEmpty(changeApprovalConfigList)) {
            return compareResult;
        }
        // 所有配置了的字段转Map
        final Map<String, List<BusSysDataChangeApprovalConfig>> approvalFieldMap =
                changeApprovalConfigList.stream().collect(Collectors.groupingBy(BusSysDataChangeApprovalConfig::getFieldId));


        List<ComparableResult> noBpmFieldList = new ArrayList<>();
        log.info("当前变更项目： {} - {}", project.getId(), project.getCpmProjectName());
        // 有审批流的字段
        // 简易审批不需要合并审批
        for (final ComparableResult result : compareResult) {
            final String fieldId = result.getFieldId();
            log.info("field -> {}", fieldId);
            // 当前字段的原始值
            final Object rawFieldContent = result.getRawFieldContent();
            log.info("rawFieldContent -> {}", rawFieldContent);
            // 当前字段的目标值
            final Object newFieldContent = result.getNewFieldContent();
            log.info("newFieldContent -> {}", newFieldContent);
            // 当前字段配置的所有审批流
            final List<BusSysDataChangeApprovalConfig> approvalConfigList = approvalFieldMap.get(fieldId);
            if (CollectionUtils.isEmpty(approvalConfigList)) {
                // 未配置审批流，需要更新
                noBpmFieldList.add(result);
                continue;
            }
            // 筛出配置有审批流的且字段配置的配置信息，都未配置则放到无审批流待更新列表中
            final List<BusSysDataChangeApprovalConfig> hasApprovalConfigList =
                    filterApprovalConfigList(approvalConfigList, rawFieldContent, newFieldContent);
            if (CollectionUtils.isEmpty(hasApprovalConfigList)) {
                // 有配置但是没有配置审批流，需要更新
                noBpmFieldList.add(result);
                continue;
            }

            // 匹配出当前字段的所有审批流中，审批层级最级的审批流
            /*匹配规则：
                1. 同一审批层级下(同一个组织，同一字段，并符合原始及目标值)，“固定值”的配置优先于“任意值”。： 固->固  > 任意->固 > 固->任意 > 任意->任意
                3. 本级未匹配到时，一直向上找，直到找到匹配的配置，或者找不到
             */

            // hasApprovalConfigList 进行转换，将同一组织下的配置按 固->固  > 任意->固 > 固->任意 > 任意->任意 配置
            Map<String, BusSysDataChangeApprovalConfig> queryCodeMap = groupApprovalConfigList(hasApprovalConfigList,
                    rawFieldContent, newFieldContent);

            final String yunshuExecuteUnitIdPath = project.getYunshuExecuteUnitIdPath();

            // 获取最终确定的审批流
            BusSysDataChangeApprovalConfig finalConfig = findFinalConfig(queryCodeMap, yunshuExecuteUnitIdPath);
            if (Objects.isNull(finalConfig)) {
                noBpmFieldList.add(result);
                continue;
            }
            // 发起审批流
            processApproval(project, busSysDataTypeEnum, finalConfig, fieldId, rawFieldContent, newFieldContent);

        }

        return noBpmFieldList;
    }

    /**
     * 开始审批流
     *
     * @param project            项目信息
     * @param busSysDataTypeEnum 业务板块信息
     * @param finalConfig        审批配置信息
     * @param fieldId            审批字段
     * @param rawFieldContent    原始值
     * @param newFieldContent    变更后的值
     */
    private void processApproval(final Project project, final BusSysDataTypeEnum busSysDataTypeEnum,
            final BusSysDataChangeApprovalConfig finalConfig, final String fieldId, final Object rawFieldContent,
            final Object newFieldContent) {
        final Map<String, Object> projectMap = BeanUtil.beanToMap(project);
        // 判断当前字段是否已存在审批流，已存在则要进行废弃，并发起新的审批流
        // 审批类型
        final Integer processType = finalConfig.getProcessType();
        final ProcessTypeEnum processTypeEnum = ProcessTypeEnum.getEnumByCode(processType);
        // 0 为无需审批 1 为简易审批 2 为云枢审批
        if (processTypeEnum.equals(ProcessTypeEnum.CPM_BPM)) {
            // 填充表单
            final String belongId = IdUtil.objectId();
            final BusinessSystemDataApprovalForm businessSystemDataApprovalForm =
                    new BusinessSystemDataApprovalForm().setFieldId(fieldId).setCreateAt(Instant.now().toEpochMilli())
                            .setId(belongId).setRawFieldContent(JsonUtils.toJsonStr(rawFieldContent))
                            .setTargetFieldContent(JsonUtils.toJsonStr(newFieldContent)).setProjectId(project.getId())
                            .setCurrentFieldContent(JsonUtils.toJsonStr(projectMap.get(fieldId)))
                            .setApprovalConfigId(finalConfig.getId())
                            .setBusinessDataType(busSysDataTypeEnum.getName());
            //// 同一个项目下，同一个字段，只能有一个审批中的流程，有新流程创建，则要将旧流程作废
            final List<WfApproval> inProcessApprovalList =
                    wfApprovalService.getInProcessApproval(busSysDataTypeEnum.getName(), fieldId, project.getId());
            inProcessApprovalList.forEach(approval -> {
                // 删除业务表单
                businessSystemDataApprovalFormService.removeById(approval.getBelongId());
                // 作废旧流程
                wfApprovalService.update(new LambdaUpdateWrapper<WfApproval>().set(WfApproval::getStatus,
                        WfTaskStateEnum.CANCEL.getDictCode()).eq(WfApproval::getProcInstId,
                        approval.getProcInstId()));
            });
            businessSystemDataApprovalFormService.save(businessSystemDataApprovalForm);
            // 发起审批 用户使用默认管理员帐号发起审批
            // 默认帐号：admin  默认组织：中建三局集团有限公司 默认组织id：1
            final WfApproval wfApproval =
                    new WfApproval().setBelongId(belongId).setScopeType(WorkFlowScopeTypeEnum.BUSINESS_SYSTEM_DATA_CHANGE_ENUM.getScopeType())
                            .setProcessType(ProcessTypeEnum.CPM_BPM.getCode()).setProcDefId(belongId).setProjectId(project.getId())
                            .setStatus(WfTaskStateEnum.INPROGRESS.getDictCode()).setProcInstId(IdUtil.fastSimpleUUID())
                            .setRemark(String.valueOf(finalConfig.getId()));
            wfApprovalService.save(wfApproval);
        }
    }

    /**
     * 获取最后配置信息
     *
     * @param queryCodeMap            类型，字段及优化级匹配后的列表
     * @param yunshuExecuteUnitIdPath 项目执行单位queryCode
     * @return {@link BusSysDataChangeApprovalConfig}
     */
    private static BusSysDataChangeApprovalConfig findFinalConfig(
            final Map<String, BusSysDataChangeApprovalConfig> queryCodeMap, final String yunshuExecuteUnitIdPath) {
        // 取同一层级的审批流
        BusSysDataChangeApprovalConfig finalConfig = queryCodeMap.get(yunshuExecuteUnitIdPath);
        // 没有取到就向上查找
        if (Objects.isNull(finalConfig)) {
            final String[] treeIds = yunshuExecuteUnitIdPath.split("#");
            for (int i = treeIds.length - 1; i > 0; i--) {
                final String treeId = treeIds[i];
                String filterIdPath = yunshuExecuteUnitIdPath.substring(0,
                        yunshuExecuteUnitIdPath.indexOf('#' + treeId));
                finalConfig = queryCodeMap.get(filterIdPath);
                if (Objects.nonNull(finalConfig)) {
                    break;
                }
            }


        }
        return finalConfig;
    }

    /**
     * 同一组织的配置信息进行优先级过滤
     *
     * @param hasApprovalConfigList 配置了审批流的配置列表
     * @param rawFieldContent       原始值
     * @param newFieldContent       变更后的值
     * @return {@link Map}<{@link String}, {@link BusSysDataChangeApprovalConfig}>
     */
    private static Map<String, BusSysDataChangeApprovalConfig> groupApprovalConfigList(
            final List<BusSysDataChangeApprovalConfig> hasApprovalConfigList, final Object rawFieldContent,
            final Object newFieldContent) {
        return hasApprovalConfigList.stream()
                .collect(Collectors.groupingBy(
                        BusSysDataChangeApprovalConfig::getExecuteUnitQueryCode,
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> selectOneFromList(list, rawFieldContent, newFieldContent)
                        )
                ));
    }

    /**
     * 有配置了审批流，且原始值，目标值符合要求的配置信息
     *
     * @param approvalConfigList 配置列表
     * @param rawFieldContent    原始值
     * @param newFieldContent    变更后的值
     * @return {@link List}<{@link BusSysDataChangeApprovalConfig}>
     */
    private static List<BusSysDataChangeApprovalConfig> filterApprovalConfigList(
            final List<BusSysDataChangeApprovalConfig> approvalConfigList, final Object rawFieldContent,
            final Object newFieldContent) {
        // 类型
        return approvalConfigList.stream().filter(config -> !config.getProcessType().equals(ProcessTypeEnum.NO_APPROVAL.getCode()))
                // 字段值，原始值相等或任意值，目标值相等或任意值
                .filter(config -> matchRawValueOrRandom(config, rawFieldContent) && matchTargetOrRandom(config,
                        newFieldContent))
                .collect(Collectors.toList());
    }

    /**
     * 获取配置信息
     *
     * @param busSysDataTypeEnum 业务板块
     * @param compareResult      字段比较结果
     * @return {@link List}<{@link BusSysDataChangeApprovalConfig}>
     */
    private List<BusSysDataChangeApprovalConfig> getChangeApprovalConfigList(
            final BusSysDataTypeEnum busSysDataTypeEnum, final List<ComparableResult> compareResult) {
        final LambdaQueryWrapper<BusSysDataChangeApprovalConfig> lambdaQueryWrapper =
                new LambdaQueryWrapper<BusSysDataChangeApprovalConfig>()
                        // 业务板块
                        .eq(BusSysDataChangeApprovalConfig::getScopeType, busSysDataTypeEnum.getName())
                        // 字段
                        .in(CollectionUtils.isNotEmpty(compareResult), BusSysDataChangeApprovalConfig::getFieldId,
                                compareResult.stream().map(ComparableResult::getFieldId).toArray());
        // 所有配置过的字段
        return this.baseMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 匹配目标值 或 任意值
     *
     * @param config          配置信息
     * @param newFieldContent 目标值
     * @return boolean
     */
    private static boolean matchTargetOrRandom(final BusSysDataChangeApprovalConfig config,
            final Object newFieldContent) {
        return Objects.equals(newFieldContent, config.getTargetFieldContent()) || Objects.equals(config.getTargetFieldContent(), "*");
    }

    /**
     * 匹配原始值 或 任意值
     *
     * @param config          配置信息
     * @param rawFieldContent 原始值
     * @return boolean
     */
    private static boolean matchRawValueOrRandom(final BusSysDataChangeApprovalConfig config,
            final Object rawFieldContent) {
        return Objects.equals(rawFieldContent, config.getRawFieldContent()) || Objects.equals(config.getRawFieldContent(), "*");
    }

    private static BusSysDataChangeApprovalConfig selectOneFromList(
            final List<BusSysDataChangeApprovalConfig> currentUnitConfig,
            final Object rawFieldContent, final Object newFieldContent) {
        // 固定 -> 固定
        final BusSysDataChangeApprovalConfig fixedToFixedConfig = currentUnitConfig.stream()
                .filter(config -> Objects.equals(rawFieldContent, config.getRawFieldContent())
                        && Objects.equals(newFieldContent, config.getTargetFieldContent())).findFirst().orElse(null);
        log.info("fixedToFixedConfig -> {}", fixedToFixedConfig);
        // 任意值 -> 固定值
        final BusSysDataChangeApprovalConfig anyToFixedConfig =
                currentUnitConfig.stream().filter(config -> "*".equals(config.getRawFieldContent())
                        && Objects.equals(newFieldContent, config.getTargetFieldContent())).findFirst().orElse(null);
        log.info("anyToFixedConfig -> {}", anyToFixedConfig);
        // 固定值 -> 任意值
        final BusSysDataChangeApprovalConfig fixedToAnyConfig =
                currentUnitConfig.stream().filter(config -> Objects.equals(rawFieldContent, config.getRawFieldContent())
                        && "*".equals(config.getTargetFieldContent())).findFirst().orElse(null);
        log.info("fixedToAnyConfig -> {}", fixedToAnyConfig);
        // 任意值 -> 任意值
        final BusSysDataChangeApprovalConfig anyToAnyConfig =
                currentUnitConfig.stream().filter(config -> "*".equals(config.getRawFieldContent())
                        && "*".equals(config.getTargetFieldContent())).findFirst().orElse(null);
        log.info("anyToAnyConfig -> {}", anyToAnyConfig);
        BusSysDataChangeApprovalConfig[] configs = {fixedToFixedConfig, anyToFixedConfig, fixedToAnyConfig,
                anyToAnyConfig};
        BusSysDataChangeApprovalConfig firstNonNull = Arrays.stream(configs)
                .filter(Objects::nonNull).findFirst().orElse(null);
        log.info("firstNonNull -> {}", firstNonNull);
        return Stream.of(configs)
                .filter(Objects::nonNull).findFirst().orElse(null);
    }

    @Override
    public Boolean addDataChangeConfig(final BusSysDataChangeConfigReq configReq) {
        final YunshuOrgSync yunshuOrgSync =
                Optional.ofNullable(yunshuOrgSyncService.selectById(configReq.getExecuteUnitTreeId())).orElseThrow(() -> new BusinessException(80107000));
        final String idPath = yunshuOrgSync.getIdPath();
        final String fieldId = configReq.getFieldId();
        final String scopeType = configReq.getScopeType();
        final String orgId = yunshuOrgSync.getDeptId();
        // 校验流程类型
        ProcessTypeEnum.getEnumByCode(configReq.getProcessType());

        // 同一单位只能配置一个字段,原始值及目标值
        checkForDup(fieldId, scopeType, configReq.getRawFieldContent(), configReq.getTargetFieldContent(), orgId);
        final BusSysDataChangeApprovalConfig approvalConfig = BeanUtil.copyProperties(configReq,
                BusSysDataChangeApprovalConfig.class);
        approvalConfig.setExecuteUnitQueryCode(idPath).setExecuteUnitOrgId(orgId);
        return save(approvalConfig);
    }

    /**
     * 分页查询
     *
     * @param configReq 查询参数
     * @return {@link Page}<{@link BusSysDataChangeConfigResp}>
     */
    @Override
    public Page<BusSysDataChangeConfigResp> listPage(final BusSysDataChangeConfigPageReq configReq) {
        // 当前登陆用户的queryCode
        String queryCode = LoginUserUtil.queryCode();
        if (Objects.nonNull(configReq.getExecuteUnitTreeId())) {
            final YunshuOrgSync yunshuOrgSync =
                    Optional.ofNullable(yunshuOrgSyncService.selectById(configReq.getExecuteUnitTreeId())).orElseThrow(() -> new BusinessException(80107000));
            final String idPath = yunshuOrgSync.getIdPath();
            // 查询当前用户组织的本下数据
            if (!idPath.startsWith(queryCode)) {
                return new Page<>();
            }
            queryCode = idPath;
        }
        configReq.setExecuteUnitQueryCode(queryCode);
        final com.github.pagehelper.Page<BusSysDataChangeConfigResp> page =
                PageMethod.startPage(configReq.getCurrent(), configReq.getSize()).doSelectPage(() ->
                        this.baseMapper.pageList(configReq));
        final List<BusSysDataChangeConfigResp> result = page.getResult();

        return new Page<BusSysDataChangeConfigResp>(page.getTotal(), page.getPageNum(), page.getPageSize())
                .setRecords(result);
    }


    /**
     * 编辑数据变更配置
     *
     * @param editReq 编辑参数
     * @return {@link Boolean}
     */
    @Override
    public Boolean editDataChangeConfig(final BusSysDataChangeConfigEditReq editReq) {
        final YunshuOrgSync yunshuOrgSync =
                Optional.ofNullable(yunshuOrgSyncService.selectById(editReq.getExecuteUnitTreeId())).orElseThrow(() -> new BusinessException(80107000));
        final String scopeType = editReq.getScopeType();
        final String fieldId = editReq.getFieldId();
        final String orgId = yunshuOrgSync.getDeptId();
        checkForDup(fieldId, scopeType, editReq.getRawFieldContent(), editReq.getTargetFieldContent(), orgId);
        final BusSysDataChangeApprovalConfig changeApprovalConfig = BeanUtil.copyProperties(editReq,
                BusSysDataChangeApprovalConfig.class);
        changeApprovalConfig.setExecuteUnitOrgId(orgId).setExecuteUnitQueryCode(yunshuOrgSync.getIdPath());
        return updateById(changeApprovalConfig);
    }

    /**
     * 检查重复项
     *
     * @param fieldId            字段id
     * @param scopeType          业务板块
     * @param rawFiledContent    原始值
     * @param targetFieldContent 目标值
     * @param orgId              执行单位组织id
     */
    private void checkForDup(final String fieldId, final String scopeType, final String rawFiledContent,
            final String targetFieldContent, final String orgId) {
        final LambdaQueryWrapper<BusSysDataChangeApprovalConfig> lambdaQueryWrapper =
                new LambdaQueryWrapper<BusSysDataChangeApprovalConfig>().eq(BusSysDataChangeApprovalConfig::getFieldId,
                                fieldId).eq(BusSysDataChangeApprovalConfig::getScopeType, scopeType)
                        .eq(BusSysDataChangeApprovalConfig::getRawFieldContent, rawFiledContent)
                        .eq(BusSysDataChangeApprovalConfig::getTargetFieldContent, targetFieldContent)
                        .eq(BusSysDataChangeApprovalConfig::getExecuteUnitOrgId, orgId);
        if (count(lambdaQueryWrapper) >= Constants.NUMBER_ONE) {
            throw new BusinessException(80107009);
        }
    }

    @Override
    public List<BusSysDataListResp> listBusSysData() {
        return Arrays.stream(BusSysDataTypeEnum.values()).map(val -> {
            final BusSysDataListResp busSysDataListResp =
                    new BusSysDataListResp().setCode(String.valueOf(val.getCode())).setName(val.getName())
                            .setTableName(val.getTableName()).setDesc(val.getDesc()).setEntityName(val.getEntityName());
            final List<EventBusinessColumn> eventBusinessColumns =
                    eventBusinessColumnMapper.databaseColumns(Collections.singleton(val.getTableName()));
            if (CollectionUtils.isNotEmpty(eventBusinessColumns)) {
                final String entityName = busSysDataListResp.getEntityName();
                Class<?> entityClass = null;
                try {
                    entityClass = Class.forName("com.cscec3b.iti.projectmanagement.server.entity." + entityName);

                } catch (ClassNotFoundException e) {
                    throw new RuntimeException(e);
                }
                final Class<?> finalEntityClass = entityClass;
                eventBusinessColumns.stream().filter(Objects::nonNull).forEach(businessColumn -> {
                    final List<ColumnDetail> columns = businessColumn.getColumns();
                    columns.forEach(column -> {
                        final Field field;
                        try {
                            field = finalEntityClass.getDeclaredField(column.getCamelCase());
                        } catch (NoSuchFieldException e) {
                            throw new RuntimeException(e);
                        }
                        final EnumValues annotation = field.getAnnotation(EnumValues.class);
                        // 要求在这里使用的枚举内要内置 getAllEnumMap 方法
                        if (Objects.nonNull(annotation)) {
                            final List<Map<String, Object>> invoke = getMaps(annotation);
                            column.setEnumValues(invoke);
                        }
                    });
                    busSysDataListResp.setColumnDetails(columns);
                });
            }
            return busSysDataListResp;
        }).collect(Collectors.toList());
    }

    /**
     * 获取枚举值
     *
     * @param annotation
     * @return {@link List}<{@link Map}<{@link String}, {@link Object}>>
     */
    private static List<Map<String, Object>> getMaps(final EnumValues annotation) {
        final List<Map<String, Object>> invoke;
        try {
            final Class<Enum> enumClass = (Class<Enum>) annotation.value();
            final Method getAllEnumMap = enumClass.getMethod("getAllEnumMap");
            invoke = (List<Map<String, Object>>) getAllEnumMap.invoke(enumClass);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            throw new RuntimeException(e);
        }
        return invoke;
    }

    /**
     * 删除除数据变更配置
     *
     * @param id id
     * @return {@link Boolean}
     */
    @Override
    public Boolean deleteDataChangeConfig(final Long id) {
        return removeById(id);
    }


}
