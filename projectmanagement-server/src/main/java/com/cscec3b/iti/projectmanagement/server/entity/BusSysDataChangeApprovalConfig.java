package com.cscec3b.iti.projectmanagement.server.entity;

import com.baomidou.mybatisplus.annotation.*;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 业务系统字段数据变更审批配置表
 */
@Data
@Accessors(chain = true)
@TableName(value = "business_system_data_change_approval_config")
public class BusSysDataChangeApprovalConfig {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 字段ID
     */
    @TableField(value = "field_id")
    private String fieldId;

    /**
     * 板块信息: smartsite: 智慧工地
     */
    @TableField(value = "scope_type")
    private String scopeType;

    /**
     * 板块名称
     */
    @TableField(value = "scope_type_name")
    private String scopeTypeName;

    /**
     * 字段名
     */
    @TableField(value = "field_name")
    private String fieldName;

    /**
     * 字段原始值
     */
    @TableField(value = "raw_field_content")
    private String rawFieldContent;

    /**
     * 原始字段值枚举
     */
    @ApiModelProperty(value = "原始字段值枚举")
    private String rawFieldEnumName;

    /**
     * 目标字段值枚举
     */
    @ApiModelProperty(value = "目标字段值枚举")
    private String targetFieldEnumName;

    /**
     * 字段目标值
     */
    @TableField(value = "target_field_content")
    private String targetFieldContent;

    /**
     * 流程类型：0：无需审批；1：简易审批；2：云枢审批流；
     */
    @TableField(value = "process_type")
    private Integer processType;

    /**
     * 流程定义id
     */
    @TableField(value = "proc_def_id")
    private String procDefId;

    /**
     * 执行单位组织id
     */
    @TableField(value = "execute_unit_org_id")
    private String executeUnitOrgId;

    /**
     * 执行单位treeId
     */
    @ApiModelProperty(value = "执行单位treeId")
    private String executeUnitTreeId;

    /**
     * 执行单位queryCode
     */
    @TableField(value = "execute_unit_query_code")
    private String executeUnitQueryCode;

    @TableField(value = "deleted")
    @TableLogic(value = "0", delval = "UNIX_TIMESTAMP() * 1000")
    private Long deleted;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    private String createBy;

    /**
     * 添加时间
     */
    @TableField(value = "create_at", fill = FieldFill.INSERT)
    private Long createAt;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    private Long updateAt;
}