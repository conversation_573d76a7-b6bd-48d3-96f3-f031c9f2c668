package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.text.StringSubstitutor;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.model.req.ProjectFlowEventNoticeReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.MsgNoticeTargetUserInfo;
import com.cscec3b.iti.projectmanagement.api.dto.response.pendingtaskmsgconfig.PendingTaskMsgConfigResp;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.enums.MsgNoticeTypeEnum;
import com.cscec3b.iti.projectmanagement.server.event.SheJiYuanEpcEvent;
import com.cscec3b.iti.projectmanagement.server.mapper.PendingTaskMsgConfigMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.service.IEventNoticeService;
import com.cscec3b.iti.taskmesage.dto.TaskAndMsgDto;
import com.cscec3b.iti.taskmesage.service.TaskAndMessageService;

import cn.hutool.core.util.IdUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class EventNoticeServiceImpl implements IEventNoticeService {

    private final ProjectMapper projectMapper;

    private final TaskAndMessageService taskAndMessageService;

    private final PendingTaskMsgConfigMapper pendingTaskMsgConfigMapper;

    @Override
    public Boolean sendNoticeToHr(ProjectFlowEventNoticeReq noticeReq) throws Exception {
        final FlowNodeDataTypeEnum dataTypeEnum = FlowNodeDataTypeEnum.getByCode(noticeReq.getFlowDataTypeCode());
        final FlowNodeEnum nodeEnum = FlowNodeEnum.getByCode(noticeReq.getFlowNodeCode());
        if (dataTypeEnum.equals(FlowNodeDataTypeEnum.CREATE)
            && nodeEnum.equals(FlowNodeEnum.FINANCE_SMART_SITE_APPROVAL)) {
            final String bpmInstanceId = IdUtil.objectId();
            final String taskCode = IdUtil.objectId();
            final Long projectId = noticeReq.getProjectId();
            final Project project = projectMapper.selectById(projectId);
            final String billId = project.getCpmProjectKey();
            final String billType = MsgNoticeTypeEnum.EHR.getCode();
            final PendingTaskMsgConfigResp taskMsgConfig =
                pendingTaskMsgConfigMapper.match(MsgNoticeTypeEnum.EHR.getCode(), project.getYunshuExecuteUnitId());
            if (ObjectUtils.allNotNull(project, taskMsgConfig)) {
                genLink(project, taskMsgConfig, taskCode, billId, bpmInstanceId, billType);
                final Map map = parsePayLoad(taskMsgConfig.getPayload(), project);
                final List<MsgNoticeTargetUserInfo> targetUserInfoList = taskMsgConfig.getTargetUsers();
                Set<String> targetUserSet =
                    targetUserInfoList.stream().map(MsgNoticeTargetUserInfo::getId).collect(Collectors.toSet());
                final TaskAndMsgDto taskAndMsgDto = TaskAndMsgDto.builder().taskCode(taskCode)
                    .webLink(taskMsgConfig.getWebLink()).appLink(taskMsgConfig.getAppLink())
                    .configCode(taskMsgConfig.getUcMsgConfigCode()).payload(JsonUtils.toJsonStr(map))
                    .targetUsers(targetUserSet).billId(billId).bpmInstanceId(bpmInstanceId).billType(billType)
                    .billTypeName(MsgNoticeTypeEnum.EHR.getZhCN())
                    .startUserName("项目中心-标准立项事件").startTime(LocalDateTime.now()).title(MsgNoticeTypeEnum.EHR.getZhCN())
                    .retryTimeCycle(taskMsgConfig.getRetryCycle()).build();
                taskAndMsgDto.setBusinessKey(String.valueOf(noticeReq.getProjectId()));
                taskAndMessageService.sendTodoTaskAndMsg(taskAndMsgDto);
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    @Async("cpmTaskExecutor")
    @EventListener
    @Override
    public void SjyEventListener(SheJiYuanEpcEvent sjyEvent) throws Exception {
        final Long id = sjyEvent.getId();
        final String yunshuExecuteUnitId = sjyEvent.getYunshuExecuteUnitId();
        final Project project = projectMapper.selectById(id);
        final PendingTaskMsgConfigResp taskMsgConfig =
            pendingTaskMsgConfigMapper.match(MsgNoticeTypeEnum.SJY.getCode(), yunshuExecuteUnitId);
        if (ObjectUtils.allNotNull(project, taskMsgConfig)) {
            final String billId = IdUtil.fastSimpleUUID();
            final String taskCode = IdUtil.objectId();
            final String bpmInstanceId = project.getCpmProjectKey();
            final String billType = MsgNoticeTypeEnum.SJY.getCode();
            genLink(project, taskMsgConfig, taskCode, billId, bpmInstanceId, billType);
            final Map<String, String> payloadVar = taskMsgConfig.getPayload();
            final Map<String, Object> payloadMap = parsePayLoad(payloadVar, project);
            final List<MsgNoticeTargetUserInfo> targetUserInfoList = taskMsgConfig.getTargetUsers();
            if (CollectionUtils.isEmpty(targetUserInfoList)) {
                return;
            }
            Set<String> targetUserSet =
                targetUserInfoList.stream().map(MsgNoticeTargetUserInfo::getId).collect(Collectors.toSet());
            final TaskAndMsgDto taskAndMsgDto = TaskAndMsgDto.builder().taskCode(taskCode)
                .webLink(taskMsgConfig.getWebLink()).appLink(taskMsgConfig.getAppLink())
                .configCode(taskMsgConfig.getUcMsgConfigCode()).payload(JsonUtils.toJsonStr(payloadMap))
                .targetUsers(targetUserSet).billId(billId).bpmInstanceId(bpmInstanceId)
                .billType(MsgNoticeTypeEnum.SJY.getCode()).billTypeName(MsgNoticeTypeEnum.SJY.getZhCN())
                .startUserName("项目中心-设计研究院").startTime(LocalDateTime.now()).title(MsgNoticeTypeEnum.SJY.getZhCN())
                .retryTimeCycle(taskMsgConfig.getRetryCycle()).build();
            taskAndMsgDto.setBusinessKey(String.valueOf(sjyEvent.getId()));
            taskAndMessageService.sendTodoTaskAndMsg(taskAndMsgDto);
        }

    }

    private void genLink(Project project, PendingTaskMsgConfigResp taskMsgConfig, String taskCode, String billId,
        String bpmInstanceId, String type) {
        Map<String, Object> values = new HashMap<>();
        values.put("projectId", project.getId());
        values.put("bpmInstanceId", bpmInstanceId);
        values.put("taskCode", taskCode);
        values.put("billId", billId);
        values.put("type", type);

        StringSubstitutor substitutor = new StringSubstitutor(values)
            // 允许未定义变量
            .setEnableUndefinedVariableException(false)
            // 未定义变量会被替换成空字符串
            .setPreserveEscapes(false)
            .setVariableResolver(key -> values.containsKey(key) ? String.valueOf(values.get(key)) : "default");
        final String webLink = substitutor.replace(taskMsgConfig.getWebLink());
        final String appLink = substitutor.replace(taskMsgConfig.getAppLink());

        taskMsgConfig.setWebLink(webLink);
        taskMsgConfig.setAppLink(appLink);

    }

    // private void genPayload(Project project, PendingTaskMsgConfigResp taskMsgConfig) {
    // final Map<String, Object> projectMap = BeanUtil.beanToMap(project);
    // final String payload = taskMsgConfig.getPayload();
    // final Map<String, Object> payloadMap = BeanUtil.beanToMap(JSONUtil.parseObj(payload));
    // payloadMap.forEach((k, v) -> {
    // if (projectMap.containsKey(k)) {
    // payloadMap.put(k, projectMap.get(k));
    // }
    // });
    // taskMsgConfig.setPayload(JSONUtil.toJsonStr(payloadMap));
    // }

    public Map<String, Object> parsePayLoad(Map<String, String> payloadMap, Object object) throws Exception {
        Map<String, Object> tempMap = new HashMap<>();
        payloadMap.forEach((k, v) -> {
            Object value = null;
            try {
                value = getFieldValue(object, v);
            } catch (IllegalAccessException e) {
                v = null;
            }
            tempMap.put(k, value);
        });
        return tempMap;
    }

    private Object getFieldValue(Object object, String fieldName) throws IllegalAccessException {
        Field field = null;
        try {
            field = object.getClass().getDeclaredField(fieldName);
        } catch (NoSuchFieldException e) {
            log.error("field not found", e);
        }
        if (field != null) {
            field.setAccessible(true);
            return field.get(object);
        }
        return null;
    }
}
