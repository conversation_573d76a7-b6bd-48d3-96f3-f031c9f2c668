package com.cscec3b.iti.projectmanagement.server.enums;

import com.cscec3b.iti.common.base.dictionary.IDataDictionary;

import lombok.Getter;

/**
 * <AUTHOR>
 * @description ProjectClassEnum
 * @date 2023/02/17 17:45
 */
@Getter
public enum ProjectClassEnum implements IDataDictionary{
    /**
     * 建造类/施工项目
     */
        CONSTRUCTION_PROJECT(12, "建造类/施工项目", "construction_project"),

    /**
     * 其它分类项目
     */
    OTHER(99, "其它分类项目", "other");

        ProjectClassEnum(Integer dictCode, String zhCN, String enUS) {
            this.dictCode = dictCode;
            this.zhCN = zhCN;
            this.enUS = enUS;
        }

        final Integer dictCode;

        final String zhCN;

        final String enUS;

        @Override
        public Integer getDictCode() {
            return dictCode;
        }

        @Override
        public String getZhCN() {
            return zhCN;
        }

        @Override
        public String getEnUS() {
            return enUS;
        }

        /**
         * 增加说明
         */
        @Override
        public String getDesc() {
            return "项目分类枚举类";
        }
}
