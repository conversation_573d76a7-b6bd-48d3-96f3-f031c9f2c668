package com.cscec3b.iti.projectmanagement.server.service.impl;

import static com.cscec3b.iti.projectmanagement.server.enums.ApprovalStepEnumV2.DEPT_LINK_AND_FINANCE;

import java.time.Instant;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cscec3b.iti.common.redis.lock.annotation.Lock;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.api.dto.dto.CommEnumDict;
import com.cscec3b.iti.projectmanagement.api.dto.request.ApprovalTypeStepMappingReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.ApprovalTypeStepMappingUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.ApprovalTypeSubscribeMappingReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.ProjectApprovalTypeSaveReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.ProjectApprovalTypeUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.ProjectApprovalTypeResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.SubscriberForApprovalTypeResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.ApprovalTypeStepMappingResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.ApprovalTypeSubscribeMappingResp;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.ApprovalType;
import com.cscec3b.iti.projectmanagement.server.entity.ApprovalTypeStepMapping;
import com.cscec3b.iti.projectmanagement.server.entity.ApprovalTypeSubscribeMapping;
import com.cscec3b.iti.projectmanagement.server.enums.ApprovalStepEnumV2;
import com.cscec3b.iti.projectmanagement.server.service.ApprovalTypeStepMappingService;
import com.cscec3b.iti.projectmanagement.server.service.ApprovalTypeSubscribeMappingService;
import com.cscec3b.iti.projectmanagement.server.service.IApprovalTypeService;
import com.cscec3b.iti.projectmanagement.server.service.IProjectApprovalTypeService;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 立项项目类型管理服务
 *
 * <AUTHOR>
 * @date 2024/01/02
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class ProjectApprovalTypeServiceImpl implements IProjectApprovalTypeService {

    /**
     * 系统内置立项项目类型根节点名称
     */
    private final static String APPROVAL_TYPE_SYSTEM_INTEGRATOR = "approval_type";

    private final IApprovalTypeService approvalTypeService;

    private final ApprovalTypeStepMappingService stepMappingService;

    private final ApprovalTypeSubscribeMappingService subscribeMappingService;


    @Override
    public ProjectApprovalTypeResp approvalTypeTree() {
        final List<ProjectApprovalTypeResp> projectApprovalTypeList =
                approvalTypeService.typeList(APPROVAL_TYPE_SYSTEM_INTEGRATOR);
        // 组装成树
        if (CollectionUtils.isNotEmpty(projectApprovalTypeList)) {
            final List<ProjectApprovalTypeResp> approvalTypeRespList =
                    projectApprovalTypeList.stream().filter(m -> m.getParentId() == 0)
                            .peek(typeResp -> typeResp.setChild(this.getChildren(typeResp, projectApprovalTypeList)))
                            .collect(Collectors.toList());
            return approvalTypeRespList.get(0);
        }
        return null;
    }

    private List<ProjectApprovalTypeResp> getChildren(ProjectApprovalTypeResp typeResp,
            List<ProjectApprovalTypeResp> typeRespList) {
        return typeRespList.stream().filter(type -> Objects.equals(typeResp.getTypeId(), type.getParentId()))
                .peek(m -> m.setChild(this.getChildren(m, typeRespList)).setParentCode(typeResp.getTypeCode())
                        .setParentName(typeResp.getTypeName())).collect(Collectors.toList());
    }


    @Override
    public List<ApprovalTypeStepMappingResp> getStepMappingByTypeId(Long typeId) {
        final List<ApprovalTypeStepMappingResp> approvalTypeStepMappingResps =
                stepMappingService.lastListByType(typeId, Instant.now().toEpochMilli());
        if (CollectionUtils.isNotEmpty(approvalTypeStepMappingResps)) {
            approvalTypeStepMappingResps.forEach(m -> {
                final Integer stepNo = m.getStepNo();
                final ApprovalStepEnumV2 stepEnum = ApprovalStepEnumV2.getByNo(stepNo);
                m.setStepName(stepEnum.getName()).setStepCode(stepEnum.getCode());
            });
            return approvalTypeStepMappingResps;
        }
        return Collections.emptyList();
    }

    @Override
    public List<ApprovalTypeSubscribeMappingResp> getSubscribeMappingByTypeId(Long typeId) {
        return subscribeMappingService.getSubscribeMappingByTypeId(typeId);
    }

    @Override
    public Boolean saveApprovalTypeAndMapping(ProjectApprovalTypeSaveReq saveReq) {
        final List<ApprovalType> list = approvalTypeService.list(
                Wrappers.<ApprovalType>lambdaQuery().eq(ApprovalType::getTypeCode, saveReq.getTypeCode()));
        if (CollectionUtils.isNotEmpty(list)) {
            throw new FrameworkException(-1, "类型编码已存在");
        }
        final ApprovalType approvalType =
                new ApprovalType().setTypeCode(saveReq.getTypeCode()).setTypeName(saveReq.getTypeName()).setParentId(saveReq.getParentId())
                        .setRemark(saveReq.getRemark()).setRootCode(saveReq.getRootCode())
                        .setLastNode(saveReq.getLastNode());
        final Integer lastNode = approvalType.getLastNode();
        if (lastNode.equals(Constants.NUMBER_ONE)) {
            approvalType.setBusinessSegmentCodePath(JSONUtil.toJsonStr(saveReq.getBusinessSegmentCodePath()))
                    .setOrgId(saveReq.getOrgId());
        }
        if (Objects.isNull(saveReq.getParentId())) {
            final ApprovalType rootDict =
                    approvalTypeService.getOne(Wrappers.<ApprovalType>lambdaQuery().eq(ApprovalType::getTypeCode,
                    APPROVAL_TYPE_SYSTEM_INTEGRATOR));
            // 根节点不存在
            Optional.ofNullable(rootDict).map(dict -> approvalType.setParentId(dict.getTypeId())).orElseThrow(() -> new BusinessException(-1));
        }
        approvalTypeService.save(approvalType);
        if (lastNode.equals(Constants.NUMBER_ONE)) {
            // 保存立项步骤映射
            saveStepMapping(saveReq.getStepMappings(), approvalType);
        }
        return Boolean.TRUE;
    }

    private void genSubscribeMapping(List<ApprovalTypeSubscribeMappingReq> subscribeMappings,
            ApprovalType approvalType) {
        final List<ApprovalTypeSubscribeMapping> subscribeMappingList = subscribeMappings.stream().map(mapping ->
                        new ApprovalTypeSubscribeMapping().setSubscribeId(mapping.getSubscribeId())
                                .setTypeId(approvalType.getTypeId()).setPushLevel(mapping.getPushLevel()))
                .collect(Collectors.toList());
        subscribeMappingService.saveBatch(subscribeMappingList);
    }

    @Override
    public Boolean updateApprovalTypeAndMapping(ProjectApprovalTypeUpdateReq updateReq) {
        final ApprovalType dbAppType = approvalTypeService.getById(updateReq.getTypeId());
        // 获取最新版本
        final List<ApprovalTypeStepMappingResp> dbStepMapping =
                stepMappingService.lastListByType(dbAppType.getTypeId(), Instant.now().toEpochMilli());
        final List<Integer> dbStepList = dbStepMapping.stream()
                .sorted(Comparator.comparing(
                        ApprovalTypeStepMappingResp::getStepSeq))
                .map(ApprovalTypeStepMappingResp::getStepNo)
                .collect(Collectors.toList());
        List<ApprovalTypeStepMappingReq> stepMappings = updateReq.getStepMappings();
        final List<Integer> stepList =
                stepMappings.stream().sorted(Comparator.comparing(ApprovalTypeStepMappingReq::getStepSeq))
                        .map(ApprovalTypeStepMappingReq::getStepNo).collect(Collectors.toList());
        final String dbBusinessSegmentCodePath = dbAppType.getBusinessSegmentCodePath();
        TypeReference<Set<String>> typeReference = new TypeReference<Set<String>>() {
        };
        final Set<String> dbBusinessSegmentSet = JSONUtil.toBean(dbBusinessSegmentCodePath, typeReference, true);
        final Set<String> businessSegmentCodePathSet = updateReq.getBusinessSegmentCodePath();

        final ApprovalType approvalType =
                new ApprovalType().setTypeCode(updateReq.getTypeCode()).setTypeName(updateReq.getTypeName())
                        .setParentId(updateReq.getParentId()).setRemark(updateReq.getRemark())
                        .setLastNode(updateReq.getLastNode()).setRootCode(updateReq.getRootCode())
                        .setTypeId(updateReq.getTypeId()).setOrgId(updateReq.getOrgId())
                        .setBusinessSegmentCodePath(JSONUtil.toJsonStr(businessSegmentCodePathSet));
        approvalTypeService.updateById(approvalType);
        // 比较组织或步骤顺序,业务板块如数据发生了变化，会生成一条的版本记录
        if (!updateReq.getOrgId().equals(dbAppType.getOrgId())
                || !CollectionUtils.isEqualCollection(dbStepList, stepList)
                || ObjectUtils.notEqual(dbBusinessSegmentSet, businessSegmentCodePathSet)) {
            // todo 需要考虑是新增加一个类型，还是在原类型上修改
            this.saveStepMapping(stepMappings, approvalType);
        } else {
            this.updateStepMapping(stepMappings, approvalType);
        }
        return Boolean.TRUE;
    }

    /**
     * 保存步骤映射
     *
     * @param stepMappings 步骤映射
     * @param approvalType 审批类型
     */
    @Lock(lockKey = "#approvalType.typeId", waitTime = 1000, leaseTime = 500)
    private void updateStepMapping(List<ApprovalTypeStepMappingReq> stepMappings, ApprovalType approvalType) {
        // 获取当前最大version
        Integer maxVersion = stepMappingService.maxVersionByTypeId(approvalType.getTypeId());
        final List<ApprovalTypeStepMapping> typeStepMappingList = stepMappings.stream()
                .map(mapping -> new ApprovalTypeStepMapping().setId(mapping.getStepMappingId())
                        .setTypeId(approvalType.getTypeId()).setStepNo(mapping.getStepNo())
                        .setStepSeq(mapping.getStepSeq()).setVersion(maxVersion).setOrgId(approvalType.getOrgId())
                        .setSendTodoTask(mapping.isSendTodoTask()).setSendNoticeMsg(mapping.isSendNoticeMsg())
                        .setUserChoseType(mapping.getUserChoseType()).setMsgConfigId(mapping.getMsgConfigId())
                        .setSendUserCodes(JSONUtil.toJsonStr(mapping.getSendUserCodes()))).collect(Collectors.toList());
        stepMappingService.updateBatchById(typeStepMappingList);
    }

    /**
     * 保存步骤映射
     *
     * @param stepMappings 步骤映射
     * @param approvalType 审批类型
     */
    @Lock(lockKey = "#approvalType.typeId", waitTime = 1000, leaseTime = 500)
    private void saveStepMapping(List<ApprovalTypeStepMappingReq> stepMappings, ApprovalType approvalType) {
        // 获取当前最大version
        Integer maxVersion = stepMappingService.maxVersionByTypeId(approvalType.getTypeId());
        final List<ApprovalTypeStepMapping> typeStepMappingList = stepMappings.stream().map(mapping ->
                new ApprovalTypeStepMapping().setTypeId(approvalType.getTypeId()).setStepNo(mapping.getStepNo())
                        .setStepSeq(mapping.getStepSeq()).setOrgId(approvalType.getOrgId())
                        .setVersion(maxVersion + 1).setSendTodoTask(mapping.isSendTodoTask())
                        .setSendNoticeMsg(mapping.isSendNoticeMsg()).setMsgConfigId(mapping.getMsgConfigId())
                        .setSendUserCodes(JSONUtil.toJsonStr(mapping.getSendUserCodes()))
                        .setUserChoseType(mapping.getUserChoseType())
        ).collect(Collectors.toList());
        stepMappingService.saveBatch(typeStepMappingList);
    }

    @Override
    public Boolean deleteApprovalType(Long typeId) {
        // 如果存在子类，需先删除子类
        List<ApprovalType> childenList =
                approvalTypeService.list(Wrappers.<ApprovalType>lambdaQuery().eq(ApprovalType::getParentId, typeId));
        if (CollectionUtils.isNotEmpty(childenList)) {
            throw new BusinessException(80108006);
        }
        stepMappingService.remove(Wrappers.<ApprovalTypeStepMapping>lambdaUpdate()
                .eq(ApprovalTypeStepMapping::getTypeId, typeId));
        approvalTypeService.removeById(typeId);
        return Boolean.TRUE;
    }

    @Override
    public List<SubscriberForApprovalTypeResp> getSubscribeForApprovalTypeList(String subscriber, String appCode,
            String executeUnitQueryCode) {
        return approvalTypeService.getSubscribeForApprovalTypeList(subscriber, appCode, executeUnitQueryCode);
    }

    @Override
    public List<CommEnumDict> approvalStepList() {
        return Stream.of(ApprovalStepEnumV2.values())
            .filter(enumValue -> !enumValue.equals(DEPT_LINK_AND_FINANCE))
                .map(enumValue ->
                        new CommEnumDict().setNo(String.valueOf(enumValue.getNo()))
                                .setCode(enumValue.getCode())
                                .setName(enumValue.getName())
                )
                .collect(Collectors.toList());
    }

    @Override
    public List<ApprovalTypeStepMappingResp> getDefaultApprovalTypeStepMapping() {
        final ApprovalType type =
                approvalTypeService.getOne(Wrappers.<ApprovalType>lambdaQuery().eq(ApprovalType::getTypeCode,
                        "default"));
        final List<ApprovalTypeStepMappingResp> approvalTypeStepMappingResps =
                stepMappingService.lastListByType(type.getTypeId(), Instant.now().toEpochMilli());
        approvalTypeStepMappingResps.forEach(o -> {
            final Integer stepNo = o.getStepNo();
            final ApprovalStepEnumV2 stepEnum = ApprovalStepEnumV2.getByNo(stepNo);
            o.setStepName(stepEnum.getName()).setStepCode(stepEnum.getCode());
        });
        return approvalTypeStepMappingResps;
    }

    @Override
    public Boolean updateDefaultApprovalTypeStepMapping(List<ApprovalTypeStepMappingUpdateReq> stepMappingReq) {
        final ApprovalType type =
                approvalTypeService.getOne(Wrappers.<ApprovalType>lambdaQuery().eq(ApprovalType::getTypeCode,
                        "default"));
        // 获取当前最大version
        Integer maxVersion = stepMappingService.maxVersionByTypeId(type.getTypeId());
        final List<ApprovalTypeStepMapping> approvalTypeStepMappings = BeanUtil.copyToList(stepMappingReq,
                ApprovalTypeStepMapping.class);
        final long currentTime = Instant.now().toEpochMilli();
        approvalTypeStepMappings.forEach(o -> o.setVersion(maxVersion + 1)
                .setTypeId(type.getTypeId()).setCreateAt(currentTime).setUpdateAt(currentTime));
        stepMappingService.saveBatch(approvalTypeStepMappings);
        return true;
    }
}
