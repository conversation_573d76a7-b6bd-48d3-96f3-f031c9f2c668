//package com.cscec3b.iti.projectmanagement.server.config;
//
//import java.lang.reflect.Method;
//import java.util.Arrays;
//import java.util.List;
//
//import javax.annotation.Resource;
//import javax.servlet.http.HttpServletRequest;
//import javax.servlet.http.HttpServletResponse;
//
//import org.apache.commons.collections4.CollectionUtils;
//import org.apache.commons.lang3.StringUtils;
//import org.apache.shenyu.client.springmvc.annotation.ShenyuSpringMvcClient;
//import org.springframework.context.annotation.Import;
//import org.springframework.core.env.Environment;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.http.HttpStatus;
//import org.springframework.util.AntPathMatcher;
//import org.springframework.web.method.HandlerMethod;
//
//import com.cscec3b.iti.common.base.api.GenericityResponse;
//import com.cscec3b.iti.common.web.config.SpringUtils;
//import com.cscec3b.iti.usercenter.sdk.api.permission.dto.request.PermVerReq;
//import com.cscec3b.iti.usercenter.server.apiauth.handler.PermissionAbstractAuthByTokenHandler;
//import com.cscec3b.iti.usercenter.server.apiauth.service.IAuthService;
//import com.cscec3b.iti.usercenter.server.appcofig.UcApiProperties;
//import com.cscec3b.iti.usercenter.server.client.entity.SdkTokenExtends;
//import com.cscec3b.iti.usercenter.server.utils.AuthUtil;
//
//import cn.hutool.core.util.CharsetUtil;
//import lombok.extern.slf4j.Slf4j;
//
///**
// * 自定义Token 拦截及鉴权拦截器
// *
// * <AUTHOR>
// * @date 2022/10/18 15:06
// **/
//
//@Slf4j
//@Import(UcApiProperties.class)
//public class CusAuthByTokenHandler extends PermissionAbstractAuthByTokenHandler {
//
//    private static final String REQUEST = "REQUEST";
//
//    private final AntPathMatcher antPathMatcher = new AntPathMatcher();
//
//    @Resource
//    private PmExcludePathProperties excludePathProperties;
//
//    public CusAuthByTokenHandler(UcApiProperties properties, IAuthService authService,
//        StringRedisTemplate redisTemplate) {
//        super(properties, authService, redisTemplate);
//    }
//
//    /**
//     * 1. 拦截所有请求(全局白名单以外),放行自定义白名单接口,放行@ShenYuSpringMvcClient接口 2. @ShenYuSpringMvcClient 的接口由神禹拦截处理,当前拦截器不作处理
//     *
//     * @param request current HTTP request
//     * @param response current HTTP response
//     * @param handler chosen handler to execute, for type and/or instance evaluation
//     * @return
//     * @throws Exception
//     */
//    @Override
//    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
//        throws Exception {
//
//        response.setCharacterEncoding(CharsetUtil.UTF_8);
//        String requestMethod = request.getMethod();
//	    String requestUri = request.getRequestURI();
//	    log.debug("enter the token interceptor : {} {}", requestMethod, requestUri);
//
//        // 放行自定义白名单(application.yml 文件中配置)
//	    if (isExcludeRequest(requestUri, requestMethod)) {
//            log.debug("this api is custom exclude path");
//            return true;
//        }
//
//        // 不是request类型目前不处理; 不是handlerMethod类型直接跳过
//        if (!isRequestAndHandlerMethod(request, handler)) {
//            log.debug("this api is not request and handlerMethod, excluded");
//            return true;
//        }
//
//        // 放行有 @ShenyuSpringMvcClient 注解的接口, 已由神禹前置处理
//        if (isShenYuRequest(handler)) {
//            log.debug("this api from shenYu, excluded");
//            return true;
//        }
//
//        // 校验Token
//        if (StringUtils.isBlank(AuthUtil.getToken())) {
//            response.setStatus(HttpStatus.UNAUTHORIZED.value());
//            log.error("no token, return {}", HttpStatus.UNAUTHORIZED.value());
//            return false;
//        }
//
//        // 缓存用户信息
//        saveUserContext(AuthUtil.getToken());
//
//        // 鉴权
//        if (checkPermission(request)) {
//            log.debug("has permission, go on");
//            return true;
//        }
//
//        log.debug("no permission, return 403");
//        response.setStatus(HttpStatus.FORBIDDEN.value());
//        return false;
//    }
//
//    private boolean checkPermission(HttpServletRequest request) {
//        log.debug("permission token auth type is uc ");
//        PermVerReq perm = buildPermReq(request, properties);
//        final Environment environment = SpringUtils.getBean(Environment.class);
//        if (environment != null) {
//            // 本地环境直接跳过鉴权
//            final String[] activeProfiles = environment.getActiveProfiles();
//            if (Arrays.asList(activeProfiles).contains("local")) {
//                return true;
//            }
//        }
//        GenericityResponse<Boolean> restResult = hasApiPermissionByToken(perm);
//        return  restResult.getStatus() == 0 && Boolean.TRUE.equals(restResult.getData());
//    }
//
//    private void saveUserContext(String token) {
//        // 解析 token 放入线程
//        SdkTokenExtends userAppTokenExtends = AuthUtil.parseToken(token);
//        // 放入本地线程
//        PmUserContextHolder.setUser(userAppTokenExtends);
//    }
//
//    private boolean isRequestAndHandlerMethod(HttpServletRequest request, Object handler) {
//        return REQUEST.equalsIgnoreCase(request.getDispatcherType().name()) && handler instanceof HandlerMethod;
//    }
//
//    /**
//     * 判断是否神禹请求
//     *
//     * @param handler
//     * @return
//     */
//    private boolean isShenYuRequest(Object handler) {
//        Method method = ((HandlerMethod) handler).getMethod();
//        return method.isAnnotationPresent(ShenyuSpringMvcClient.class);
//    }
//
//    /**
//     * 白名单
//     * @param requestUri
//     * @param requestMethod
//     * @return
//     */
//    private boolean isExcludeRequest(String requestUri, String requestMethod) {
//        List<ExcludePath> excludePaths = excludePathProperties.getExcludePaths();
//        if (CollectionUtils.isNotEmpty(excludePaths)) {
//            for (ExcludePath excludePath : excludePaths) {
//	            if (antPathMatcher.match(excludePath.getPath(), requestUri)
//                    && requestMethod.equalsIgnoreCase(excludePath.getHttpMethod())) {
//                    return true;
//                }
//            }
//        }
//        return false;
//    }
//
//    /**
//     * 请求完成 清除ThreadLocal
//     * @param request  current HTTP request
//     * @param response current HTTP response
//     * @param handler  the handler (or {@link HandlerMethod}) that started asynchronous
//     *                 execution, for type and/or instance examination
//     * @param ex       any exception thrown on handler execution, if any; this does not
//     *                 include exceptions that have been handled through an exception resolver
//     * @throws Exception
//     */
//    @Override
//    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception
//    ex)
//        throws Exception {
//        log.debug("enter the afterCompletion");
//        // 请求完成 清除ThreadLocal
//        try {
//            PmUserContextHolder.remove();
//        } catch (Exception e) {
//            log.error(" remove thread error : {}", e);
//        }
//
//    }
//
//}
