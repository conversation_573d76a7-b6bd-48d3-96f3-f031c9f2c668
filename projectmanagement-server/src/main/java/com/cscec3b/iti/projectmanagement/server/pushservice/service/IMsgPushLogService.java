package com.cscec3b.iti.projectmanagement.server.pushservice.service;

import com.cscec3b.iti.projectmanagement.server.entity.MsgPushLog;

public interface IMsgPushLogService {
    
    
    /**
     * 保存
     *
     * @param record 记录
     * @return int
     * <AUTHOR>
     * @date 2023/08/21
     */
    int insert(MsgPushLog record);
    
    /**
     * 查询详情
     *
     * @param msgId 味精id
     * @return {@link MsgPushLog }
     * <AUTHOR>
     * @date 2023/08/21
     */
    MsgPushLog selectByPrimaryKey(String msgId);
    
    /**
     * 按主键可选更新
     *
     * @param record 记录
     * @return int
     * <AUTHOR>
     * @date 2023/08/21
     */
    int updateByPrimaryKeySelective(MsgPushLog record);
    
    /**
     * 更新
     *
     * @param record 记录
     * @return int
     * <AUTHOR>
     * @date 2023/08/21
     */
    int updateByPrimaryKey(MsgPushLog record);

}
