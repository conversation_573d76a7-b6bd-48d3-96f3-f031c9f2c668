package com.cscec3b.iti.projectmanagement.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 市场营销-局内部补充协议合同表
 */
@ApiModel(description = "市场营销-局内部补充协议合同表")
@Data
@Accessors(chain = true)
@TableName(value = "bureau_supplementary_agreement")
public class BureauSupplementaryAgreement {
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键id")
    private Long id;

    /**
     * 发起人单位
     */
    @TableField(value = "submit_person")
    @ApiModelProperty(value = "发起人单位")
    private String submitPerson;

    /**
     * 补充协议编号
     */
    @TableField(value = "agreement_code")
    @ApiModelProperty(value = "补充协议编号")
    private String agreementCode;

    /**
     * 工程名称
     */
    @TableField(value = "project_name")
    @ApiModelProperty(value = "工程名称")
    private String projectName;

    /**
     * 工程属地
     */
    @TableField(value = "project_belong")
    @ApiModelProperty(value = "工程属地")
    private String projectBelong;

    /**
     * 是否局重点项目
     */
    @TableField(value = "bureau_project")
    @ApiModelProperty(value = "是否局重点项目")
    private String bureauProject;

    /**
     * 是否授权外
     */
    @TableField(value = "mandate_foreign")
    @ApiModelProperty(value = "是否授权外")
    private String mandateForeign;

    /**
     * 客户名称
     */
    @TableField(value = "customer_name")
    @ApiModelProperty(value = "客户名称")
    private String customerName;

    /**
     * 上级相关方/客户母公司
     */
    @TableField(value = "superior_company_name")
    @ApiModelProperty(value = "上级相关方/客户母公司")
    private String superiorCompanyName;

    /**
     * 客户企业性质
     */
    @TableField(value = "enterprise_type")
    @ApiModelProperty(value = "客户企业性质")
    private String enterpriseType;

    /**
     * 建设单位（甲方）联系人
     */
    @TableField(value = "contact_person")
    @ApiModelProperty(value = "建设单位（甲方）联系人")
    private String contactPerson;

    /**
     * 合同经办人
     */
    @TableField(value = "contract_responsible_person")
    @ApiModelProperty(value = "合同经办人")
    private String contractResponsiblePerson;

    /**
     * 是否纳入公司考核指标
     */
    @TableField(value = "company_assessment_indicators")
    @ApiModelProperty(value = "是否纳入公司考核指标")
    private String companyAssessmentIndicators;

    /**
     * 业务类型
     */
    @TableField(value = "business_type")
    @ApiModelProperty(value = "业务类型")
    private String businessType;

    /**
     * 补充协议金额
     */
    @TableField(value = "supplement_amount")
    @ApiModelProperty(value = "补充协议金额 ")
    private BigDecimal supplementAmount;

    /**
     * 计价方式
     */
    @TableField(value = "pricing_method")
    @ApiModelProperty(value = "计价方式")
    private String pricingMethod;

    /**
     * 合同形式
     */
    @TableField(value = "contract_form")
    @ApiModelProperty(value = "合同形式")
    private String contractForm;

    /**
     * 人工费是否可调
     */
    @TableField(value = "cost_of_labor_change")
    @ApiModelProperty(value = "人工费是否可调")
    private String costOfLaborChange;

    /**
     * 主材费是否可调
     */
    @TableField(value = "cost_of_labor_change2")
    @ApiModelProperty(value = "主材费是否可调")
    private String costOfLaborChange2;

    /**
     * 是否有预付款
     */
    @TableField(value = "advances_flag")
    @ApiModelProperty(value = "是否有预付款")
    private String advancesFlag;

    /**
     * 进度款付款方式
     */
    @TableField(value = "advances_way")
    @ApiModelProperty(value = "进度款付款方式")
    private String advancesWay;

    /**
     * 月进度付款比例
     */
    @TableField(value = "advances_month_rate")
    @ApiModelProperty(value = "月进度付款比例")
    private String advancesMonthRate;

    /**
     * 竣工验收支付比例
     */
    @TableField(value = "completed_rate")
    @ApiModelProperty(value = "竣工验收支付比例")
    private String completedRate;

    /**
     * 竣工验收收款周期（月）
     */
    @TableField(value = "completed_cycle")
    @ApiModelProperty(value = "竣工验收收款周期（月）")
    private String completedCycle;

    /**
     * 结算支付比例
     */
    @TableField(value = "settlement_rate")
    @ApiModelProperty(value = "结算支付比例")
    private String settlementRate;

    /**
     * 结算周期（月）
     */
    @TableField(value = "settlement_cycle")
    @ApiModelProperty(value = "结算周期（月）")
    private String settlementCycle;

    /**
     * 保修金
     */
    @TableField(value = "warranty_premium")
    @ApiModelProperty(value = "保修金")
    private String warrantyPremium;

    /**
     * 保修金比例
     */
    @TableField(value = "warranty_premium_rate")
    @ApiModelProperty(value = "保修金比例")
    private String warrantyPremiumRate;

    /**
     * 保修金支付方式
     */
    @TableField(value = "warranty_premium_way")
    @ApiModelProperty(value = "保修金支付方式")
    private String warrantyPremiumWay;

    /**
     * 支付方式
     */
    @TableField(value = "pay_type_new")
    @ApiModelProperty(value = "支付方式")
    private String payTypeNew;

    /**
     * 现金支付方式
     */
    @TableField(value = "specific_pay_way")
    @ApiModelProperty(value = "现金支付方式")
    private String specificPayWay;

    /**
     * 是否垫资
     */
    @TableField(value = "advances_fund_flag")
    @ApiModelProperty(value = "是否垫资")
    private String advancesFundFlag;

    /**
     * 履约担保方式
     */
    @TableField(value = "guarantee_way")
    @ApiModelProperty(value = "履约担保方式")
    private String guaranteeWay;

    /**
     * 补充协议文本
     */
    @TableField(value = "agreement_url")
    @ApiModelProperty(value = "补充协议文本")
    private String agreementUrl;

    /**
     * 合同主要条款对比表
     */
    @TableField(value = "contract_term_url")
    @ApiModelProperty(value = "合同主要条款对比表")
    private String contractTermUrl;

    /**
     * 法律意见书
     */
    @TableField(value = "law_url")
    @ApiModelProperty(value = "法律意见书")
    private String lawUrl;

    /**
     * 局内补充协议编号
     */
    @TableField(value = "bureau_supplementary_agreement_code")
    @ApiModelProperty(value = "局内补充协议编号")
    private String bureauSupplementaryAgreementCode;

    /**
     * 独立合同ID
     */
    @TableField(value = "independent_contract_id")
    @ApiModelProperty(value = "独立合同ID")
    private Long independentContractId;

    /**
     * 独立合同类型：1投标总结；2合同定案；3补充协议；4局内部合同定案；5局内部补充协议；
     */
    @TableField(value = "independent_contract_type")
    @ApiModelProperty(value = "独立合同类型：1投标总结；2合同定案；3补充协议；4局内部合同定案；5局内部补充协议；")
    private Integer independentContractType;

    /**
     * 所属源文件id
     */
    @TableField(value = "belong_id")
    @ApiModelProperty(value = "所属源文件id")
    private Long belongId;

    /**
     * 客户级别
     */
    @TableField(value = "customer_level")
    @ApiModelProperty(value = "客户级别")
    private String customerLevel;

    /**
     * 建设单位（甲方）联系人电话
     */
    @TableField(value = "contact_person_mobile")
    @ApiModelProperty(value = "建设单位（甲方）联系人电话")
    private String contactPersonMobile;

    /**
     * 突破底线条款
     */
    @TableField(value = "break_bottom")
    @ApiModelProperty(value = "突破底线条款")
    private String breakBottom;

    /**
     * 文件类型：5：局内部补充协议； 50：局内部无合同续签补充协议
     */
    @TableField(value = "belong_file_type")
    @ApiModelProperty(value = "文件类型：5：局内部补充协议； 50：局内部无合同续签补充协议")
    private Integer belongFileType;

    /**
     * 云枢执行单位名称
     */
    @TableField(value = "yunshu_execute_unit")
    @ApiModelProperty(value = "云枢执行单位名称")
    private String yunshuExecuteUnit;

    /**
     * 云枢执行单位code
     */
    @TableField(value = "yunshu_execute_unit_code")
    @ApiModelProperty(value = "云枢执行单位code")
    private String yunshuExecuteUnitCode;

    /**
     * 云枢执行单位Id
     */
    @TableField(value = "yunshu_execute_unit_id")
    @ApiModelProperty(value = "云枢执行单位Id")
    private String yunshuExecuteUnitId;

    /**
     * 云枢执行单位id_path
     */
    @TableField(value = "yunshu_execute_unit_id_path")
    @ApiModelProperty(value = "云枢执行单位id_path")
    private String yunshuExecuteUnitIdPath;

    /**
     * 前置文件类型
     */
    @TableField(value = "pre_file_type")
    @ApiModelProperty(value = "前置文件类型")
    private Integer preFileType;

    /**
     * 前置文件id
     */
    @TableField(value = "pre_file_id")
    @ApiModelProperty(value = "前置文件id")
    private Long preFileId;

    /**
     * 创建时间
     */
    @TableField(value = "create_at", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 更新时间
     */
    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新时间")
    private Long updateAt;

    /**
     * 来源：0：中标未项(市场营销 ); 1: 市场营销(合同管理)
     */
    @TableField(value = "source")
    @ApiModelProperty(value = "来源：0：中标未项(市场营销 ); 1: 市场营销(合同管理)")
    private Integer source;

    /**
     * 独立性判断,Y:是独立,N:否
     */
    @TableField(value = "is_independent")
    @ApiModelProperty(value = "独立性判断,Y:是独立,N:否")
    private String independent;

    /**
     * 签约主体
     */
    @TableField(value = "signed_subject_value")
    @ApiModelProperty(value = "签约主体")
    private String signedSubjectValue;

    /**
     * 签约主体Code
     */
    @TableField(value = "signed_subject_code")
    @ApiModelProperty(value = "签约主体Code")
    private String signedSubjectCode;

    /**
     * 业务类型Code
     */
    @TableField(value = "business_type_code")
    @ApiModelProperty(value = "业务类型Code")
    private String businessTypeCode;

    /**
     * 客户级别Code
     */
    @TableField(value = "customer_level_code")
    @ApiModelProperty(value = "客户级别Code")
    private String customerLevelCode;

    /**
     * 客户企业性质Code
     */
    @TableField(value = "enterprise_type_code")
    @ApiModelProperty(value = "客户企业性质Code")
    private String enterpriseTypeCode;

    /**
     * 客户id
     */
    @TableField(value = "customer_id")
    @ApiModelProperty(value = "客户id")
    private String customerId;

    /**
     * 客户编号
     */
    @ApiModelProperty(value = "客户编号")
    private String customerCode;

    /**
     * 客户母公司id
     */
    @ApiModelProperty(value = "客户母公司id")
    private String superiorCompanyId;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String businessLicenseCode;

    /**
     * 进度款付款方式Code
     */
    @TableField(value = "advances_way_code")
    @ApiModelProperty(value = "进度款付款方式Code")
    private String advancesWayCode;


    /**
     * 业务板块codepath
     */
    @TableField(value = "business_segment_code_path")
    @ApiModelProperty(value = "业务板块codepath")
    private String businessSegmentCodePath;
}