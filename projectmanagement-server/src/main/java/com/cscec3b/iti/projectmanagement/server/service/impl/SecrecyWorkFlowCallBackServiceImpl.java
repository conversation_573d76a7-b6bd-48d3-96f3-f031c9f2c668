package com.cscec3b.iti.projectmanagement.server.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.request.workflow.G3WfCallBackReq;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidApprovalService;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
import com.cscec3b.iti.projectmanagement.server.entity.SecrecyProjectInfo;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.workflow.WfTaskStateEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.SecrecyProjectInfoMapper;
import com.cscec3b.iti.projectmanagement.server.service.AbstractWorkFlowCallBackService;
import com.cscec3b.iti.projectmanagement.server.service.IdTableService;
import com.cscec3b.iti.projectmanagement.server.service.WfApprovalService;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * 军民融合项目流程 回调服务 impl
 *
 * <AUTHOR>
 * @date 2024/04/15
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class SecrecyWorkFlowCallBackServiceImpl extends AbstractWorkFlowCallBackService {


    /**
     * 默认的步骤排序
     */
    List<Integer> STEP_LIST = Lists.newArrayList(10, 20, 30);

    private final WfApprovalService wfApprovalService;

    /**
     * 中标未立项审批服务
     */
    private final IBidApprovalService bidApprovalService;

    /**
     * 军民融合Maper
     */
    private final SecrecyProjectInfoMapper secrecyMapper;

    /**
     * id 服务
     */
    private final IdTableService idService;

    private SecrecyProjectInfo getSecrecyInfo(Long civilMilitaryId) {
        final SecrecyProjectInfo secrecyProjectInfo = secrecyMapper.selectById(civilMilitaryId);
        if (ObjectUtils.isEmpty(secrecyProjectInfo)) {
            throw new BusinessException(80107001);
        }
        return secrecyProjectInfo;
    }


    @Override
    public Boolean start(G3WfCallBackReq callBackDto) {
        final String businessKey = callBackDto.getBusinessKey();
        final SecrecyProjectInfo secrecyProjectInfo = this.getSecrecyInfo(Long.valueOf(businessKey));
        secrecyProjectInfo.setApprovalStatus(WfTaskStateEnum.INPROGRESS.getDictCode())
                .setApprovalBeginTime(Instant.now().toEpochMilli());
        secrecyMapper.updateById(secrecyProjectInfo);
        return super.start(callBackDto);
    }

    /**
     * 流程正常结束
     *
     * @param callBackDto 回调参数
     * @return {@link Boolean}
     */
    @Override
    public Boolean end(G3WfCallBackReq callBackDto) {
        final SecrecyProjectInfo secrecyProjectInfo = getSecrecyInfo(Long.valueOf(callBackDto.getBusinessKey()));
        // 修改当前流程状态
        secrecyProjectInfo.setApprovalStatus(WfTaskStateEnum.AGREE.getDictCode());
        secrecyMapper.updateById(secrecyProjectInfo);
        // 初始化中标未立项
        final BidApproval bidApproval = new BidApproval();
        bidApproval.setApprovalBeginTime(secrecyProjectInfo.getApprovalBeginTime()).setBelongId(secrecyProjectInfo.getId())
                .setType(IndContractsTypeEnum.SECRECY.getEnUS()).setProjectCode(secrecyProjectInfo.getFileCode())
                .setProjectName(secrecyProjectInfo.getProjectName()).setCustomerName(secrecyProjectInfo.getCustomerName())
                .setAddress(secrecyProjectInfo.getProjectAddress())
                .setYunshuExecuteUnitId(secrecyProjectInfo.getYunshuExecuteUnitId())
                .setYunshuExecuteUnitCode(secrecyProjectInfo.getYunshuExecuteUnitCode())
                .setYunshuExecuteUnitIdPath(secrecyProjectInfo.getYunshuExecuteUnitIdPath())
                .setYunshuExecuteUnit(secrecyProjectInfo.getYunshuExecuteUnit())
                .setCreateHead(Constants.DATA_FROM_N_CODE)
                .setCreateBy(secrecyProjectInfo.getCreateBy())
                .setCreateAt(Instant.now().toEpochMilli());
        // 拆分区域
        final String region = secrecyProjectInfo.getRegion();
        if (region.startsWith("/中国")) {
            final String[] regions = region.split("/");
            bidApproval.setProvince(Optional.ofNullable(regions[2]).orElse(null));
            bidApproval.setCity(Optional.ofNullable(regions[3]).orElse(null));
            bidApproval.setRegion(Optional.ofNullable(regions[4]).orElse(null));
        }else if (region.startsWith("/")) {
            final String[] regions = region.split("/");
            bidApproval.setCountry(regions[0]);
        }
        bidApprovalService.save(bidApproval);
        return super.end(callBackDto);
    }

    @Override
    public Boolean complete(G3WfCallBackReq callBackDto) {
        secrecyMapper.update(null, Wrappers.<SecrecyProjectInfo>lambdaUpdate()
                .set(SecrecyProjectInfo::getApprovalStatus, WfTaskStateEnum.INPROGRESS.getDictCode())
                .eq(SecrecyProjectInfo::getId, callBackDto.getBusinessKey()));
        return super.complete(callBackDto);
    }

    /**
     * 流程终止后(不通过)需要生成生成一个新的数据
     *
     * @param callBackDto
     * @return {@link Boolean}
     */
    @Override
    public Boolean terminate(G3WfCallBackReq callBackDto) {
        final SecrecyProjectInfo secrecyProjectInfo  =
                getSecrecyInfo(Long.valueOf(callBackDto.getBusinessKey()));
        // 旧的数据设置为不通过,禁止编辑
        secrecyProjectInfo.setApprovalStatus(WfTaskStateEnum.UNPASS.getDictCode());
        secrecyMapper.updateById(secrecyProjectInfo);
        // 生成新的belongId ， 重置审批状态及id
        secrecyProjectInfo.setBelongId(IdUtil.getSnowflake().nextId())
                .setFileCode(idService.getCommonKey(Constants.SECRECY_SEQUENCE_PREFIX))
                .setApprovalStatus(WfTaskStateEnum.INIT.getDictCode()).setId(null);
        secrecyMapper.insert(secrecyProjectInfo);
        return super.terminate(callBackDto);
    }

    /**
     * 撤回，单据作废
     * @param callBackDto 回调参数
     * @return {@link Boolean}
     */
    @Override
    public Boolean withdraw(G3WfCallBackReq callBackDto) {
        secrecyMapper.update(null,
                Wrappers.<SecrecyProjectInfo>lambdaUpdate().set(SecrecyProjectInfo::getApprovalStatus,
                        WfTaskStateEnum.INIT.getDictCode()).eq(SecrecyProjectInfo::getId,
                        callBackDto.getBusinessKey()));
        return super.withdraw(callBackDto);
    }

    @Override
    public Boolean sign(G3WfCallBackReq callBackDto) {
        return super.sign(callBackDto);
    }


    @Override
    public Boolean cancel(G3WfCallBackReq callBackDto) {
        final SecrecyProjectInfo secrecyProjectInfo =
                getSecrecyInfo(Long.valueOf(callBackDto.getBusinessKey()));
        // 旧的数据设置为不通过,禁止编辑
        secrecyProjectInfo.setApprovalStatus(WfTaskStateEnum.CANCEL.getDictCode());
        secrecyMapper.updateById(secrecyProjectInfo);
        return super.cancel(callBackDto);
    }

    /**
     * 流程被驳回 回到第一步
     *
     * @param callBackDto 回调参数
     * @return {@link Boolean}
     */
    @Override
    public Boolean reject(G3WfCallBackReq callBackDto) {
        secrecyMapper.update(null,
                Wrappers.<SecrecyProjectInfo>lambdaUpdate().set(SecrecyProjectInfo::getApprovalStatus,
                        WfTaskStateEnum.REJECT.getDictCode()).eq(SecrecyProjectInfo::getId,
                        callBackDto.getBusinessKey()));
        return super.reject(callBackDto);
    }

    @Override
    public Boolean timeOut(G3WfCallBackReq callBackDto) {
        return super.timeOut(callBackDto);
    }
}
