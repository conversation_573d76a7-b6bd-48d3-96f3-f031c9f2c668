package com.cscec3b.iti.projectmanagement.server.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.IProjectEventApi;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.*;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectEventItem;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectFlowNodeListResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.SubscriberListResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.SubscriberPageResp;
import com.cscec3b.iti.projectmanagement.server.pushservice.service.IProjectEventService;
import com.cscec3b.iti.projectmanagement.server.pushservice.service.IProjectEventSubscriberService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * 项目事件订阅配置管理
 * <AUTHOR>
 * @description ProjectEventController
 * @date 2023/04/18 16:05
 */
@RestController
@RequestMapping(IProjectEventApi.PATH)
@Api(tags = "项目事件订阅配置管理")
@Slf4j
public class ProjectEventController implements IProjectEventApi {
    @Resource
    IProjectEventService eventService;

    @Resource
    IProjectEventSubscriberService eventSubscriberService;

    /**
     * 分页查询系统订阅配置
     * @param subscribersReq
     * @return
     */
    @Override
    public GenericityResponse<Page<SubscriberPageResp>> getSubscriberPages(QuerySubscribersReq subscribersReq) {
        return ResponseBuilder.fromData(eventSubscriberService.getSubscriberPages(subscribersReq));
    }

    /**
     * 查询系统配置
     * @param subscribersReq
     * @return
     */
    @Override
    public GenericityResponse<List<SubscriberListResp>> getSubscriberList(QuerySubscribersListReq subscribersReq) {
        return ResponseBuilder.fromData(eventSubscriberService.getSubscriberList(subscribersReq));
    }

    /**
     * 新增系统订阅配置
     * @param subscriberReq
     * @return
     */
    @Override
    public GenericityResponse<Boolean> createSubscriber(CreateSubscriberReq subscriberReq) {
        return ResponseBuilder.fromData(eventSubscriberService.createSubscriber(subscriberReq));
    }

    /**
     * 更新系统订阅配置
     * @param subscriberReq
     * @return
     */
    @Override
    public GenericityResponse<Boolean> updateSubscriber(UpdateSubscriberReq subscriberReq) {
        return ResponseBuilder.fromData(eventSubscriberService.updateSubscriber(subscriberReq));
    }

    /**
     * 变更系统订阅配置的状态
     * @return
     */
    @Override
    public GenericityResponse<Boolean> changeSubscriberStatus(UpdateSubscriberReq subscriberReq) {
        return ResponseBuilder.fromData(eventSubscriberService.changeSubscriberStatus(subscriberReq));
    }

    /**
     * 获取所有事件配置的信息列表
     * @return
     */
    @Override
    public GenericityResponse<List<ProjectEventItem>> getProjectEvents() {
        return ResponseBuilder.fromData(eventService.getProjectEvents());
    }

    /**
     * 新增系统事件配置
     * @param eventReq
     * @return
     */
    @Override
    public GenericityResponse<Boolean> createEvent(CreateProjectEventReq eventReq) {
        return ResponseBuilder.fromData(eventService.createEvent(eventReq));
    }

    /**
     * 更新系统事件配置
     * @param eventReq
     * @return
     */
    @Override
    public GenericityResponse<Boolean> updateEvent(UpdateProjectEventReq eventReq) {
        return ResponseBuilder.fromData(eventService.updateEvent(eventReq));
    }

    /**
     * 更新系统事件配置
     * @param eventId
     * @return
     */
    @Override
    public GenericityResponse<Boolean> updateEventStatus(Integer eventId) {
        return ResponseBuilder.fromData(eventService.updateEventStatus(eventId));
    }

    /**
     * 配置可触发事件节点
     * @return
     */
    @Override
    public GenericityResponse<List<ProjectFlowNodeListResp>> getFlowNodeEnumInfo() {
        return ResponseBuilder.fromData(eventSubscriberService.getFlowNodeEnumInfo());
    }
}
