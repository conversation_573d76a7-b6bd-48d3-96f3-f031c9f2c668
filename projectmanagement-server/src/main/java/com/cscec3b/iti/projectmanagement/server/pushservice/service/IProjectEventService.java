package com.cscec3b.iti.projectmanagement.server.pushservice.service;

import com.cscec3b.iti.projectmanagement.api.dto.request.event.CreateProjectEventReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.event.UpdateProjectEventReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectEventItem;
import com.cscec3b.iti.projectmanagement.server.entity.ProjectEvent;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.ProjectEventEnum;

import java.util.List;

/**
 * 项目立项服务类
 *
 * <AUTHOR>
 * @date 2023/04/14 16:42
 */
public interface IProjectEventService {

    // /**
    //  * 推送事件到智慧工地
    //  *
    //  * @param excludeAppKeys  不推送的系统appKey
    //  * @param projectId       项目id
    //  * @param projectProgress 项目进度信息
    //  * @param currentTime     当前时间
    //  * @param eventEnum       事件枚举
    //  * @param subscribeId    业务系统id
    //  * <AUTHOR>
    //  * @date 2023/08/12 22:23
    //  */
    // void asyncPushToSmartsite(List<String> excludeAppKeys, Long projectId, ProjectProgress projectProgress,
    //         long currentTime, ProjectEventEnum eventEnum, Long subscribeId);
    //
    // /**
    //  * 推送事件到财商
    //  *
    //  * @param projectProgress 项目进度
    //  * @param currentTime     当前时间
    //  * @param subscribeId     业务系统id
    //  * <AUTHOR>
    //  * @date 2023/08/12 22:24
    //  */
    // void asyncPushToFinance(ProjectProgress projectProgress, long currentTime, Long subscribeId);

    /**
     * 根据消息信息推送事件
     *
     * @param eventEnum 事件类型
     * @param projectId 项目id
     * @param excludes  排除名单 appkey
     */
    void getSubscriber2SendMsgByEvent(ProjectEventEnum eventEnum, Long projectId, List<String> excludes);

    // /**
    //  * 推送财商
    //  *
    //  * @param projectId   项目id
    //  * @param subscribeId 业务系统id
    //  */
    // void pushProjectApproveEventToFinance(Long projectId, Long subscribeId);

    /**
     * 通过主键查询
     *
     * @param id id
     * @return {@link ProjectEvent}
     */
    ProjectEvent selectByPrimaryKey(Integer id);

    /**
     * 更新项目
     *
     * @param record 记录
     * @return int
     * <AUTHOR>
     * @Date 2023/8/24
     */
    int updateByPrimaryKey(ProjectEvent record);

    /**
     * 选择性批量更新
     *
     * @param list 列表
     * @return int
     */
    int updateBatchSelective(List<ProjectEvent> list);

    /**
     * 查询项目事件
     *
     * @return {@link List}<{@link ProjectEventItem}>
     */
    List<ProjectEventItem> getProjectEvents();

    /**
     * 创建事件
     *
     * @param eventReq 事件请求
     * @return {@link Boolean}
     */
    Boolean createEvent(CreateProjectEventReq eventReq);

    /**
     * 更新事件
     *
     * @param eventReq 事件请求
     * @return {@link Boolean}
     */
    Boolean updateEvent(UpdateProjectEventReq eventReq);

    /**
     * 更新事件状态
     *
     * @param eventId 事件id
     * @return {@link Boolean}
     */
    Boolean updateEventStatus(Integer eventId);
}
