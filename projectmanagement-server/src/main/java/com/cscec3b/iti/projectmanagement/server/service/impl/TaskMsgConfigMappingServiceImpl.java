package com.cscec3b.iti.projectmanagement.server.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.TaskMsgConfigMappingReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.TaskMsgConfigMappingUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.pendingtaskmsgconfig.PendingTaskMsgConfigResp;
import com.cscec3b.iti.projectmanagement.server.entity.TaskMsgConfig;
import com.cscec3b.iti.projectmanagement.server.entity.TaskMsgConfigMapping;
import com.cscec3b.iti.projectmanagement.server.mapper.PendingTaskMsgConfigMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.TaskMsgConfigMappingMapper;
import com.cscec3b.iti.projectmanagement.server.service.ITaskMsgConfigMappingService;
import com.cscec3b.iti.tg.common.base.util.BeanMapUtils;
import com.github.pagehelper.PageHelper;

import lombok.RequiredArgsConstructor;

@Service
@Transactional(rollbackFor = Exception.class)
@RequiredArgsConstructor
public class TaskMsgConfigMappingServiceImpl extends ServiceImpl<TaskMsgConfigMappingMapper, TaskMsgConfigMapping>
    implements ITaskMsgConfigMappingService {

    private final PendingTaskMsgConfigMapper taskMsgConfigMapper;

    @Override
    public Boolean create(TaskMsgConfigMappingReq saveReq) {
        final TaskMsgConfig taskMsgConfig = taskMsgConfigMapper
            .selectOne(Wrappers.<TaskMsgConfig>lambdaQuery().eq(TaskMsgConfig::getTypeCode, saveReq.getTypeCode()));
        final int count = this.count(Wrappers.<TaskMsgConfigMapping>lambdaQuery()
            .eq(TaskMsgConfigMapping::getTaskMsgConfigId, taskMsgConfig.getId())
            .eq(TaskMsgConfigMapping::getOrgId, saveReq.getOrgId()));
        if (count > 0) {
            throw new RuntimeException("同组织下已存在相同的配置，请检查");
        }
        if (StringUtils.isBlank(saveReq.getRetryCycle())) {
            saveReq.setRetryCycle(taskMsgConfig.getRetryCycle());
        }
        final TaskMsgConfigMapping msgConfig = BeanMapUtils.map(saveReq, TaskMsgConfigMapping.class);
        msgConfig.setTaskMsgConfigId(taskMsgConfig.getId());
        msgConfig.setTargetUsers(JsonUtils.toJsonStr(saveReq.getTargetUsers()));
        return this.save(msgConfig);
    }

    @Override
    public Boolean updateMapping(TaskMsgConfigMappingUpdateReq updateReq) {
        final int count = this.count(Wrappers.<TaskMsgConfigMapping>lambdaQuery()
            .eq(TaskMsgConfigMapping::getTaskMsgConfigId, updateReq.getTaskMsgConfigId())
            .eq(TaskMsgConfigMapping::getOrgId, updateReq.getOrgId())
            .ne(TaskMsgConfigMapping::getId, updateReq.getId()));
        if (count > 0) {
            throw new RuntimeException("同组织下已存在相同的配置，请检查");
        }
        final TaskMsgConfigMapping msgConfig = BeanMapUtils.map(updateReq, TaskMsgConfigMapping.class);
        msgConfig.setTargetUsers(JsonUtils.toJsonStr(updateReq.getTargetUsers()));
        return this.updateById(msgConfig);
    }

    @Override
    public Page<PendingTaskMsgConfigResp> pageList(TaskMsgConfigMappingReq pageReq) {
        final com.github.pagehelper.Page<PendingTaskMsgConfigResp> doSelectPage = PageHelper
            .startPage(pageReq.getCurrent(), pageReq.getSize()).doSelectPage(() -> this.baseMapper.pageList(pageReq));
        final Page<PendingTaskMsgConfigResp> page =
            new Page<>(doSelectPage.getTotal(), doSelectPage.getPageNum(), doSelectPage.getPageSize());
        page.setRecords(doSelectPage.getResult());
        return page;
    }
}
