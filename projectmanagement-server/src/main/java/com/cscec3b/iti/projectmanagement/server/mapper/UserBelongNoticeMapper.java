package com.cscec3b.iti.projectmanagement.server.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 公告与用户关联信息
 *
 * <AUTHOR>
 * @date 2023/01/13 17:06
 **/
@Mapper
public interface UserBelongNoticeMapper {

    /**
     * 保存 用户与文章的关联关系，存在则不插入
     *
     * @param noticeId noticeId
     * @param userId userId
     * @param createAt createAt
     * @return int
     */
    int saveIgnoreExist(@Param("noticeId") Long noticeId, @Param("userId") String userId, @Param("createAt") Long createAt);

    /**
     * 根据文章id 删除所有已读用户信息
     *
     * @param noticeId nooticeId
     * @return int
     */
    int deleteByNoticeId(@Param("noticeId") Long noticeId);

}
