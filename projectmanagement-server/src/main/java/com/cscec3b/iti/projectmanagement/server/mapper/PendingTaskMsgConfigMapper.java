package com.cscec3b.iti.projectmanagement.server.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.PendingTaskMsgReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.pendingtaskmsgconfig.PendingTaskMsgConfigResp;
import com.cscec3b.iti.projectmanagement.server.entity.TaskMsgConfig;

public interface PendingTaskMsgConfigMapper extends BaseMapper<TaskMsgConfig> {

    /**
     * 匹配符合条件的配置 <br>
     * 取本级或本上级
     */
    PendingTaskMsgConfigResp match(@Param("typeCode") String typeCode, @Param("orgId") String orgId);

    /**
     * 页面列表
     *
     * @param req 要求
     * @return {@link List }<{@link PendingTaskMsgConfigResp }>
     */
    List<PendingTaskMsgConfigResp> pageList(@Param("req") PendingTaskMsgReq req);
}