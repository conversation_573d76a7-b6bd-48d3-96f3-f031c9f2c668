package com.cscec3b.iti.projectmanagement.server.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.cscec3b.iti.projectmanagement.api.dto.response.help.HelpCenterResp;
import com.cscec3b.iti.projectmanagement.server.entity.HelpCenter;

/**
 * <AUTHOR>
 * @date 2023-01-2023/1/3 16:16
 */
@Mapper
public interface HelpCenterMapper {

    /**
     * 首页-帮助中心 前5条已发布的帮助信息
     *
     * @return java.util.List<com.cscec3b.iti.projectmanagement.api.dto.response.help.HelpCenterResp>
     * <AUTHOR>
     * @date 2023/01/03 16:17
     */
    List<HelpCenterResp> limit5();


    /**
     * 帮助中心列表信息
     *
     * @param status    发布状态
     * @param userId    用户id
     * @param title     标题
     * @param beginTime 搜索发布时间的起始时间
     * @param endTime   搜索发布时间的结束时间
     * @return java.util.List<com.cscec3b.iti.projectmanagement.api.dto.response.help.HelpCenterResp>
     * <AUTHOR>
     * @date 2023/01/03 16:21
     */
    List<HelpCenterResp> listPage(@Param("title") String title, @Param("beginTime") Long beginTime, @Param("endTime") Long endTime, @Param("userId") String userId, @Param("status") Integer status);
    
    /**
     * 帮助中心
     *
     * @param helpCenter 帮忙中心信息
     * @return int
     * <AUTHOR>
     * @date 2023/08/21
     */
    int save(HelpCenter helpCenter);

    /**
     * 更新
     *
     * @param helpCenter helpCenter
     * @return int
     * <AUTHOR>
     * @date 2023/01/03 16:25
     */
    int update(HelpCenter helpCenter);

    /**
     * 批量删除，逻辑删除
     *
     * @param ids ids
     * @return int
     * <AUTHOR>
     * @date 2023/01/03 16:25
     */
    int delete(@Param("ids") Long[] ids);
    
    
    /**
     * 通过id和用户id
     *
     * @param id     id
     * @param userId 用户id
     * @return {@link HelpCenter }
     * <AUTHOR>
     * @date 2023/08/21
     */
    HelpCenter getByIdAndUserId(@Param("id") Long id, @Param("userId") String userId);

}
