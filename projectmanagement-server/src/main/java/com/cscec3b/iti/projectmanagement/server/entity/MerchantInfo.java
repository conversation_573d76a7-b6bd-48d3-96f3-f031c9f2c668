package com.cscec3b.iti.projectmanagement.server.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 主数据客商信息
 * @date 2025-06-16
 */
@ApiModel(description = "主数据客商信息")
@Data
@Accessors(chain = true)
@TableName(value = "mdm_merchant_info")
public class MerchantInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 客商名称
     */
    @TableField(value = "merchant_name")
    @ApiModelProperty(value = "客商名称")
    private String merchantName;

    /**
     * 客商编码
     */
    @TableField(value = "merchant_code")
    @ApiModelProperty(value = "客商编码")
    private String merchantCode;

    /**
     * 是否启用客户
     */
    @TableField(value = "merchant_enable")
    @ApiModelProperty(value = "是否启用客户")
    private String merchantEnable;

    /**
     * 客商类型
     */
    @TableField(value = "type")
    @ApiModelProperty(value = "客商类型")
    private String type;

    /**
     * 是否封存
     */
    @TableField(value = "freeze")
    @ApiModelProperty(value = "是否封存")
    private String freeze;

    /**
     * 客户性质
     */
    @TableField(value = "merchant_property")
    @ApiModelProperty(value = "客户性质")
    private String merchantProperty;

    /**
     * 关联人员编码
     */
    @TableField(value = "relate_user_code")
    @ApiModelProperty(value = "关联人员编码")
    private String relateUserCode;

    /**
     * 关联公司编码
     */
    @TableField(value = "relate_company_code")
    @ApiModelProperty(value = "关联公司编码")
    private String relateCompanyCode;

    /**
     * 供应商性质
     */
    @TableField(value = "supplier_property")
    @ApiModelProperty(value = "供应商性质")
    private String supplierProperty;

    /**
     * 是否启用供应商
     */
    @TableField(value = "supplier_enable")
    @ApiModelProperty(value = "是否启用供应商")
    private String supplierEnable;

    /**
     * 行政区域
     */
    @TableField(value = "region")
    @ApiModelProperty(value = "行政区域")
    private String region;

    /**
     * 客商简称
     */
    @TableField(value = "merchant_abbreviation")
    @ApiModelProperty(value = "客商简称")
    private String merchantAbbreviation;

    /**
     * 统一社会信用代码
     */
    @TableField(value = "unified_social_code")
    @ApiModelProperty(value = "统一社会信用代码")
    private String unifiedSocialCode;

    /**
     * 创建人
     */
    @TableField(value = "create_by", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @TableField(value = "create_at", fill = FieldFill.INSERT)
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 更新人
     */
    @TableField(value = "update_by", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 修改时间
     */
    @TableField(value = "update_at", fill = FieldFill.INSERT_UPDATE)
    @ApiModelProperty(value = "修改时间")
    private Long updateAt;

    /**
     * 是否删除：0： 未删除； 时间戳为删除时间
     */
    @TableField(value = "deleted")
    @ApiModelProperty(value = "是否删除：0： 未删除； 时间戳为删除时间")
    private Long deleted;
}