package com.cscec3b.iti.projectmanagement.server.entity;

import java.util.Date;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 智慧工地项目履约信息表
 */
@ApiModel(description = "智慧工地项目履约信息表")
@Data
public class SmartsiteProjectAgreementInfo {
    /**
     * 自增id
     */
    @ApiModelProperty(value = "自增id")
    private Integer id;

    /**
     * 与基础信息表关联
     */
    @ApiModelProperty(value = "与基础信息表关联")
    private Integer projectId;

    /**
     * 合同开工日期
     */
    @ApiModelProperty(value = "合同开工日期")
    private Long contractStartDate;

    /**
     * 合同竣工日期
     */
    @ApiModelProperty(value = "合同竣工日期")
    private Long contractEndDate;

    /**
     * 合同天数
     */
    @ApiModelProperty(value = "合同天数")
    private Integer contractDay;

    /**
     * 实际开工日期
     */
    @ApiModelProperty(value = "实际开工日期")
    private Long realityStartDate;

    /**
     * 实际竣工日期
     */
    @ApiModelProperty(value = "实际竣工日期")
    private Long realityEndDate;

    /**
     * 实际天数
     */
    @ApiModelProperty(value = "实际天数")
    private Integer realityDay;

    /**
     * 项目进场日期
     */
    @ApiModelProperty(value = "项目进场日期")
    private Long projectApprovalDate;

    /**
     * 业主下发开工令日期
     */
    @ApiModelProperty(value = "业主下发开工令日期")
    private Long ownerStartDate;

    /**
     * 监理下发开工令日期
     */
    @ApiModelProperty(value = "监理下发开工令日期")
    private Long managerStartDate;

    /**
     * 五方主体验收日期
     */
    @ApiModelProperty(value = "五方主体验收日期")
    private Long checkDate;

    /**
     * 竣工备案日期
     */
    @ApiModelProperty(value = "竣工备案日期")
    private Long recordDate;

    /**
     * 审批通过的信息集合
     */
    @ApiModelProperty(value = "审批通过的信息集合")
    private String displayInfo;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态")
    private String projectStatus;
}
