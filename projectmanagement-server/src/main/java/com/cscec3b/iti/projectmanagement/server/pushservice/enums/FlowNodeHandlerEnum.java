package com.cscec3b.iti.projectmanagement.server.pushservice.enums;

import com.cscec3b.iti.common.web.exception.FrameworkException;
import lombok.Getter;

import java.util.Arrays;

/**
 * 项目事件节点位置枚举
 *
 * <AUTHOR>
 * @date 2023/09/14 19:31
 **/

@Getter
public enum FlowNodeHandlerEnum {
	
	PRE("流程开始前", "pre", "流程开始前"),
	
	POST("流程开始后", "post", "流程开始后");
	
	
	/**
	 * 节点名称
	 */
	final String name;
	
	/**
	 * 编码
	 */
	final String code;
	
	/**
	 * 描述
	 */
	final String description;
	
	
	FlowNodeHandlerEnum(String name, String code, String description) {
		this.name = name;
		this.code = code;
		this.description = description;
	}

	public static FlowNodeHandlerEnum getByCode(String code) {
		return Arrays.stream(values()).filter(dataType -> dataType.getCode().equals(code)).findFirst()
				.orElseThrow(() -> new FrameworkException(-1, "事件节点位置类型不正确"));
	}
}
