package com.cscec3b.iti.projectmanagement.server.entity;

import com.fasterxml.jackson.annotation.JsonRawValue;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description 任务表实体
 * @date 2022/10/20
 */
@Data
@Accessors(chain = true)
public class Task extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    private Long id;

    /**
     * 关联id
     */
    private Long relationId;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 发起人
     */
    private String initPerson;

    /**
     * 经办人
     */
    private String handlerPerson;

    /**
     * 发起时间
     */
    private Long initTime;

    /**
     * 完成时间
     */
    private Long finishTime;

    /**
     * 状态(0:待办;1:在办;2:已办)
     */
    private Integer status;

    /**
     * 发起人页面参数
     */
    @JsonRawValue
    private String initPersonPageParam;

    /**
     * 经办人页面参数
     */
    @JsonRawValue
    private String handlerPersonPageParam;
}