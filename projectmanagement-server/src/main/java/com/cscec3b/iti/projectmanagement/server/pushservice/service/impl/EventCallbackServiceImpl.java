package com.cscec3b.iti.projectmanagement.server.pushservice.service.impl;

import static cn.hutool.core.text.CharSequenceUtil.toCamelCase;
import static com.cscec3b.iti.projectmanagement.server.constant.ApiConstants.BUSINESS_DATA_IS_NULL_MSG;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.groovy.util.Maps;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.model.req.ProjectEventCallBackReq;
import com.cscec3b.iti.model.resp.ProjectArchiveResp;
import com.cscec3b.iti.model.resp.ProjectBaseArchive;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ColumnDetail;
import com.cscec3b.iti.projectmanagement.api.dto.dto.EventBusinessColumn;
import com.cscec3b.iti.projectmanagement.api.dto.dto.SmartSiteRespEntity;
import com.cscec3b.iti.projectmanagement.api.dto.dto.enignproject.MDMGateWayResponse;
import com.cscec3b.iti.projectmanagement.api.dto.dto.enignproject.MdmApi;
import com.cscec3b.iti.projectmanagement.api.dto.dto.enignproject.MdmPushEntity;
import com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite.*;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectOpenEventNoticeReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectEventCallBackResp;
import com.cscec3b.iti.projectmanagement.server.config.OperationNoticeProperties;
import com.cscec3b.iti.projectmanagement.server.config.SmartSiteProperties;
import com.cscec3b.iti.projectmanagement.server.constant.ApiConstants;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.*;
import com.cscec3b.iti.projectmanagement.server.enums.ProjectProgressEnum;
import com.cscec3b.iti.projectmanagement.server.feign.MDMOpenApiFeign;
import com.cscec3b.iti.projectmanagement.server.mapper.BidApprovalMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.EngineeringProjectMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.EventBusinessColumnMapper;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectEventMapper;
import com.cscec3b.iti.projectmanagement.server.pushservice.consumer.ProjectPushConsumer;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.FlowNodeHandlerEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.enums.ProjectEventEnum;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.CpmProjectFlowEvent;
import com.cscec3b.iti.projectmanagement.server.pushservice.event.CpmProjectUpdateEvent;
import com.cscec3b.iti.projectmanagement.server.pushservice.service.IEventCallbackService;
import com.cscec3b.iti.projectmanagement.server.pushservice.service.IProjectEventSubscriberService;
import com.cscec3b.iti.projectmanagement.server.service.*;
import com.cscec3b.iti.projectmanagement.server.service.impl.MDMServiceImpl;
import com.cscec3b.iti.projectmanagement.server.util.MDMDataConverter;
import com.cscec3b.iti.projectmanagement.server.util.OpenApiInvoker;
import com.cscec3b.iti.retry.annotations.PmReTry;
import com.cscec3b.iti.taskmesage.dto.TaskAndMsgDto;
import com.cscec3b.iti.taskmesage.service.TaskAndMessageService;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Sets;
import com.odin.freyr.common.orika.BeanMapUtils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.map.MapUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 事件回调方法
 *
 * <AUTHOR>
 * @date 2023/04/18 22:26
 **/

@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(rollbackFor = Exception.class)
public class EventCallbackServiceImpl implements IEventCallbackService {

    private static final String AS = " as ";
    private static final String SUPPLEMENTARY_AGREEMENT = "supplementary_agreement";
    private final ProjectEventMapper projectEventMapper;

    private final EventBusinessColumnMapper eventBusinessColumnMapper;

    private final SmartSiteProperties smartSiteProperties;

    private final RestTemplate pmRestTemplate;

    private final ISmartSiteService smartSiteService;

    private final ProjectProgressService projectProgressService;

    private final IEventReceiveRecordService receiveRecordService;

    private final ApplicationEventPublisher publisher;

    private final ProjectService projectService;

    private final IProjectFlowEventRecordService flowEventRecordService;

    private final IProjectEventSubscriberService subscriberService;

    private final SmartProjectHookInfoService smartProjectHookInfoService;

    private final TaskAndMessageService taskAndMessageService;

    private final BidApprovalMapper bidApprovalMapper;

    private final MDMServiceImpl mdmService;

    private final OperationNoticeProperties noticeProperties;

    private final MappingErrorInfoService mappingErrorInfoService;

    private final EngineeringProjectMapper engineeringProjectMapper;

    private final MDMOpenApiFeign mdmOpenApiFeign;

    /**
     * 检查返回字段
     *
     * @param event 事件
     * @return {@link List }<{@link EventBusinessColumn }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    private static List<EventBusinessColumn> checkColumn(ProjectEvent event) {
        final List<EventBusinessColumn> eventBusinessColumns;
        try {
            final String businessColumn = event.getBusinessColumn();
            eventBusinessColumns = JsonUtils.jsonToList(businessColumn, EventBusinessColumn.class);
            if (CollectionUtils.isEmpty(eventBusinessColumns)) {
                // 事件msg配置异常
                throw new BusinessException(8010606);
            }
        } catch (Exception e) {
            throw new BusinessException(8010607);
        }
        return eventBusinessColumns;
    }

    @Override
    public ProjectEventCallBackResp eventCallback(Integer eventId, Integer projectId, String eventCode) {
        final ProjectEventCallBackResp eventCallBackResp = new ProjectEventCallBackResp();
        // 校验事件
        ProjectEvent event = checkEvent(eventId, eventCode);
        // 比对订阅的字段与数据表的字段是否一致，如存在不一致则不传入查询，防止不存在的字段导致SQL错误
        final List<EventBusinessColumn> eventBusinessColumns = checkColumn(event);

        // 事件配置
        final Map<String, EventBusinessColumn> eventColumnMap = eventBusinessColumns.stream()
                .collect(Collectors.toMap(EventBusinessColumn::getTable, o -> o, (o1, o2) -> o1));
        final Set<String> tables = eventColumnMap.keySet();
        // 获取数据表字段信息
        final List<EventBusinessColumn> databaseColumns = eventBusinessColumnMapper.databaseColumns(tables);
        // 比对订阅字段， 只取订阅字段中与数据表字段的交集
        databaseColumns.forEach(databaseColumn -> {
            // 数据表信息
            final String databaseTable = databaseColumn.getTable();
            final List<ColumnDetail> databaseColumnDetails = databaseColumn.getColumns();
            final List<String> tableColumns =
                    databaseColumnDetails.stream().map(ColumnDetail::getColumn).collect(Collectors.toList());
            // 事件配置的字段信息
            final EventBusinessColumn eventBusinessColumn = eventColumnMap.get(databaseTable);
            final List<ColumnDetail> columnDetails = eventBusinessColumn.getColumns();
            final List<String> eventColumns =
                    columnDetails.stream().map(ColumnDetail::getColumn).collect(Collectors.toList());

            // 取交集，即去掉事件字段在表中不存在的信息；
            eventColumns.retainAll(tableColumns);
            if (CollectionUtils.isEmpty(eventColumns)) {
                return;
            }
            // 增加驼峰别名
            final List<String> cameCaseColumns =
                    eventColumns.stream().map(c -> c + AS + toCamelCase(c)).collect(Collectors.toList());

            // 获取对应表的信息(因为查询条件的限制，暂无法动态)
            // 项目信息
            setProjectInfo(projectId, databaseTable, cameCaseColumns, eventBusinessColumn, eventCallBackResp);
            // 投标总结-
            setBidSummaryInfo(projectId, databaseTable, cameCaseColumns, eventBusinessColumn, eventCallBackResp);
            // 局内分包合同
            setBureauContractInfo(projectId, databaseTable, cameCaseColumns, eventBusinessColumn, eventCallBackResp);
            // 局内补充协议表
            setBureauSupplementaryAgreementInfo(projectId, databaseTable, cameCaseColumns, eventBusinessColumn,
                eventCallBackResp);
            // 合同定案表
            setContractInfo(projectId, databaseTable, cameCaseColumns, eventBusinessColumn, eventCallBackResp);
            // 补充协议supplementary_agreement
            setSupplementaryAgreementInfo(projectId, databaseTable, cameCaseColumns, eventBusinessColumn,
                eventCallBackResp);
            // 以下为返回了详细的事件字段配置及值
            // final Map<String, Object> finalTempMap = tempMap;
            // columnDetails.forEach(eventColumn -> {
            // final String column = eventColumn.getColumn();
            // final String camelCase = StrUtil.toCamelCase(column);
            // final Object val = finalTempMap.get(camelCase);
            // if (ObjectUtils.isNotEmpty(val)) {
            // eventColumn.setVal(val);
            // }
            // eventColumn.setCamelCase(camelCase);
            // });

        });

        return eventCallBackResp;
    }

    /**
     * 填充补充协议信息
     *
     * @param projectId           项目id
     * @param databaseTable       数据库表
     * @param cameCaseColumns     字段
     * @param eventBusinessColumn 事件字段
     * @param eventCallBackResp   事件回调职责
     * <AUTHOR>
     * @date 2023/08/21
     */
    private void setSupplementaryAgreementInfo(Integer projectId, String databaseTable, List<String> cameCaseColumns,
            EventBusinessColumn eventBusinessColumn, ProjectEventCallBackResp eventCallBackResp) {
        List<Map<String, Object>> tempMap;
        if (SUPPLEMENTARY_AGREEMENT.equals(databaseTable)) {
            tempMap = eventBusinessColumnMapper.getSupplementaryAgreementInfo(cameCaseColumns, projectId);
            // 赋值
            eventBusinessColumn.setTable(SUPPLEMENTARY_AGREEMENT);
            if (ObjectUtils.isNotEmpty(tempMap) && ObjectUtils.isNotEmpty(tempMap.get(0))) {
                eventCallBackResp.setSupplementaryAgreementInfo(tempMap);
            }
        }
    }

    /**
     * 设置合同信息
     *
     * @param projectId           项目id
     * @param databaseTable       数据库表
     * @param cameCaseColumns     字段
     * @param eventBusinessColumn 事件字段
     * @param eventCallBackResp   事件回调职责
     * <AUTHOR>
     * @date 2023/08/21
     */
    private void setContractInfo(Integer projectId, String databaseTable, List<String> cameCaseColumns,
            EventBusinessColumn eventBusinessColumn, ProjectEventCallBackResp eventCallBackResp) {
        List<Map<String, Object>> tempMap;
        if (Constants.CONTRACT_TABLE.equals(databaseTable)) {
            tempMap = eventBusinessColumnMapper.getContractInfo(cameCaseColumns, projectId);
            // 赋值
            eventBusinessColumn.setTable(Constants.CONTRACT_TABLE);
            if (ObjectUtils.isNotEmpty(tempMap) && ObjectUtils.isNotEmpty(tempMap.get(0))) {
                eventCallBackResp.setContractInfo(tempMap);
            }
        }
    }

    /**
     * 局补充协议信息
     *
     * @param projectId           项目id
     * @param databaseTable       数据库表
     * @param cameCaseColumns     字段
     * @param eventBusinessColumn 事件字段
     * @param eventCallBackResp   事件回调职责
     * <AUTHOR>
     * @date 2023/08/21
     */
    private void setBureauSupplementaryAgreementInfo(Integer projectId, String databaseTable, List<String> cameCaseColumns,
            EventBusinessColumn eventBusinessColumn, ProjectEventCallBackResp eventCallBackResp) {
        List<Map<String, Object>> tempMap;
        if (Constants.BUREAU_SUPPLEMENTARY_AGREEMENT_TABLE.equals(databaseTable)) {
            tempMap = eventBusinessColumnMapper.getBureauSupplementaryAgreementInfo(cameCaseColumns, projectId);
            // 赋值
            eventBusinessColumn.setTable(Constants.BUREAU_SUPPLEMENTARY_AGREEMENT_TABLE);
            if (ObjectUtils.isNotEmpty(tempMap) && ObjectUtils.isNotEmpty(tempMap.get(0))) {
                eventCallBackResp.setBureauSupplementaryAgreementInfo(tempMap);
            }
        }
    }

    /**
     * 局内分包合同
     *
     * @param projectId           项目id
     * @param databaseTable       数据库表
     * @param cameCaseColumns     字段信息
     * @param eventBusinessColumn 事件商业专栏
     * @param eventCallBackResp   事件回调职责
     * <AUTHOR>
     * @date 2023/08/21
     */
    private void setBureauContractInfo(Integer projectId, String databaseTable, List<String> cameCaseColumns,
            EventBusinessColumn eventBusinessColumn, ProjectEventCallBackResp eventCallBackResp) {
        List<Map<String, Object>> tempMap;
        if (Constants.BUREAU_CONTRACT_TABLE.equals(databaseTable)) {
            tempMap = eventBusinessColumnMapper.getBureauContractInfo(cameCaseColumns, projectId);
            // 赋值
            eventBusinessColumn.setTable(Constants.BUREAU_CONTRACT_TABLE);
            if (CollectionUtils.isNotEmpty(tempMap) && ObjectUtils.isNotEmpty(tempMap.get(0))) {
                eventCallBackResp.setBureauContractInfo(tempMap);
            }
        }
    }

    /**
     * 投标总结
     *
     * @param projectId           项目id
     * @param databaseTable       数据库表
     * @param cameCaseColumns     字段信息
     * @param eventBusinessColumn 事件字段
     * @param eventCallBackResp   事件回调职责
     * <AUTHOR>
     * @date 2023/08/21
     */
    private void setBidSummaryInfo(Integer projectId, String databaseTable, List<String> cameCaseColumns,
            EventBusinessColumn eventBusinessColumn, ProjectEventCallBackResp eventCallBackResp) {
        List<Map<String, Object>> tempMap;
        if (Constants.BID_SUMMARY_TABLE.equals(databaseTable)) {
            tempMap = eventBusinessColumnMapper.getBidSummaryInfo(cameCaseColumns, projectId);
            // 赋值
            eventBusinessColumn.setTable(Constants.BID_SUMMARY_TABLE);
            if (ObjectUtils.isNotEmpty(tempMap) && ObjectUtils.isNotEmpty(tempMap.get(0))) {
                eventCallBackResp.setBidSummaryInfo(tempMap);
            }
        }
    }

    /**
     * 填充项目信息
     *
     * @param projectId           项目id
     * @param databaseTable       数据库表
     * @param cameCaseColumns     字段信息
     * @param eventBusinessColumn 事件字段
     * @param eventCallBackResp   事件回调职责
     * <AUTHOR>
     * @date 2023/08/21
     */
    private void setProjectInfo(Integer projectId, String databaseTable, List<String> cameCaseColumns,
            EventBusinessColumn eventBusinessColumn, ProjectEventCallBackResp eventCallBackResp) {
        List<Map<String, Object>> tempMap;
        if (Constants.PROJECT_TABLE.equals(databaseTable)) {
            tempMap = eventBusinessColumnMapper.getProjectInfo(cameCaseColumns, projectId);
            // 赋值
            eventBusinessColumn.setTable(Constants.PROJECT_TABLE);
            if (ObjectUtils.isNotEmpty(tempMap)) {
                eventCallBackResp.setProjectInfo(tempMap);
            }
        }
    }

    /**
     * 检查事件
     *
     * @param eventId   标识符
     * @param eventCode 事件编码
     * @return {@link ProjectEvent }
     * <AUTHOR>
     * @date 2023/08/21
     */
    private ProjectEvent checkEvent(Integer eventId, String eventCode) {
        ProjectEvent event;
        if (StringUtils.isNotBlank(eventCode)) {
            event = projectEventMapper.selectByEventCode(eventCode);
        } else {
            event = projectEventMapper.selectByPrimaryKey(eventId);
        }
        if (ObjectUtils.isEmpty(event)) {
            throw new BusinessException(8010605);
        }
        return event;
    }

    /**
     * 外部系统事件通知
     * <AUTHOR>
     * @date 2023/05/10 15:38
     * @param noticeReq 通知参数
     * @return java.lang.Boolean
     */
    @Override
    public Boolean openEventNotice(ProjectOpenEventNoticeReq noticeReq) {
        final String projectId = noticeReq.getProjectId();
        if (ObjectUtils.isEmpty(projectId)) {
            // 项目中心项目id 和 项目云枢id不能为空
            throw new BusinessException(-1);
        }
        // 事件触发时间
        final Long triggerTime = noticeReq.getTriggerTime();
        final String projectYunshuOrgId = noticeReq.getProjectYunshuOrgId();
        // 事件类型转换
        final String eventCode = noticeReq.getEventCode();
        final ProjectEventEnum eventEnum = ProjectEventEnum.getEnumByCode(eventCode);
        if (ObjectUtils.isEmpty(eventEnum)) {
            throw new BusinessException(8010606);
        }
        // 生成调用记录
        final ProjectEventReceiveRecord receiveRecord = new ProjectEventReceiveRecord();
        final long currentTime = Instant.now().toEpochMilli();
        receiveRecord.setProjectId(Long.valueOf(noticeReq.getProjectId())).setEventCode(eventCode)
            .setTriggerTime(triggerTime).setReceiveParam(JsonUtils.toJsonStr(noticeReq))
                .setReceiveTime(currentTime);
        // 保存智慧工地全量信息
        final ProjectDetailOutResp smartProjectInfo;
        try {
            smartProjectInfo = getSmartProjectInfo(projectId, receiveRecord, projectYunshuOrgId);
        } catch (Exception e) {
            log.error("获取智慧工地项目信息失败", e);
            receiveRecord.setErrMsg(e.getMessage()).setApiResult(Constants.NUMBER_ZERO);
            return Boolean.FALSE;
        } finally {
            receiveRecordService.insert(receiveRecord);
        }

        ProjectProgress dbProjectProgress = projectProgressService.selectProjectProgress(Long.valueOf(projectId));
        final ProjectProgress progress = new ProjectProgress().setProjectId(Long.valueOf(projectId));
        // 项目监控信息
        final Project project = projectService.selectById(Long.valueOf(projectId));

        switch (eventEnum) {
            // 挂接
            // 挂接模式下反查到的项目信息是主项目的信息
            case HOOK:
                // 当前项目是24年前进入项目中心的 属于在途，给运维/秦琪发个消息，让运维判断项目是标准立项(修订)，还是挂接(创建项目部)
                // 当前项目是24年后进入项目中心的 属于历史项目，从挂接入口 然后发待办给工地返的人，让他们或转办的人去创建经济线项目 ？
                /**
                 * 1. 维护挂接关系；2. 判断是在途项目，还是非在途项目；<br>
                 * 3. 非在途项目，发起待办，要求发起人在待办里创建项目部； 4. 创建项目部时，使用主项目的财商编码, 由发起人选择上级部门，项目部名称 5. 如果财商编码为空，刚不允许创建
                 */
                // progress.setSmartApproveStatus(ProjectProgressEnum.SMART_SITE_HOOK.getDictCode()); // 暂不设置，由创建项目部
                // 或修改云枢组织时填充
                try {
                    // final Project project = projectService.selectById(Long.valueOf(projectId));
                    final Long createAt = project.getCreateAt();
                    // 生成并保存挂接关系
                    final SmartProjectHookInfo smartProjectHookInfo = genAndSaveHookInfo(smartProjectInfo, projectId);
                    // 判断是否2024年 (1704038400000L)前的项目
                    if (Objects.nonNull(createAt) && createAt < 1704038400000L) {
                        // 2024年以前项目有可能是在途项目，需要再判断
                        log.info("无法判断是否在途项目，给运维人员发起待办");
                        if (CollectionUtils.isNotEmpty(noticeProperties.getNotice())) {
                            log.info("待办通知列表:{}", noticeProperties.getNotice());
                            sendTodoTask(smartProjectHookInfo, noticeProperties.getNotice(), true);
                        }
                        smartProjectHookInfo.setHookProjectType("疑似在途-待人工审核");
                    } else {
                        // 非在途项目 发起待办
                        log.info("非在途项目，给挂接发起人发起待办");
                        // 发送待办任务及消息
                        sendTodoTask(smartProjectHookInfo, Collections.emptyList(), false);
                    }
                    // 立项时间
                    // progress.setToUcTime(Instant.now().toEpochMilli());
                    // // 更新项目信息
                    // updateAndNotice(smartProjectInfo, receiveRecord, progress, eventEnum);
                    // // 更新项目立项进度
                    // projectProgressService.smartsiteProjectApprovalUpdate(progress);
                    receiveRecordService.updateById(receiveRecord);
                    smartProjectHookInfoService.updateById(smartProjectHookInfo);
                } catch (NumberFormatException e) {
                    receiveRecord.setApiResult(Constants.NUMBER_ZERO).setErrMsg(e.getMessage());
                    throw e;
                } finally {
                    String errMsg = receiveRecord.getErrMsg();
                    // 如果错误信息超过1000字符，则截取前1000个字符
                    if (StringUtils.length(errMsg) > 1000) {
                        errMsg = StringUtils.substring(errMsg, 0, 1000);
                    }
                    receiveRecord.setErrMsg(errMsg);
                    receiveRecordService.updateById(receiveRecord);
                }
                break;
                // 此处不需要 break
                // 在途项目 继续走立项流程
                // 立项
            case INITIATION:
                // 已立项 后不再接受事件通知
                // 1. 更新项目立项进度
                // 2. 更新项目信息
                // 3. 触发工地立项事件
                // 4. 视情况触发标准立项
                try {
                    final Boolean approvalStatus =
                        Optional.ofNullable(dbProjectProgress).map(ProjectProgress::getSmartApproveStatus)
                            .map(status -> Objects.equals(status, ProjectProgressEnum.COMPLETED.getDictCode()))
                            .orElse(false);
                    if (approvalStatus) {
                        log.error("当前项目{}已经处于立项状态，不再接收立项事件", projectId);
                        receiveRecord.setApiResult(Constants.NUMBER_ZERO).setErrMsg("当前项目已经处于立项状态，不再接收立项事件通知");
                    } else {
                        // 更新项目立项状态
                        if (Objects.isNull(progress.getSmartApproveStatus())) {
                            progress.setSmartApproveStatus(ProjectProgressEnum.COMPLETED.getDictCode());
                        }
                        // 立项时间
                        progress.setToUcTime(Instant.now().toEpochMilli());
                        // 更新项目信息 触发立项事件
                        updateAndNotice(smartProjectInfo, receiveRecord, progress, eventEnum);
                        // 更新项目立项进度
                        projectProgressService.smartsiteProjectApprovalUpdate(progress);
                        projectService.setWhetherProjectHasBeenCompleted(progress.getProjectId());
                    }
                } catch (Exception e) {
                    receiveRecord.setApiResult(Constants.NUMBER_ZERO).setErrMsg(e.getMessage());
                    throw e;
                } finally {
                    String errMsg = receiveRecord.getErrMsg();
                    // 如果错误信息超过1000字符，则截取前1000个字符
                    if (StringUtils.length(errMsg) > 1000) {
                        errMsg = StringUtils.substring(errMsg, 0, 1000);
                    }
                    receiveRecord.setErrMsg(errMsg);
                    receiveRecordService.updateById(receiveRecord);
                }
                break;
            // 更新
            case SMART_UPDATE:
                try {
                    // 更新前先比对云枢id，不一致则不更新，并记录项目关联关系错误记录
                    if (!Objects.equals(projectYunshuOrgId, project.getYunshuOrgId())) {
                        log.error("云枢id不一致，不更新项目信息");
                        receiveRecord.setApiResult(Constants.NUMBER_ZERO).setErrMsg("映射错误：云枢id不一致，不更新项目信息");
                        final MappingErrorInfo errorInfo = MappingErrorInfo.builder().type(1)
                            .requestTime(LocalDateTime.now()).projectId(Long.valueOf(projectId))
                            .originalValue(project.getYunshuOrgId()).revisedValue(projectYunshuOrgId)
                            .eventReceiveRecordId(receiveRecord.getId()).requestTime(LocalDateTime.now()).build();
                        mappingErrorInfoService.save(errorInfo);
                        return Boolean.FALSE;
                    }

                    updateProject(receiveRecord, smartProjectInfo);
                    // 智慧工地更新 后置
                    publisher.publishEvent(new CpmProjectFlowEvent(this, Long.valueOf(projectId),
                            FlowNodeEnum.SMART_SITE_SEGMENT_UPDATE, FlowNodeHandlerEnum.POST,
                            FlowNodeDataTypeEnum.UPDATE));
                } catch (Exception e) {
                    String errMsg = StringUtils.substring(e.getMessage(), 0, 1000);
                    receiveRecord.setApiResult(Constants.NUMBER_ZERO).setErrMsg(errMsg);
                } finally {
                    receiveRecordService.updateById(receiveRecord);
                }


                break;
            case ENGINE_UPDATE:
                handleEngineUpdate(smartProjectInfo, receiveRecord);
                break;
            //            case CANCEL:
            //                final ProjectProgress oldProgress =
            //                        projectProgressService.selectProjectProgress(Long.valueOf(projectId));
            //                oldProgress.setSmartApproveStatus(ProjectProgressEnum.SMART_SITE_CANCEL.getDictCode
            //                ());
            //                // 更新项目立项进度
            ////                projectProgressService.smartsiteProjectApprovalUpdate(oldProgress);
            default:
                break;

        }

        return Boolean.TRUE;
    }

    /**
     * @param smartProjectHookInfo 挂接信息
     * @param notice               待办通知人列表
     * @param isNotice             是否需要通知运维人员，true 需要通知运维人员，false 不需要通知运维人员
     */
    private void sendTodoTask(SmartProjectHookInfo smartProjectHookInfo, List<String> notice, boolean isNotice) {
        final Long currentProjectId = smartProjectHookInfo.getCurrentProjectId();
        final Project project = projectService.selectById(currentProjectId);
        String appLink = smartSiteProperties.getTaskNotice().getAppLink();
        final String projectHook = "project_hook";
        appLink = String.format(appLink, smartProjectHookInfo.getId(), projectHook,
                isNotice);
        String webLink = smartSiteProperties.getTaskNotice().getWebLink();
        webLink = String.format(webLink, smartProjectHookInfo.getId(), projectHook,
                isNotice);
        final String msgConfigCode = smartSiteProperties.getTaskNotice().getMsgConfigCode();
        final String billId = String.valueOf(smartProjectHookInfo.getId());
        final String title = isNotice ? "项目挂接-疑似在途项目判断" : "项目挂接-经济线项目部创建";
        final Set<String> targetUser = CollectionUtils.isNotEmpty(notice) ? Sets.newHashSet(notice)
            : Collections.singleton(smartProjectHookInfo.getHookUserCode());
        final Map<String, String> payload = Maps.of("projectName", project.getCpmProjectName(), "title", title);
        final TaskAndMsgDto taskAndMsgDto = TaskAndMsgDto.builder().webLink(webLink).appLink(appLink)
                .configCode(msgConfigCode).payload(JSONUtil.toJsonStr(payload)).bpmInstanceId(billId)
                .targetUsers(targetUser).billId(billId).billType(projectHook).billTypeName("项目挂接").startUserName("项目中心")
            .startTime(LocalDateTime.now()).title(title)
            .retryTimeCycle(smartSiteProperties.getTaskNotice().getRetryCycle()).build();
        taskAndMessageService.sendTodoTaskAndMsg(taskAndMsgDto);
    }

    private SmartProjectHookInfo genAndSaveHookInfo(ProjectDetailOutResp smartProjectInfo, String projectId) {
        final ProjectHookInfo projectHookInfo = smartProjectInfo.getProjectHookInfo();
        final SmartProjectHookInfo smartProjectHookInfo = SmartProjectHookInfo.builder()
            .currentProjectId(Long.valueOf(projectId))
            .currentProjectKey(smartProjectInfo.getProjectInfo().getCpmProjectKey())
            .hookYunshuOrgId(smartProjectInfo.getProjectInfo().getYunshuOrgId())
            .hookUserCode(smartProjectInfo.getHookUser()).hookTime(Instant.now().toEpochMilli())
            .createBy(Optional.ofNullable(projectHookInfo).map(ProjectHookInfo::getCreateName).orElse(null))
            .hookProjectType(smartProjectInfo.getHookProjectType()).build();
        smartProjectHookInfoService.save(smartProjectHookInfo);
        return smartProjectHookInfo;
    }

    /**
     * 立项事件
     *
     * @param smartProjectInfo 工地项目信息
     * @param receiveRecord 接收记录
     * @param progress 进度
     * @param eventEnum 事件枚举
     */
    private void updateAndNotice(ProjectDetailOutResp smartProjectInfo, ProjectEventReceiveRecord receiveRecord,
        ProjectProgress progress, ProjectEventEnum eventEnum) {
        try {
            // 更新项目信息
            updateProject(receiveRecord, smartProjectInfo);
            // 智慧工地立项 后置
            publisher.publishEvent(new CpmProjectFlowEvent(this, progress.getProjectId(),
                    FlowNodeEnum.SMART_SITE_SEGMENT, FlowNodeHandlerEnum.POST,
                    FlowNodeDataTypeEnum.CREATE));
        } catch (Exception e) {
            String errMsg = StringUtils.substring(e.getMessage(), 0, 1000);
            progress.setSmartApproveStatus(ProjectProgressEnum.IN_PROGRESS.getDictCode());
            progress.setSmartRemarks(eventEnum.getZhCN() + " 失败，原因：" + errMsg);
            receiveRecord.setApiResult(Constants.NUMBER_ZERO).setErrMsg(errMsg);
            throw e;
        }
    }


    /**
     * 工地项目更新
     *
     * @param projectId     项目id
     * @param receiveRecord 接收记录
     * @return boolean
     */
    @Deprecated
    @PmReTry
    private void smartProjectUpdate(final String projectId, final ProjectEventReceiveRecord receiveRecord,
            String yunshuOrgId) {
        final ProjectDetailOutResp detailOutResp;
        final SmartSiteProjectSyncDto syncDto;
        try {
            // 去智慧工地查询立项信息
            detailOutResp = getProjectInitInfo2Update(projectId, receiveRecord);
            if (StringUtils.isNotBlank(yunshuOrgId) && StringUtils.isBlank(detailOutResp.getProjectInfo().getYunshuOrgId())) {
                detailOutResp.getProjectInfo().setYunshuOrgId(yunshuOrgId);
            }
            // 保存智慧工地全量信息
            smartSiteService.addSmartSiteOutResp(detailOutResp, Long.valueOf(projectId));
            // 组装项目更新参数
            syncDto = smartSiteService.getSmartSiteProjectSyncDto(detailOutResp.getProjectInfo(),
                    detailOutResp.getProjectBasicInfo(), detailOutResp.getProjectContactInfo(),
                    detailOutResp.getProjectAgreementInfo(), detailOutResp.getProjectContractInfo(),
                    detailOutResp.getProjectRelyInfo());
            // 更新项目中心数据
            smartSiteService.syncSmartSiteProject(syncDto, receiveRecord);
        } catch (Exception e) {
            String errMsg = StringUtils.substring(e.getMessage(), 0, 1000);
            receiveRecord.setApiResult(Constants.NUMBER_ZERO).setErrMsg(errMsg);
            throw e;
        }
    }

    private ProjectDetailOutResp getSmartProjectInfo(String projectId, ProjectEventReceiveRecord receiveRecord,
        String yunshuOrgId) {
        // 去智慧工地查询立项信息
        ProjectDetailOutResp detailOutResp = getProjectInitInfo2Update(projectId, receiveRecord);
        if (StringUtils.isNotBlank(yunshuOrgId) && StringUtils.isBlank(detailOutResp.getProjectInfo().getYunshuOrgId())) {
            detailOutResp.getProjectInfo().setYunshuOrgId(yunshuOrgId);
        }
        // 保存智慧工地全量信息
        smartSiteService.addSmartSiteOutResp(detailOutResp, Long.valueOf(projectId));
        return detailOutResp;
        // 组装项目更新参数
        // updateProject(receiveRecord, detailOutResp);
    }

    private void updateProject(ProjectEventReceiveRecord receiveRecord, ProjectDetailOutResp detailOutResp) {
        SmartSiteProjectSyncDto syncDto = smartSiteService.getSmartSiteProjectSyncDto(detailOutResp.getProjectInfo(),
                detailOutResp.getProjectBasicInfo(), detailOutResp.getProjectContactInfo(),
                detailOutResp.getProjectAgreementInfo(), detailOutResp.getProjectContractInfo(),
                detailOutResp.getProjectRelyInfo());
        // 更新项目中心数据
        smartSiteService.syncSmartSiteProject(syncDto, receiveRecord);
    }

    /**
     * 工地项目立项
     *
     * @param projectId     项目id
     * @param receiveRecord 接收记录
     * @param eventEnum     事件类型
     * @return boolean
     */
    private void smartProjectApproval(final String projectId, final ProjectEventReceiveRecord receiveRecord,
            final ProjectEventEnum eventEnum, String yunshuOrgId) {
        final ProjectDetailOutResp detailOutResp;
        final SmartSiteProjectSyncDto syncDto;
        final ProjectProgress progress = new ProjectProgress().setProjectId(Long.valueOf(projectId));
        // 已立项 或 已挂接后不再接受事件通知
        final ProjectProgress projectProgress = projectProgressService.selectProjectProgress(Long.valueOf(projectId));
        if (ObjectUtils.isNotEmpty(projectProgress) && (Objects.equals(projectProgress.getSmartApproveStatus(),
                ProjectProgressEnum.COMPLETED.getDictCode())
                || Objects.equals(projectProgress.getSmartApproveStatus(),
                ProjectProgressEnum.SMART_SITE_HOOK.getDictCode()))) {
            return;
        }

        try {
            // 保存智慧工地全量信息
            detailOutResp = getProjectInitInfo2Update(projectId, receiveRecord);
            if (StringUtils.isNotBlank(yunshuOrgId) && StringUtils.isBlank(detailOutResp.getProjectInfo().getYunshuOrgId())) {
                detailOutResp.getProjectInfo().setYunshuOrgId(yunshuOrgId);
            }
            // 去智慧工地查询立项信息
            smartSiteService.addSmartSiteOutResp(detailOutResp, Long.valueOf(projectId));
            // 组装项目更新参数
            syncDto = smartSiteService.getSmartSiteProjectSyncDto(detailOutResp.getProjectInfo(),
                    detailOutResp.getProjectBasicInfo(), detailOutResp.getProjectContactInfo(),
                    detailOutResp.getProjectAgreementInfo(), detailOutResp.getProjectContractInfo(),
                    detailOutResp.getProjectRelyInfo());
            // 更新项目中心数据
            smartSiteService.syncSmartSiteProject(syncDto, receiveRecord);
            //立项和挂接状态
            if (ProjectEventEnum.INITIATION.equals(eventEnum)) {
                progress.setSmartApproveStatus(ProjectProgressEnum.COMPLETED.getDictCode());
            } else {
                progress.setSmartApproveStatus(ProjectProgressEnum.SMART_SITE_HOOK.getDictCode());
            }
            progress.setToUcTime(Instant.now().toEpochMilli());
            // 智慧工地立项 后置
            publisher.publishEvent(new CpmProjectFlowEvent(this, progress.getProjectId(),
                    FlowNodeEnum.SMART_SITE_SEGMENT, FlowNodeHandlerEnum.POST, FlowNodeDataTypeEnum.CREATE));
        } catch (Exception e) {
            String errMsg = StringUtils.substring(e.getMessage(), 0, 1000);
            progress.setSmartApproveStatus(ProjectProgressEnum.IN_PROGRESS.getDictCode());
            progress.setSmartRemarks("立项失败：" + errMsg);
            receiveRecord.setApiResult(Constants.NUMBER_ZERO).setErrMsg(errMsg);
        } finally {
            // 更新项目立项进度
            projectProgressService.smartsiteProjectApprovalUpdate(progress);
            // 更新项目立项状态
            projectService.setWhetherProjectHasBeenCompleted(progress.getProjectId());
        }
    }

    @Override
    public ProjectArchiveResp flowEventCallback(ProjectEventCallBackReq callBackReq) {
        log.debug("flowEventCallback:{}", callBackReq);
        // 通过订阅者id,消息id, flowNodeDataType等判断是否配置是否正确
        // 获取消费者消息
        final ProjectEventSubscribe eventSubscribe =
            subscriberService.getById(Long.valueOf(callBackReq.getConsumerId()));
        // 获取触发点消息
        final ProjectFlowEventRecord flowEventRecord =
            flowEventRecordService.selectByPrimaryKey(callBackReq.getRequestId());
        if (ObjectUtils.isEmpty(flowEventRecord)) {
            // 消息不存在
            throw new BusinessException(-1);
        }

        // final String dataType = callBackReq.getFlowDataTypeCode();
        final String projectArchive = flowEventRecord.getProjectArchive();
        return JsonUtils.jsonToBean(projectArchive, ProjectArchiveResp.class);
        // if (null == callBackResp) {
        //     throw new BusinessException(-1);
        // }
        // ProjectArchiveResp resultResp = null;
        // if (FlowNodeDataTypeEnum.UPDATE.getCode().equals(dataType)) {
        //     final String flowNodeCode = flowEventRecord.getFlowNodeCode();
        //     final FlowNodeEnum nodeEnum = FlowNodeEnum.getByCode(flowNodeCode);
        //     resultResp = getProjectArchiveResp(callBackReq.getProjectId(), nodeEnum, callBackResp);
        // } else {
        //     resultResp = callBackResp;
        // }
        // return resultResp;
    }

    @Override
    public ProjectArchiveResp getProjectArchiveUpdateSegment(final Long projectId, final FlowNodeEnum nodeEnum,
                                                             final ProjectArchiveResp archiveResp) {
        // 将 archiveResp 转换为 ProjectBaseInfo
        ProjectBaseArchive projectBaseArchive = BeanMapUtils.map(archiveResp, ProjectBaseArchive.class);

        // 将 archiveResp 转换为 Map
        final Map<String, Object> archiveRespMap = BeanUtil.beanToMap(archiveResp);

        // 查找符合条件的 entry
        Optional<Map.Entry<String, Object>> matchingEntry = archiveRespMap.entrySet().stream()
                .filter(entry -> entry.getKey().equals(nodeEnum.getFiled()) && Objects.nonNull(entry.getValue()))
                .findFirst();

        // 如果找到匹配的 entry，则创建新的 ProjectArchiveResp 并复制属性
        if (matchingEntry.isPresent()) {
            Map.Entry<String, Object> entry = matchingEntry.get();
            ProjectArchiveResp newArchiveResp = BeanUtil.mapToBean(ImmutableMap.of(entry.getKey(), entry.getValue()),
                    ProjectArchiveResp.class, false, CopyOptions.create());
            BeanUtil.copyProperties(projectBaseArchive, newArchiveResp);
            return newArchiveResp;
        }

        // 如果没有找到匹配的 entry，则直接返回 projectBaseArchive 转换后的 ProjectArchiveResp
        return BeanMapUtils.map(projectBaseArchive, ProjectArchiveResp.class);
    }

    /**
     * 去智慧工地查询项目信息
     * <AUTHOR>
     * @date 2023/05/10 15:53
     * @param projectId
     * @return java.lang.Boolean
     */
    private ProjectDetailOutResp getProjectInitInfo2Update(String projectId, ProjectEventReceiveRecord receiveRecord) {
        final String initProjectCallbackUrl = smartSiteProperties.getInitProjectCallbackUrl();
        // 项目监控信息

        ParameterizedTypeReference<SmartSiteRespEntity<ProjectDetailOutResp>> responseBodyType =
            new ParameterizedTypeReference<SmartSiteRespEntity<ProjectDetailOutResp>>() { };

        final Map<Object, Object> apiParamMap = MapUtil.builder().put("projectId", projectId).build();

        final HttpEntity<MultiValueMap<String, Object>> httpEntity = ProjectPushConsumer
                .apiInvoker(OpenApiInvoker.encryptData(smartSiteProperties, apiParamMap, initProjectCallbackUrl));
        final String url = smartSiteProperties.getHost() + smartSiteProperties.getServiceName();
        final ResponseEntity<SmartSiteRespEntity<ProjectDetailOutResp>> responseEntity;
        try {
            // 请求智慧工地接口
            responseEntity = pmRestTemplate.exchange(url, HttpMethod.POST, httpEntity, responseBodyType);
        } catch (RestClientException e) {
            final String msg = String.format(ApiConstants.REQUEST_ERROR_MSG, e.getMessage());
            throw new FrameworkException(-1, msg);
        }

        final int statusCodeValue = responseEntity.getStatusCodeValue();
        if (statusCodeValue != HttpStatus.OK.value()) {
            final String msg = String.format(ApiConstants.HTTP_RESP_ERROR_MSG, statusCodeValue);
            throw new FrameworkException(statusCodeValue, msg);
        }
        final SmartSiteRespEntity<ProjectDetailOutResp> resultEntity = responseEntity.getBody();
        if (!Objects.isNull(resultEntity)) {
            if (!resultEntity.isSuccess()) {
                final String msg = String.format(ApiConstants.BUSINESS_ERROR_MSG, resultEntity.getErrorCode(),
                        resultEntity.getErrorMsg());
                throw new FrameworkException(-1, msg);
            }
            final ProjectDetailOutResp detailOutResp = resultEntity.getData();
            if (ObjectUtils.isEmpty(detailOutResp) || !ObjectUtils.allNotNull(detailOutResp.getProjectInfo(),
                    detailOutResp.getProjectBasicInfo(), detailOutResp.getProjectContactInfo(),
                    detailOutResp.getProjectAgreementInfo())) {
                final String msg = String.format(BUSINESS_DATA_IS_NULL_MSG, resultEntity);
                throw new FrameworkException(-1, msg);
            }
            //
            receiveRecord.setPullResult(JsonUtils.toJsonStr(detailOutResp));
            return detailOutResp;
        }
        final String errorMsg = String.format(ApiConstants.REQUEST_ERROR_MSG, statusCodeValue);
        throw new FrameworkException(statusCodeValue, errorMsg);
    }

    @Override
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public ProjectDetailOutResp getProjectInitInfo2Update(String cmpProjectKey) {
        final String initProjectCallbackUrl = smartSiteProperties.getInitProjectCallbackUrl();

        ParameterizedTypeReference<SmartSiteRespEntity<ProjectDetailOutResp>> responseBodyType =
            new ParameterizedTypeReference<SmartSiteRespEntity<ProjectDetailOutResp>>() {};

        final Map<Object, Object> apiParamMap = MapUtil.builder().put("cpmProjectKey", cmpProjectKey).build();

        final HttpEntity<MultiValueMap<String, Object>> httpEntity = ProjectPushConsumer
            .apiInvoker(OpenApiInvoker.encryptData(smartSiteProperties, apiParamMap, initProjectCallbackUrl));
        final String url = smartSiteProperties.getHost() + smartSiteProperties.getServiceName();
        final ResponseEntity<SmartSiteRespEntity<ProjectDetailOutResp>> responseEntity;
        try {
            // 请求智慧工地接口
            responseEntity = pmRestTemplate.exchange(url, HttpMethod.POST, httpEntity, responseBodyType);
        } catch (RestClientException e) {
            final String msg = String.format(ApiConstants.REQUEST_ERROR_MSG, e.getMessage());
            throw new FrameworkException(-1, msg);
        }

        final int statusCodeValue = responseEntity.getStatusCodeValue();
        if (statusCodeValue != HttpStatus.OK.value()) {
            final String msg = String.format(ApiConstants.HTTP_RESP_ERROR_MSG, statusCodeValue);
            throw new FrameworkException(statusCodeValue, msg);
        }
        final SmartSiteRespEntity<ProjectDetailOutResp> resultEntity = responseEntity.getBody();
        if (!Objects.isNull(resultEntity)) {
            if (!resultEntity.isSuccess()) {
                final String msg = String.format(ApiConstants.BUSINESS_ERROR_MSG, resultEntity.getErrorCode(),
                    resultEntity.getErrorMsg());
                throw new FrameworkException(-1, msg);
            }
            final ProjectDetailOutResp detailOutResp = resultEntity.getData();
            if (ObjectUtils.isEmpty(detailOutResp)
                || !ObjectUtils.allNotNull(detailOutResp.getProjectInfo(), detailOutResp.getProjectBasicInfo(),
                    detailOutResp.getProjectContactInfo(), detailOutResp.getProjectAgreementInfo())) {
                final String msg = String.format(BUSINESS_DATA_IS_NULL_MSG, resultEntity);
                throw new FrameworkException(-1, msg);
            }
            //
            return detailOutResp;
        }
        final String errorMsg = String.format(ApiConstants.REQUEST_ERROR_MSG, statusCodeValue);
        throw new FrameworkException(statusCodeValue, errorMsg);
    }

    /**
     * 从施工参数中提取建筑面积
     * 
     * @param projectParameter 施工参数
     * @return 建筑面积
     */
    private BigDecimal extractBuildingAreaFromParameter(String projectParameter) {
        if (StringUtils.isBlank(projectParameter)) {
            return null;
        }
        try {
            // 尝试匹配建筑面积相关的数值，支持多种格式
            // 例如：建筑面积：12345.67平方米、面积12345.67㎡、12345.67m²等
            Pattern pattern = Pattern.compile("(?:建筑面积[：:]?|面积[：:]?)([\\d,]+\\.?\\d*)(?:平方米|㎡|m²|万平方米)?");
            Matcher matcher = pattern.matcher(projectParameter);
            if (matcher.find()) {
                String areaStr = matcher.group(1).replace(",", "");
                BigDecimal area = new BigDecimal(areaStr);
                // 如果包含"万"，需要乘以10000
                if (projectParameter.contains("万平方米")) {
                    area = area.multiply(new BigDecimal("10000"));
                }
                return area;
            }

            // 如果没有明确标识，尝试提取第一个数值作为面积
            Pattern numberPattern = Pattern.compile("([\\d,]+\\.?\\d*)");
            Matcher numberMatcher = numberPattern.matcher(projectParameter);
            if (numberMatcher.find()) {
                String numberStr = numberMatcher.group(1).replace(",", "");
                return new BigDecimal(numberStr);
            }
        } catch (Exception e) {
            log.warn("提取建筑面积失败，projectParameter: {}, error: {}", projectParameter, e.getMessage());
        }
        return null;
    }

    /**
     * 从合同里程碑中提取项目里程
     * 
     * @param contractMilestone 合同里程碑
     * @return 项目里程
     */
    private BigDecimal extractMileageFromMilestone(String contractMilestone) {
        if (StringUtils.isBlank(contractMilestone)) {
            return null;
        }
        try {
            // 尝试匹配里程相关的数值，支持多种格式
            // 例如：里程：123.45公里、123.45km、总长度123.45千米等
            Pattern pattern = Pattern.compile("(?:里程[：:]?|长度[：:]?|总长[：:]?)([\\d,]+\\.?\\d*)(?:公里|千米|km|KM)?");
            Matcher matcher = pattern.matcher(contractMilestone);
            if (matcher.find()) {
                String mileageStr = matcher.group(1).replace(",", "");
                return new BigDecimal(mileageStr);
            }

            // 如果没有明确标识，尝试提取第一个数值作为里程
            Pattern numberPattern = Pattern.compile("([\\d,]+\\.?\\d*)");
            Matcher numberMatcher = numberPattern.matcher(contractMilestone);
            if (numberMatcher.find()) {
                String numberStr = numberMatcher.group(1).replace(",", "");
                return new BigDecimal(numberStr);
            }
        } catch (Exception e) {
            log.warn("提取项目里程失败，contractMilestone: {}, error: {}", contractMilestone, e.getMessage());
        }
        return null;
    }

    /**
     * 同步工程项目到MDM
     *
     * @param engineeringProject 工程项目
     */
    private void syncToMDM(EngineeringProject engineeringProject) {
        try {
            // 直接使用EngineeringProject转换为MdmPushEntity进行推送
            log.info("开始同步工程项目到MDM，projectId: {}", engineeringProject.getMainProjectId());

            // 使用MDMDataConverter将EngineeringProject转换为MdmPushEntity
            MdmPushEntity mdmPushEntity = MDMDataConverter.convertFromEngineeringProject(engineeringProject);
            if (mdmPushEntity == null) {
                log.error("转换MdmPushEntity失败，projectId: {}", engineeringProject.getMainProjectId());
                return;
            }

            // 构建MDM推送请求
            MdmApi<MdmPushEntity> pushDto = new MdmApi<>();
            pushDto.setMsgId(generateMsgId());
            pushDto.setDataType("Engproject");
            pushDto.setData(Collections.singletonList(mdmPushEntity));

            // 直接推送到MDM，不进行预校验
            MDMGateWayResponse response = mdmOpenApiFeign.pushEngineProject(pushDto);
            if (response != null && response.mdmApiIsSuccess()) {
                log.info("MDM同步成功，projectId: {}, mdmCode: {}", engineeringProject.getMainProjectId(),
                    response.getData() != null ? response.getData().getMdmCode() : "N/A");
            } else {
                log.error("MDM推送失败，projectId: {}, 响应: {}", engineeringProject.getMainProjectId(), response);
            }
        } catch (Exception e) {
            log.error("MDM同步异常，projectId: {}", engineeringProject.getMainProjectId(), e);
        }
    }

    /**
     * 处理工程项目更新事件
     *
     * @param smartProjectInfo 智慧工地项目信息
     * @param receiveRecord 接收记录
     */
    private void handleEngineUpdate(ProjectDetailOutResp smartProjectInfo, ProjectEventReceiveRecord receiveRecord) {
        try {
            // 1. 查找工程项目
            EngineeringProject engineeringProject = findEngineeringProject(smartProjectInfo, receiveRecord);
            if (engineeringProject == null) {
                return; // 错误信息已在findEngineeringProject中设置
            }

            // 2. 更新工程项目数据
            boolean isUpdated = updateEngineeringProjectData(engineeringProject, smartProjectInfo);

            // 3. 保存更新并处理后续操作
            if (isUpdated) {
                saveAndProcessUpdatedProject(engineeringProject, receiveRecord);
            } else {
                log.info("工程项目无需更新，engineeringKey: {}", engineeringProject.getEngineeringKey());
                receiveRecord.setApiResult(Constants.NUMBER_ONE);
            }
        } catch (Exception e) {
            log.error("工程项目更新异常", e);
            String errMsg = StringUtils.substring(e.getMessage(), 0, 1000);
            receiveRecord.setApiResult(Constants.NUMBER_ZERO).setErrMsg(errMsg);
        } finally {
            receiveRecordService.updateById(receiveRecord);
        }
    }

    /**
     * 查找工程项目
     *
     * @param smartProjectInfo 智慧工地项目信息
     * @param receiveRecord 接收记录
     * @return 工程项目实体，如果未找到返回null
     */
    private EngineeringProject findEngineeringProject(ProjectDetailOutResp smartProjectInfo, ProjectEventReceiveRecord receiveRecord) {
        ProjectInfo projectInfo = smartProjectInfo.getProjectInfo();
        String engineeringKey = projectInfo.getEngineeringKey();

        EngineeringProject engineeringProject = engineeringProjectMapper.selectOne(Wrappers
            .<EngineeringProject>lambdaQuery().eq(EngineeringProject::getEngineeringKey, engineeringKey));

        if (Objects.isNull(engineeringProject)) {
            log.error("未找到工程项目，engineeringKey: {}", engineeringKey);
            receiveRecord.setApiResult(Constants.NUMBER_ZERO).setErrMsg("未找到对应的工程项目");
            return null;
        }

        return engineeringProject;
    }

    /**
     * 更新工程项目数据
     *
     * @param engineeringProject 工程项目实体
     * @param smartProjectInfo 智慧工地项目信息
     * @return 是否有数据更新
     */
    private boolean updateEngineeringProjectData(EngineeringProject engineeringProject, ProjectDetailOutResp smartProjectInfo) {
        boolean isUpdated = false;

        // 获取各部分信息
        ProjectBasicInfo projectBasicInfo = smartProjectInfo.getProjectBasicInfo();
        ProjectAgreementInfo projectAgreementInfo = smartProjectInfo.getProjectAgreementInfo();
        ProjectContractInfo projectContractInfo = smartProjectInfo.getProjectContractInfo();

        // 更新核心字段
        isUpdated |= updateCoreFields(engineeringProject, projectBasicInfo, projectAgreementInfo, projectContractInfo);

        // 更新基础字段
        isUpdated |= updateBasicFields(engineeringProject, projectBasicInfo);

        // 更新项目经理信息
        isUpdated |= updateProjectManagerInfo(engineeringProject, projectContractInfo);

        // 更新时间字段
        isUpdated |= updateTimeFields(engineeringProject, projectContractInfo);

        return isUpdated;
    }

    /**
     * 保存更新后的项目并处理后续操作
     *
     * @param engineeringProject 工程项目实体
     * @param receiveRecord 接收记录
     */
    private void saveAndProcessUpdatedProject(EngineeringProject engineeringProject, ProjectEventReceiveRecord receiveRecord) {
        int updateResult = engineeringProjectMapper.updateById(engineeringProject);
        if (updateResult > 0) {
            log.info("工程项目更新成功，engineeringKey: {}", engineeringProject.getEngineeringKey());

            // 同步到MDM
            syncToMDM(engineeringProject);

            // 触发工程项目更新事件
            publishEngineUpdateEvent(engineeringProject);

            receiveRecord.setApiResult(Constants.NUMBER_ONE);
        } else {
            log.error("工程项目更新失败，engineeringKey: {}", engineeringProject.getEngineeringKey());
            receiveRecord.setApiResult(Constants.NUMBER_ZERO).setErrMsg("工程项目更新失败");
        }
    }

    /**
     * 生成MDM消息ID
     *
     * @return 消息ID
     */
    private String generateMsgId() {
        return "CPM_" + System.currentTimeMillis() + "_"
            + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }

}
