package com.cscec3b.iti.projectmanagement.server.config;

import static com.cscec3b.iti.projectmanagement.server.constant.MqConstants.*;

import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import lombok.extern.slf4j.Slf4j;

import java.util.Map;


/**
 * MQ配置类
 * <AUTHOR>
 * @date 2024/09/14
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class RabbitMqConfig {

    private final RabbitAdmin rabbitAdmin;


    /**
     * 业务延时交换机
     *
     * @return org.springframework.amqp.core.DirectExchange
     * <AUTHOR>
     * @date 2023/04/13 11:29
     */
    @Bean
    public DirectExchange projectEventExchange() {
        return ExchangeBuilder.directExchange(PROJECT_EVENT_EXCHANGE_NAME).delayed().build();
    }

    /**
     * 业务队列,自动创建队列
     *
     * @return org.springframework.amqp.core.Queue
     * <AUTHOR>
     * @date 2023/04/13 11:31
     */
    @Bean
    public Queue projectEventQueue() {
        final Queue queue = QueueBuilder.durable(PROJECT_EVENT_BUSINESS_QUEUE).build();
        rabbitAdmin.declareQueue(queue);
        return queue;
    }

    /**
     * 队列与交换机通过路由key绑定
     *
     * @return
     */
    @Bean
    public Binding projectEventBinding2Queue() {
        return BindingBuilder.bind(projectEventQueue()).to(projectEventExchange())
                .with(PROJECT_EVENT_BUSINESS_ROUTE_KEY);
    }


    @Bean
    public RabbitTemplate rabbitTemplate(ConnectionFactory connectionFactory) {
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        // 使用jackson 消息转换器
        rabbitTemplate.setMessageConverter(jsonMessageConverter());
        rabbitTemplate.setEncoding("UTF-8");
        // 在消息没有被路由到合适的队列情况下，Broker会将消息返回给生产者，
        // 为true时如果Exchange根据类型和消息Routing Key无法路由到一个合适的Queue存储消息，
        // Broker会调用Basic.Return回调给handleReturn()，再回调给ReturnCallback，将消息返回给生产者。
        // 为false时，丢弃该消息
        rabbitTemplate.setMandatory(true);

        // 消息确认，需要配置 spring.rabbitmq.publisher-confirms = true
        // 确认消息是否到达交换机
        rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> {
            if (ack) {
                log.info("消息发送成功");
            } else {
                log.error("消息发送失败：{}", cause);
                log.error("数据:{}", correlationData);
                // todo 重试逻辑
            }
        });
        // 通过交换机路由到队列失败时会回调 spring.rabbitmq.publisher-returns = true
        rabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> {
                    final Integer delay = message.getMessageProperties().getReceivedDelay();

                    if (ObjectUtils.isNotEmpty(delay) && delay > 0) {
                        log.info("延时消息。。。。");
                        return;
                    }
                    log.info("消息从Exchange路由到Queue失败: exchange: {}, route: {}, replyCode: {}, replyText: {}, message: " +
                                    "{}",
                            exchange, routingKey, replyCode, replyText, message);
                }

                // 重试逻辑
        );
        return rabbitTemplate;
    }

    // 定义消息转换器
    @Bean
    public Jackson2JsonMessageConverter jsonMessageConverter() {
        return new Jackson2JsonMessageConverter();
    }



}
