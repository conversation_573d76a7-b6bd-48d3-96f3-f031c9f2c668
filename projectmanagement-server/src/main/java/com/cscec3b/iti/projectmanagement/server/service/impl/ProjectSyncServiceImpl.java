package com.cscec3b.iti.projectmanagement.server.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.cscec3b.iti.common.base.json.JsonUtils;
import com.cscec3b.iti.common.web.config.SpringUtils;
import com.cscec3b.iti.common.web.exception.FrameworkException;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.BusinessSystemDataSmartsite;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.SmartsiteEngineerParameter;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ComparableResult;
import com.cscec3b.iti.projectmanagement.server.enums.BusSysDataTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.ComparableHandleTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.ProjectStatusEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.ProjectMapper;
import com.cscec3b.iti.projectmanagement.server.service.BusinessSystemDataChangeApprovalConfigService;
import com.cscec3b.iti.projectmanagement.server.service.IProjectSyncService;
import com.cscec3b.iti.projectmanagement.server.service.ISmartsiteEngineerParameterService;
import com.cscec3b.iti.projectmanagement.server.util.ComparableUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class ProjectSyncServiceImpl implements IProjectSyncService {

    /**
     * ProjectMapper
     */
    private final ProjectMapper projectMapper;

    /**
     * 审批配置服务
     */
    private final BusinessSystemDataChangeApprovalConfigService dataChangeApprovalConfigService;

    /**
     * 工地工程参数服务
     */
    private final ISmartsiteEngineerParameterService engineerParameterService;


    // 1. 更新信息到业务板块表中，
    // 2. 从配置信息里取出配置了审批的字段
    // 3. 更新信息与配置字段进行比较，拿到配置字段信息
    // 4. 配置变更值，取出最后要审批的字段，
    // 5. 更新非审批字段到项目表中
    // 6. 审批字段发起流程
    @Override
    public boolean syncProject(String cpmProjectKey, String yunshuOrgId, String segment, Map<String, Object> data) {
        final BusSysDataTypeEnum dateTypeEnum = BusSysDataTypeEnum.segment(segment);
        final String entityName = dateTypeEnum.getEntityName();
        final Class iService = dateTypeEnum.getIService();
        Project project = getProject(cpmProjectKey, yunshuOrgId, dateTypeEnum);
        Class<?> clazz;
        try {
            clazz = Class.forName("com.cscec3b.iti.projectmanagement.server.entity." + entityName);
        } catch (ClassNotFoundException e) {
            throw new FrameworkException(-1, "未配置的业务系统数据类型：" + entityName);
        }
        // 处理ProjectId 字段，该字段会导致映射及更新Project表异常
        data.remove("projectId");
        final Object bean = BeanUtil.toBean(data, clazz);
        final Object beforeChangeData = BeanUtil.copyProperties(project, clazz);
        // 智慧工地定制方法, 数据转换
        convertData(dateTypeEnum, bean);
        // 走前置事件（业务板块字段变更审批表）
        // 变更记录所有有变更的信息 匹配审批流程
        final List<ComparableResult> compareResult = ComparableUtils.compare(beforeChangeData, bean);
        // 忽略未发生变化的字段， 获取发生变化的字段
        final List<ComparableResult> changedFieldList =
                compareResult.stream().filter(result ->
                                !result.getHandlerType().equals(ComparableHandleTypeEnum.NO_CHANGE.getDictCode()))
                        .collect(Collectors.toList());
        // 如果对比发现字段字段未发生变化， 则无需审批,
        if (CollectionUtils.isEmpty(changedFieldList)) {
            return true;
        }
        // 走业务变更审批流配置前置判断方法
        final List<ComparableResult> noBpmResultList = dataChangeApprovalConfigService.matchApprovalProcess(project,
                dateTypeEnum, changedFieldList);
        if (CollectionUtils.isNotEmpty(noBpmResultList)) {
            final Map<String, Object> tempMap =
                    noBpmResultList.stream().collect(Collectors.toMap(ComparableResult::getFieldId,
                            o -> Objects.isNull(o.getNewFieldContent()) ? "" : o.getNewFieldContent(),
                            (k1, k2) -> k2));
            if (ObjectUtils.isNotEmpty(tempMap)) {
                final Project updateProjectDto = BeanUtil.toBean(tempMap, Project.class);
                updateProjectDto.setId(project.getId());
                projectMapper.updateProjectById(updateProjectDto);
            }
        }

        if (!data.containsKey("projectId")) {
            data.put("projectId", project.getId());
        }
        try {
            IService service = (IService<?>) SpringUtils.getBean(iService);
            log.info("更新数据：{}-{}", service, data);
            service.saveOrUpdate(BeanUtil.toBean(data, clazz));
        } catch (Exception e) {
            log.error("更新数据异常：{}", e.getMessage());
            throw new FrameworkException(-1, "更新数据异常：" + e.getMessage());
        }
        return true;
    }

    /**
     * 根据传入的 cpmProjectKey 和 yunshuOrgId 获取相应的 Project 对象
     * 如果没有找到对应的 Project 对象，抛出异常
     * 如果找到了 Project 对象，则返回该对象
     * 如果存在多个 Project 对象，也抛出异常
     *
     * @param cpmProjectKey 项目的唯一标识符
     * @param yunshuOrgId   云枢组织的唯一标识符
     * @param dateTypeEnum  业务系统数据类型枚举
     * @return 返回找到的 Project 对象或抛出相应的异常
     * @throws FrameworkException 如果项目不存在或存在多个项目
     */
    private Project getProject(String cpmProjectKey, String yunshuOrgId, final BusSysDataTypeEnum dateTypeEnum) {
        Project project = projectMapper.selectOne(Wrappers.<Project>lambdaQuery().eq(Project::getCpmProjectKey,
                cpmProjectKey));
        if (Objects.isNull(project)) {
            // 商务板块要求可以根据yunshuOrgId获取项目信息
            if (dateTypeEnum.equals(BusSysDataTypeEnum.BMS_SEGMENT)) {
                List<Project> projects =
                        projectMapper.selectList(Wrappers.<Project>lambdaQuery().eq(Project::getYunshuOrgId,
                                yunshuOrgId));
                if (CollectionUtils.isEmpty(projects) || projects.size() > 1) {
                    throw new FrameworkException(-1, "项目不存在或存在多个");
                } else {
                    project = projects.get(0);
                }
            } else {
                throw new FrameworkException(-1, "项目不存在");
            }
        }
        return project;
    }

    /**
     * 数据转换
     *
     * @param dateTypeEnum 业务系统数据类型枚举
     * @param bean         数据信息
     */
    private void convertData(BusSysDataTypeEnum dateTypeEnum, Object bean) {
        if (dateTypeEnum.equals(BusSysDataTypeEnum.SMART_SITE_SEGMENT)) {
            BusinessSystemDataSmartsite businessData = (BusinessSystemDataSmartsite) bean;
            // 转换工程状态
            fillprojectStatusEng(businessData);
            // 转换工程参数
            final String engineerParameter = businessData.getEngineerParameter();
            if (JSONUtil.isJsonArray(engineerParameter)) {
                fillEngineeringParameters(businessData, JSONUtil.parseArray(engineerParameter));
            }
            // 现在无需在此处获取云枢上级组织信息, 因为智慧工地现在不创建云枢组织
            // nothing to do ...
        }
    }

    /**
     * 项目状态转换
     *
     * @param businessData 工地项目信息
     */
    private void fillprojectStatusEng(BusinessSystemDataSmartsite businessData) {
        // 转换项目工程状态
        String projectStatusEng = null;
        final String infoProjectStatus = businessData.getProjectStatusEng();

        // 拆分施工项目状态字段第一级用于映射编码
        String infoFirstProjectStatus = infoProjectStatus.split(Constants.COMMA_ENG)[0];

        if (StringUtils.isNotBlank(infoFirstProjectStatus)) {
            if (Constants.PREPARE.equals(infoFirstProjectStatus) || Constants.PROJECT_APPROVAL.equals(infoFirstProjectStatus)
                    || Constants.CONSTRUCTION_PREPARATION.equals(infoFirstProjectStatus)) {
                projectStatusEng = ProjectStatusEnum.OPERATING_RESERVE.getDictCode();
            } else {
                final ProjectStatusEnum enumCode = ProjectStatusEnum.getEnumCode(infoFirstProjectStatus);
                projectStatusEng = ObjectUtils.isNotEmpty(enumCode) ? enumCode.getDictCode() : null;
            }
        }
        businessData.setProjectStatusEng(projectStatusEng);
    }

    /**
     * 工程参数转换
     *
     * @param businessData 工地项目信息
     * @param jsonArray    工程参数信息
     */
    private void fillEngineeringParameters(BusinessSystemDataSmartsite businessData, JSONArray jsonArray) {
        List<SmartsiteEngineerParameter> paramList = new ArrayList<>();
        final String projectType = businessData.getProjectType();
        final String[] split = projectType.split("#");
        if (jsonArray.size() > Constants.NUMBER_ZERO && split.length >= Constants.NUMBER_TWO) {
            final String ids = split[1];
            final String[] idSplit = ids.split(",");
            final String id = idSplit[idSplit.length - 1];
            for (Object arr : jsonArray) {
                JSONObject parseObj = JSONUtil.parseObj(arr);
                Set<Map.Entry<String, Object>> entries = parseObj.entrySet();
                for (Map.Entry<String, Object> entry : entries) {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    processEntryValue(key, value, id, paramList);
                }
            }
        }
        businessData.setEngineerParameter(JsonUtils.toJsonStr(paramList));
    }

    private void processEntryValue(String key, Object value, String id, List<SmartsiteEngineerParameter> paramList) {
        if (key.startsWith("__CONTRACT_CONSTRUCTION_PARAMS__")) {
            key = key.replace("__CONTRACT_CONSTRUCTION_PARAMS__", "");
        }
        SmartsiteEngineerParameter cacheParameter = engineerParameterService.getByIdAndDataKey(id, key);
        if (ObjectUtils.isNotEmpty(cacheParameter)) {
            cacheParameter.setValue(String.valueOf(value));
            paramList.add(cacheParameter);
        }
    }

}
