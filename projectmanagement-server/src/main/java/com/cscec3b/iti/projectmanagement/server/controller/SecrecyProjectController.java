package com.cscec3b.iti.projectmanagement.server.controller;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.api.ResponseBuilder;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.logger.annotations.Logger;
import com.cscec3b.iti.projectmanagement.api.ISecrecyProjectApi;
import com.cscec3b.iti.projectmanagement.api.dto.request.secrecy.SecrecyProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.secrecy.QuerySecrecyProjectParams;
import com.cscec3b.iti.projectmanagement.api.dto.request.secrecy.UpdateSecrecyProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.secrecy.SecrecyProjectDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.secrecy.SecrecyProjectResp;
import com.cscec3b.iti.projectmanagement.server.service.ISecrecyProjectInfoService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping(ISecrecyProjectApi.PATH)
@Api(tags = "保密项目控制器")
@RequiredArgsConstructor
public class SecrecyProjectController implements ISecrecyProjectApi {

    private final ISecrecyProjectInfoService secrecyProjectInfoService;

    /**
     * 编辑保密项目API
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @Logger
    public GenericityResponse<Boolean> updateSpecialProject(UpdateSecrecyProjectReq request) {
        return new GenericityResponse<>(secrecyProjectInfoService.updateSecrecyProject(request));
    }
    /**
     * 保密项目详情API
     *
     * @param id id
     * @return {@link GenericityResponse }<{@link SecrecyProjectDetailResp }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    public GenericityResponse<SecrecyProjectDetailResp> getSpecialProjectDetail(Long id) {
        return new GenericityResponse<>(secrecyProjectInfoService.getDetail(id));
    }

    /**
     * 保密项目列表分页查询
     *
     * @param params 参数个数
     * @return {@link GenericityResponse}<{@link Page}<{@link SecrecyProjectResp}>>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    public GenericityResponse<Page<SecrecyProjectResp>> specialProjectPage(QuerySecrecyProjectParams params) {
        return new GenericityResponse<>(secrecyProjectInfoService.pageList(params));
    }

    /**
     * 新建保密项目
     *
     * @param request 请求
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Override
    @Logger
    public GenericityResponse<Long> create(SecrecyProjectReq request) {
        return new GenericityResponse<>(secrecyProjectInfoService.create(request));
    }

    @Override
    public GenericityResponse<Boolean> delete(List<Long> ids) {
        return ResponseBuilder.fromData(secrecyProjectInfoService.deleteSecrecyProject(ids));
    }
}
