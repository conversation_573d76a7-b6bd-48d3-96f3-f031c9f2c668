package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.projectmanagement.api.dto.dto.OSSProcessEvent;
import com.cscec3b.iti.projectmanagement.api.dto.response.OSSProcessResp;

/**
 * oss进度服务
 *
 * <AUTHOR>
 * @date 2023/1/10 16:29
 */
public interface IOSSProcessService {

    /**
     * 缓存上传/下载进度
     *
     * @param ossProcessEvent 进度信息
     */
    void cacheProcess(OSSProcessEvent ossProcessEvent);

    /**
     * 获取文件上传/下载进度
     *
     * @param key key
     * @return com.cscec3b.iti.projectmanagement.api.dto.response.OSSProcessResp
     * <AUTHOR>
     * @date 2023/01/10 16:45
     */
    OSSProcessResp getProcess(String key);


    /**
     * 进度通知监听器
     *
     * @param event event
     * <AUTHOR>
     * @date 2023/01/11 09:10
     */
    void parseProcessListener(OSSProcessEvent event);
}
