package com.cscec3b.iti.projectmanagement.server.service.impl;

import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import com.cscec3b.iti.portal.dto.ContentMsgDto;
import com.cscec3b.iti.portal.dto.response.PortalBaseResp;
import com.cscec3b.iti.portal.service.IPortalService;
import com.cscec3b.iti.projectmanagement.api.dto.request.task.TaskReq;
import com.cscec3b.iti.projectmanagement.server.constant.PortalConstants;
import com.cscec3b.iti.projectmanagement.server.service.IPmPortalMsgService;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @Description PmPortalMsgService
 * <AUTHOR>
 * @Date 2022/12/2 10:56
 */
@Slf4j
@Service
public class PmPortalMsgServiceImpl implements IPmPortalMsgService {
    @Resource
    private IPortalService portalMsgService;

    /**
     * @param task 待办任务
     * @description 消息推送到经办人的三局通账号
     * <AUTHOR>
     */
    @Override
    @Async
    //重试策略：重试3次、第一次延迟2s、延迟时间间隔3倍
    @Retryable(include = {RuntimeException.class}, maxAttempts = 3, backoff = @Backoff(delay = 2000L, multiplier = 3))
    public void pushMessageToPortal(TaskReq task) {
        log.info("待办任务消息推送到三局通账户pushMessageToPortal==========>>>>task:{}", task);

        //拼接消息体内容
        String content = MessageFormat.format(PortalConstants.TODO_CONTENT, task.getTaskName(),
                DateUtil.format(new Date(System.currentTimeMillis()), DatePattern.NORM_DATETIME_PATTERN),
                "");
        ContentMsgDto contentMsgDto = new ContentMsgDto()
                .setUserId(PortalConstants.PM_APPROVAL_PERSON)
                .setTitle(PortalConstants.TODO_TITLE)
                .setContent(content)
                .setSenderName(PortalConstants.SENDER_NAME);

        List<ContentMsgDto> contentMsgDtoList = new ArrayList<>();
        contentMsgDtoList.add(contentMsgDto);
        //调用三局通信息保存接口
        PortalBaseResp portalBaseResp = portalMsgService.pushPortalSaveMessage(contentMsgDtoList);
        if (!(PortalConstants.PORTAL_RESP_SUCCESS_CODE.equals(portalBaseResp.getCode())
                && PortalConstants.PORTAL_RESP_SUCCESS_MSG.equals(portalBaseResp.getMsg()))) {
            throw new RuntimeException("推送三局通接口请求错误=========>>>>返回对象："+ portalBaseResp);
        }
    }
}
