package com.cscec3b.iti.projectmanagement.server.entity.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.cscec3b.iti.projectmanagement.server.converter.excelConverter.BureauNominalProjectTypeConverter;
import com.cscec3b.iti.projectmanagement.server.converter.excelConverter.IndContractsTypeConverter;
import com.cscec3b.iti.projectmanagement.server.converter.excelConverter.LongToDateTimeConverter;
import com.cscec3b.iti.projectmanagement.server.converter.excelConverter.ProjectBelongConverter;
import com.cscec3b.iti.projectmanagement.server.converter.excelConverter.ProjectStatusBizConverter;
import com.cscec3b.iti.projectmanagement.server.converter.excelConverter.ProjectStatusConverter;
import com.cscec3b.iti.projectmanagement.server.converter.excelConverter.TrueFalseConverter;
import com.cscec3b.iti.projectmanagement.server.converter.excelConverter.YesNoConverter;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectExportResp", description = "项目导出响应对象")
public class ProjectExportDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ExcelProperty("项目ID")
    private Long id;

    /**
     * 独立合同ID
     */
    @ExcelProperty(value = "独立合同ID")
    private Long independentContractId;

    /**
     * 独立合同类型：1投标总结；2补充协议；3局内分包合同
     */
    @ApiModelProperty(value = "独立合同类型：1投标总结；2补充协议；3局内分包合同", position = 6)
    @ExcelProperty(value = "独立合同类型", converter = IndContractsTypeConverter.class)
    private String independentContractType;


    /**
     * 是否生态敏感区项目
     */
    @ApiModelProperty(value = "是否生态敏感区项目", position = 10)
    @ExcelProperty(value = "是否生态敏感区项目", converter = TrueFalseConverter.class)
    private String isEcologySensitive;

    /**
     * 是否边小远散项目
     */
    @ApiModelProperty(value = "是否边小远散项目", position = 11)
    @ExcelProperty(value = "是否边小远散项目", converter = TrueFalseConverter.class)
    private String isEdgeSmall;

    /**
     * 项目级别
     */
    @ApiModelProperty(value = "项目级别", position = 12)
    @ExcelProperty("项目级别")
    private String projectLevel;

    /**
     * 实际进场日期
     */
    @ApiModelProperty(value = "实际进场日期", position = 13)
    @ExcelProperty(value = "实际进场日期", converter = LongToDateTimeConverter.class)
    private String realEnterTime;

    /**
     * 实际竣工日期
     */
    @ApiModelProperty(value = "实际竣工日期", position = 14)
    @ExcelProperty(value = "实际竣工日期", converter = LongToDateTimeConverter.class)
    private String workEndTime;

    /**
     * 五方主体验收日期（实际通车时间）
     */
    @ApiModelProperty(value = "五方主体验收日期（实际通车时间）", position = 15)
    @ExcelProperty(value = "五方主体验收日期（实际通车时间）", converter = LongToDateTimeConverter.class)
    private String realOpenTrafficTime;

    /**
     * 建设单位（甲方）联系人
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人", position = 16)
    @ExcelProperty("建设单位（甲方）联系人")
    private String contactPerson;

    /**
     * 建设单位（甲方）联系人电话 项目中心
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人电话 项目中心", position = 17)
    @ExcelProperty("建设单位（甲方）联系人电话")
    private String contactPersonMobile;

    /**
     * 现场业主代表姓名
     */
    @ApiModelProperty(value = "现场业主代表姓名", position = 18)
    @ExcelProperty("现场业主代表姓名")
    private String sceneOwnerRepresentName;

    /**
     * 现场业主代表职务
     */
    @ApiModelProperty(value = "现场业主代表职务", position = 19)
    @ExcelProperty("现场业主代表职务")
    private String sceneOwnerRepresentDuty;

    /**
     * 现场业主代表联系电话
     */
    @ApiModelProperty(value = "现场业主代表联系电话", position = 20)
    @ExcelProperty("现场业主代表联系电话")
    private String sceneOwnerRepresentPhone;

    /**
     * 工程参数json
     */
    @ApiModelProperty(value = "工程参数json", position = 21)
    @ExcelProperty("工程参数json")
    private String engineerParameter;

    /**
     * 财商立项编号
     */
    @ApiModelProperty(value = "财商立项编号", position = 22)
    @ExcelProperty("财商立项编号")
    private String projectFinanceCode;

    /**
     * 财商立项名称
     */
    @ApiModelProperty(value = "财商立项名称", position = 23)
    @ExcelProperty("财商立项名称")
    private String projectFinanceName;

    /**
     * 财商立项项目简称（中文）
     */
    @ApiModelProperty(value = "财商立项项目简称（中文）", position = 24)
    @ExcelProperty("财商项目简称")
    private String projectFinanceAbbreviation;

    /**
     * 工程类型（国家标准）
     */
    @ApiModelProperty(value = "工程类型（国家标准）", position = 25)
    @ExcelProperty("工程类型（国家标准）")
    private String countryProjectType;

    /**
     * 工程类型（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）", position = 26)
    @ExcelProperty("工程类型（总公司市场口径）")
    private String marketProjectType;

    /**
     * 工程类型（总公司综合口径）
     */
    @ApiModelProperty(value = "工程类型（总公司综合口径）", position = 27)
    @ExcelProperty("工程类型（总公司综合口径）")
    private String projectType;

    /**
     * 承包模式
     */
    @ApiModelProperty(value = "承包模式", position = 28)
    @ExcelProperty("承包模式")
    private String contractMode;

    /**
     * 行政区域（地理位置）
     */
    @ApiModelProperty(value = "行政区域（地理位置）", position = 29)
    @ExcelProperty("行政区域（地理位置）")
    private String region;

    /**
     * 项目地址
     */
    @ApiModelProperty(value = "项目地址", position = 30)
    @ExcelProperty("项目地址")
    private String projectAddress;

    /**
     * 是否投资项目
     */
    @ApiModelProperty(value = "是否投资项目", position = 31)
    @ExcelProperty(value = "是否投资项目", converter = YesNoConverter.class)
    private String investmentProjects;

    /**
     * 投资主体
     */
    @ApiModelProperty(value = "投资主体", position = 32)
    @ExcelProperty("投资主体")
    private String investors;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", position = 33)
    @ExcelProperty("业务类型")
    private String businessType;

    /**
     * 合同总金额（元）
     */
    @ApiModelProperty(value = "合同总金额（元）", position = 34)
    @ExcelProperty("合同总金额（元）")
    private BigDecimal contractAmount;

    /**
     * 设计单位
     */
    @ApiModelProperty(value = "设计单位", position = 35)
    @ExcelProperty("设计单位")
    private String designer;

    /**
     * 监理单位
     */
    @ApiModelProperty(value = "监理单位", position = 36)
    @ExcelProperty("监理单位")
    private String supervisor;

    /**
     * 项目经理
     */
    @ApiModelProperty(value = "项目经理", position = 37)
    @ExcelProperty("项目经理")
    private String projectManager;

    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别", position = 38)
    @ExcelProperty("客户级别")
    private String customerLevel;

    /**
     * 合同开工日期
     */
    @ApiModelProperty(value = "合同开工日期", position = 39)
    @ExcelProperty(value = "合同开工日期", converter = LongToDateTimeConverter.class)
    private String workerBeginTime;

    /**
     * 合同竣工日期
     */
    @ApiModelProperty(value = "合同竣工日期", position = 40)
    @ExcelProperty(value = "合同竣工日期", converter = LongToDateTimeConverter.class)
    private String workerEndTime;

    /**
     * 总工期
     */
    @ApiModelProperty(value = "总工期", position = 41)
    @ExcelProperty("总工期")
    private Integer countDays;

    /**
     * 实际开工日期
     */
    @ApiModelProperty(value = "实际开工日期", position = 42)
    @ExcelProperty(value = "实际开工日期", converter = LongToDateTimeConverter.class)
    private String realWorkBeginTime;

    /**
     * 预计实际竣工日期
     */
    @ApiModelProperty(value = "预计实际竣工日期", position = 43)
    @ExcelProperty(value = "预计实际竣工日期", converter = LongToDateTimeConverter.class)
    private String predictWorkEndTime;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", position = 44)
    @ExcelProperty("客户名称")
    private String customerName;

    /**
     * 客户母公司
     */
    @ApiModelProperty(value = "客户母公司", position = 45)
    @ExcelProperty("客户母公司")
    private String superiorCompanyName;

    /**
     * 客户企业性质
     */
    @ApiModelProperty(value = "客户企业性质", position = 46)
    @ExcelProperty("客户企业性质")
    private String enterpriseType;

    /**
     * 实际中标日期
     */
    @ApiModelProperty(value = "实际中标日期", position = 47)
    @ExcelProperty(value = "实际中标日期", converter = LongToDateTimeConverter.class)
    private String successfulTime;

    /**
     * 实际签约日期
     */
    @ApiModelProperty(value = "实际签约日期", position = 48)
    @ExcelProperty(value = "实际签约日期", converter = LongToDateTimeConverter.class)
    private String actualSignedTime;

    /**
     * 签约主体
     */
    @ApiModelProperty(value = "签约主体", position = 49)
    @ExcelProperty("签约主体")
    private String signedSubjectValue;


    /**
     * 实施单位
     */
    @ApiModelProperty(value = "实施单位", position = 50)
    @ExcelProperty("实施单位")
    private String doUnit;

    /**
     * 含税合同总价（RMB）
     */
    @ApiModelProperty(value = "含税合同总价（RMB）", position = 51)
    @ExcelProperty("含税合同总价（RMB）")
    private BigDecimal totalAmount;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额", position = 52)
    @ExcelProperty("不含税金额")
    private BigDecimal noTaxIncludedMoney;

    /**
     * 自行施工不含税金额
     */
    @ApiModelProperty(value = "自行施工不含税金额", position = 53)
    @ExcelProperty("自行施工不含税金额")
    private BigDecimal midAmountSelf;

    /**
     * 土建不含税金额
     */
    @ApiModelProperty(value = "土建不含税金额", position = 54)
    @ExcelProperty("土建不含税金额")
    private BigDecimal selfCivilAmount;

    /**
     * 安装不含税金额
     */
    @ApiModelProperty(value = "安装不含税金额", position = 55)
    @ExcelProperty("安装不含税金额")
    private BigDecimal selfInstallAmount;

    /**
     * 钢结构不含税金额
     */
    @ApiModelProperty(value = "钢结构不含税金额", position = 56)
    @ExcelProperty("钢结构不含税金额")
    private BigDecimal selfSteelStructureAmount;

    /**
     * 总包服务费
     */
    @ApiModelProperty(value = "总包服务费", position = 57)
    @ExcelProperty("总包服务费")
    private BigDecimal selfTotalServiceAmount;

    /**
     * 其他
     */
    @ApiModelProperty(value = "其他", position = 58)
    @ExcelProperty("其他")
    private BigDecimal selfOtherAmount;

    /**
     * 销项税额
     */
    @ApiModelProperty(value = "销项税额", position = 59)
    @ExcelProperty("销项税额")
    private BigDecimal projectTaxAmount;

    /**
     * 暂列金或甲指分包金额
     */
    @ApiModelProperty(value = "暂列金或甲指分包金额", position = 60)
    @ExcelProperty("暂列金或甲指分包金额")
    private BigDecimal subcontractAmount;

    /**
     * 工期奖罚类型
     */
    @ApiModelProperty(value = "工期奖罚类型", position = 61)
    @ExcelProperty("工期奖罚类型")
    private String workerDateRewardPunish;

    /**
     * 工期奖罚条款
     */
    @ApiModelProperty(value = "工期奖罚条款", position = 62)
    @ExcelProperty("工期奖罚条款")
    private String workerRewardPunishAppoint;

    /**
     * 合同承包范围
     */
    @ApiModelProperty(value = "合同承包范围", position = 63)
    @ExcelProperty("合同承包范围")
    private String contractScope;

    /**
     * 发包人指定分包、独立分包的工程
     */
    @ApiModelProperty(value = "发包人指定分包、独立分包的工程", position = 64)
    @ExcelProperty("发包人指定分包、独立分包的工程")
    private String issuerProject;

    /**
     * 质量要求
     */
    @ApiModelProperty(value = "质量要求", position = 65)
    @ExcelProperty("质量要求")
    private String qualityGuarantee;

    /**
     * 质量奖罚类型
     */
    @ApiModelProperty(value = "质量奖罚类型", position = 66)
    @ExcelProperty("质量奖罚类型")
    private String rewardPunishType;

    /**
     * 质量奖罚条款
     */
    @ApiModelProperty(value = "质量奖罚条款", position = 67)
    @ExcelProperty("质量奖罚条款")
    private String rewardPunishTerms;

    /**
     * 安全文明施工要求
     */
    @ApiModelProperty(value = "安全文明施工要求", position = 68)
    @ExcelProperty("安全文明施工要求")
    private String safetyRequirement;

    /**
     * 安全文明施工奖罚条款
     */
    @ApiModelProperty(value = "安全文明施工奖罚条款", position = 69)
    @ExcelProperty("安全文明施工奖罚条款")
    private String safetyRewardPunishTerms;

    /**
     * 是否有预付款
     */
    @ApiModelProperty(value = "是否有预付款", position = 70)
    @ExcelProperty("是否有预付款")
    private String advancesFlag;

    /**
     * 进度款支付方式
     */
    @ApiModelProperty(value = "进度款支付方式", position = 71)
    @ExcelProperty("进度款支付方式")
    private String advancesWay;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式", position = 72)
    @ExcelProperty("支付方式")
    private String payTypeNew;

    /**
     * 竣工验收支付比例
     */
    @ApiModelProperty(value = "竣工验收支付比例", position = 73)
    @ExcelProperty("竣工验收支付比例")
    private String completedRate;

    /**
     * 竣工验收收款周期（月）
     */
    @ApiModelProperty(value = "竣工验收收款周期（月）", position = 74)
    @ExcelProperty("竣工验收收款周期（月）")
    private String completedCycle;

    /**
     * 结算支付比例
     */
    @ApiModelProperty(value = "结算支付比例", position = 75)
    @ExcelProperty("结算支付比例")
    private String settlementRate;

    /**
     * 结算周期（月）
     */
    @ApiModelProperty(value = "结算周期（月）", position = 76)
    @ExcelProperty("结算周期（月）")
    private String settlementCycle;

    /**
     * 保修金
     */
    @ApiModelProperty(value = "保修金", position = 77)
    @ExcelProperty("保修金")
    private String warrantyPremium;

    /**
     * 保修金比例
     */
    @ApiModelProperty(value = "保修金比例", position = 78)
    @ExcelProperty("保修金比例")
    private String warrantyPremiumRate;

    /**
     * 保修金支付方式
     */
    @ApiModelProperty(value = "保修金支付方式", position = 79)
    @ExcelProperty("保修金支付方式")
    private String warrantyPremiumWay;

    /**
     * 是否垫资
     */
    @ApiModelProperty(value = "是否垫资", position = 80)
    @ExcelProperty(value = "是否垫资", converter = YesNoConverter.class)
    private String advancesFundFlag;

    /**
     * 履约担保方式
     */
    @ApiModelProperty(value = "履约担保方式", position = 81)
    @ExcelProperty("履约担保方式")
    private String guaranteeWay;

    /**
     * 项目及土地是否合法
     */
    @ApiModelProperty(value = "项目及土地是否合法", position = 82)
    @ExcelProperty(value = "是否合法", converter = YesNoConverter.class)
    private String landLegalityFlag;

    /**
     * 是否放弃优先受偿权
     */
    @ApiModelProperty(value = "是否放弃优先受偿权", position = 83)
    @ExcelProperty(value = "是否放弃优先受偿权", converter = YesNoConverter.class)
    private String giveUpCompensateFlag;

    /**
     * 付款比例是否低于80%
     */
    @ApiModelProperty(value = "付款比例是否低于80%", position = 84)
    @ExcelProperty(value = "付款比例是否低于80%", converter = YesNoConverter.class)
    private String payRateLessEightyFlag;

    /**
     * 支付节点是否超2个月
     */
    @ApiModelProperty(value = "支付节点是否超2个月", position = 85)
    @ExcelProperty(value = "支付节点是否超2个月", converter = YesNoConverter.class)
    private String nodeMoreTwoMonthFlag;

    /**
     * 工程编号
     */
    @ApiModelProperty(value = "工程编号")
    @ExcelProperty("工程编号")
    private String projectCode;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称")
    @ExcelProperty("工程名称")
    private String projectName;

    /**
     * 工程简称
     */
    @ApiModelProperty(value = "工程简称")
    @ExcelProperty("工程简称")
    private String projectAbbreviation;

    /**
     * 工程属地
     */
    @ApiModelProperty(value = "工程属地")
    @ExcelProperty(value = "工程属地", converter = ProjectBelongConverter.class)
    private String projectBelong;


    /**
     * 是否创建指挥部
     */
    @ApiModelProperty(value = "是否创建指挥部")
    @ExcelProperty(value = "是否创建指挥部", converter = YesNoConverter.class)
    private String isCreateHead;


    @ApiModelProperty(value = "云枢组织id")
    @ExcelProperty("云枢组织id")
    private String yunshuOrgId;


    @ApiModelProperty(value = "云枢TreeId")
    @ExcelProperty("云枢TreeId")
    private String yunshuTreeId;


    /**
     * 来源系统
     */
    @ApiModelProperty(value = "来源系统，1:市场营销；2:特殊立项")
    @ExcelProperty("来源系统")
    private String sourceSystem;

    /**
     * 立项描述
     */
    @ApiModelProperty(value = "立项描述")
    @ExcelProperty("立项描述")
    private String projectDesc;


    /**
     * 项目分类名称
     */
    @ApiModelProperty(value = "项目分类名称")
    @ExcelProperty("项目分类")
    private String projectClassName;


    /**
     * 施工项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;
     */
    @ApiModelProperty(value = "施工项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;")
    @ExcelProperty(value = "项目状态(工程)", converter = ProjectStatusConverter.class)
    private String projectStatusEng;

    /**
     * 施工项目状态(财务): 01:在施; 0301:已竣未结; 0302:已竣已结; 0199:停工; 04:销项;
     */
    @ApiModelProperty(value = "施工项目状态(财务): 01:在施; 0301:已竣未结; 0302:已竣已结; 0199:停工; 04:销项;")
    @ExcelProperty("项目状态(财务)")
    private String projectStatusFin;

    /**
     * 施工项目状态(商务): 05:未结; 06:已结
     */
    @ApiModelProperty(value = "施工项目状态(商务): 05:未结; 06:已结")
    @ExcelProperty(value = "项目状态(商务)", converter = ProjectStatusBizConverter.class)
    private String projectStatusBiz;

    @ExcelProperty(value = "项目状态(商务)业务时间", converter = LongToDateTimeConverter.class)
    private String projectStatusBizTime;

    @ExcelProperty("执行单位id")
    private String yunshuExecuteUnitId;

    @ExcelProperty("执行单位")
    private String yunshuExecuteUnit;

    @ExcelProperty("执行单位简称")
    private String yunshuExecuteUnitAbbreviation;


    @ExcelProperty("执行单位idPath")
    private String yunshuExecuteUnitIdPath;

    @ExcelProperty("创新业务分类")
    private String innovativeBusinessType;

    @ExcelProperty("局标准分类")
    private String standardType;


    @ExcelProperty(value = "局名义项目类型", converter = BureauNominalProjectTypeConverter.class)
    private String bureauNominalProjectType;


    /**
     * 质量目标
     */
    @ExcelProperty("质量目标")
    private String qualityTask;

    /**
     * 安全任务
     */
    @ExcelProperty("安全任务")
    private String securityTask;

    /**
     * 竣工备案日期
     */
    @ExcelProperty(value = "竣工备案日期", converter = LongToDateTimeConverter.class)
    private String recordDate;

    /**
     * 承包模式（智慧工地）
     */
    @ExcelProperty("承包模式（智慧工地）")
    private String smartContractModel;

    /**
     * 项目地址
     */
    @ExcelProperty("项目地址")
    private String smartProjectAddress;

    /**
     * 智慧工地项目直接上级云枢组织ID
     */
    @ExcelProperty("项目上级ID")
    private String yunshuParentOrgId;

    /**
     * 智慧工地项目直接上级全称
     */
    @ExcelProperty("项目上级名称")
    private String yunshuParentOrgName;

    /**
     * 智慧工地项目直接上级treeID
     */
    @ExcelProperty("项目上级treeID")
    private String yunshuParentTreeId;

    /**
     * 项目规模
     */
    @ExcelProperty("项目规模")
    private String projectScale;

    /**
     * 经度
     */
    @ExcelProperty("经度")
    private String lng;

    /**
     * 纬度
     */
    @ExcelProperty("纬度")
    private String lat;

    /**
     * 项目唯一标识，含义：接收市场营销立项通知或特殊立项发起后生成.P+年月日+四位流水号。例如P230915001
     */
    @ExcelProperty(value = "项目标识", index = 1)
    private String cpmProjectKey;

    /**
     * 合同开工日期
     */
    @ExcelProperty(value = "合同开工日期", converter = LongToDateTimeConverter.class)
    private String contractStartDate;

    /**
     * 合同竣工日期
     */
    @ExcelProperty(value = "合同竣工日期", converter = LongToDateTimeConverter.class)
    private String contractEndDate;

    /**
     * 标准项目名称，含义：工程名称更新和财商名称更新均更新此字段（工程名称仅在首次新增时使用）
     */
    @ExcelProperty(value = "项目名称(项目中心)", index = 2)
    private String cpmProjectName;

    /**
     * 项目简称: 财商未立项时取工程简称，财商立项后为财商简称
     */
    @ExcelProperty(value = "项目简称(项目中心)", index = 3)
    private String cpmProjectAbbreviation;

    /**
     * 预付款比例
     */
    @ExcelProperty("预付款比例")
    private String advancesRate;

    @ExcelProperty("云筑网（集采）项目编码")
    private String yzwProjectId;


    /**
     * 项目效果图
     */
    @ExcelProperty("项目效果图")
    private String effectPic;


    @ExcelProperty("市场业务板块")
    private String marketingBusinessSegment;

    @ExcelProperty("市场业务板块codePath")
    private String marketingBusinessSegmentCodePath;


    /**
     * 财商业务板块
     */
    @ExcelProperty(value = "财商业务板块")
    private String financeBusinessSegment;

    /**
     * 财商业务板块codepath
     */
    @ExcelProperty(value = "财商业务板块codepath")
    private String financeBusinessSegmentCodePath;

    /**
     * CPM 业务板块代码路径
     */
    @ExcelProperty(value = "项目中心业务板块")
    private String cpmBusinessSegmentCodePath;

    /**
     * CPM 业务部门
     */
    @ExcelProperty(value = "项目中心业务板块名称")
    private String cpmBusinessSegment;
}
