package com.cscec3b.iti.projectmanagement.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.api.dto.request.mdm.MdmOrgInfoReq;
import com.cscec3b.iti.projectmanagement.server.entity.MdmOrgInfo;
import java.util.List;

import com.cscec3b.iti.projectmanagement.server.entity.MerchantInfo;
import org.apache.ibatis.annotations.Param;

public interface MdmOrgInfoMapper extends BaseMapper<MdmOrgInfo> {
    int batchInsertOrUpdate(@Param("list") List<MdmOrgInfo> list);

    List<MdmOrgInfo> getMdmOrgInfo(MdmOrgInfoReq merchantInfoRep);
}