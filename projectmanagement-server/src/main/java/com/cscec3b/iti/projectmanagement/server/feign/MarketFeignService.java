package com.cscec3b.iti.projectmanagement.server.feign;

import com.cscec3b.iti.projectmanagement.api.dto.request.ContractIdPresentationIdMappingReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.ContractIdPresentationIdMappingResp;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "marketing-api", url = "${cscec.market.host:}")
public interface MarketFeignService {

    @Data
    class MarketApiResult<T> {
        private String exceptCauseIp;
        private String exceptCauseApp;
        private String exceptClass;

        private String resultCode;

        private String resultMsg;

        private T data;
    }

    @PostMapping("v1/contractPresentation/getContractIdPresentationIdMapping")
    @ApiOperation("根据评审id获取合同定案id")
    MarketApiResult<List<ContractIdPresentationIdMappingResp>> getContractIdPresentationIdMapping(
            @RequestBody ContractIdPresentationIdMappingReq mappingReq);
}
