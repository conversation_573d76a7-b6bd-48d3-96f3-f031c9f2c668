package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.SwitchOrgResp;
import com.cscec3b.iti.projectmanagement.api.dto.dto.xindun.UserInfo;

/**
 * 六统一用户认证
 *
 * <AUTHOR>
 * @date 2023/10/23 16:33
 **/

public interface IAuthService {


    UserInfo getUserInfo();

    /**
     * 切换组织
     * @param orgId 组织ID
     * @return {@link Boolean}
     */
    SwitchOrgResp switchOrg(String orgId);
}
