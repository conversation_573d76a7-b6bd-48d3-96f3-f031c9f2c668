package com.cscec3b.iti.projectmanagement.server.mapper;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.model.req.ProjectOpenReq;
import com.cscec3b.iti.model.req.open.ProjectYzwMappingReq;
import com.cscec3b.iti.model.resp.ContractFileRelationResp;
import com.cscec3b.iti.model.resp.ProjectArchiveResp;
import com.cscec3b.iti.model.resp.ProjectOpenResp;
import com.cscec3b.iti.model.resp.open.ProjectOpenMappingResp;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ContractRelationDto;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ExecuteUnitTreeDto;
import com.cscec3b.iti.projectmanagement.api.dto.dto.ProjectDto;
import com.cscec3b.iti.projectmanagement.api.dto.dto.YunshuExecuteInfoDto;
import com.cscec3b.iti.projectmanagement.api.dto.dto.project.ProjectAmountDetailDto;
import com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite.SmartSiteProjectSyncDto;
import com.cscec3b.iti.projectmanagement.api.dto.request.FinanceReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.OpenProjectPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.BureauNominalPageParams;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ErrorProjectPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectLatAndLngUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectOpenByFileReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectOpenHookQueryReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectQueryParams;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.special.QuerySpecialProjectParams;
import com.cscec3b.iti.projectmanagement.api.dto.response.BureauNamedProjectRelationship;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.OpenProjectPageResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.BureauNominalProjectPageResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ErrorProjectResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.FinanceResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectOpenByFileResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectOpenHookQueryResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.special.SpecialProjectResp;
import com.cscec3b.iti.projectmanagement.server.entity.Project;
import com.cscec3b.iti.projectmanagement.server.entity.dto.ProjectExportDto;
import com.cscec3b.iti.projectmanagement.server.scheduled.SyncFinanceMDMScheduled;

@Mapper
public interface ProjectMapper extends BaseMapper<Project> {

    List<Project> qryProjectByConId(@Param("contractType") Integer contractType, @Param("contractId") Long contractId);

    int createProject(@Param("vo") Project project);

    int updateProjectById(Project project);

    int updateProjectInPMById(@Param("vo") ProjectUpdateReq projectUpdateReq);

    List<ProjectDto> getAmount(@Param("contractId") Long contractId, @Param("contractType") Integer contractType);

    ProjectDto getContractAmountV2(@Param("contractId") Long independentContractId);

    ProjectDto getBidAmountV2(@Param("contractId") Long independentContractId);

    ProjectResp qryProjectById(Long id);

    List<ContractRelationDto> qryContractList(@Param("independentContractId") Long independentContractId,
            @Param("independentContractType") Integer independentContractType);

    List<ContractRelationDto> qryTenderList(@Param("independentContractId") Long independentContractId,
            @Param("independentContractType") Integer independentContractType);

    int updateStatusById(@Param("id") Long id, @Param("projectStatus") Integer projectStatus);

    Integer checkProjectInfo(Long id);

    List<ProjectResp> pageList(@Param("vo") ProjectQueryParams query);

    // long getCount(@Param("vo") ProjectQueryParams query);

    Integer checkProjectDept(String projectId);

    Integer checkProjectDept(Long id);

    Project selectById(Long id);

    FinanceResp getMarketingProjectForFinance(Long id);

    Integer updateProjectByFinance(FinanceReq financeReq);

    Integer updateProjectByProjectReq(ProjectReq projectReq);

    Integer updateProjectCmdByUc(Project pro);

    List<ProjectOpenResp> getExternalOpenProject(ProjectOpenReq projectOpenReq);

    List<Project> getSmartSiteProject();

    Integer updateProjectBySmartSite(Project project);

    List<SpecialProjectResp> specialProjectList(@Param("vo") QuerySpecialProjectParams params);

    FinanceResp getSpecialProjectForFinance(Long valueOf);

    List<Project> getAllProject();

    int batchUpdateProjectName(List<Project> projects);

    Integer updateProjectYunshuOrgIdById(Project project);

    List<ProjectOpenByFileResp> getProjectOpenByFileResp(Map<String, List<ProjectOpenByFileReq>> fileMap);

    List<Project> getStatusFromSmartSite();

    int batchUpdateProjectStatusFromSmt(List<Project> projects);

    int syncSmartSiteProjectInfo(SmartSiteProjectSyncDto syncDto);

    List<ProjectOpenHookQueryResp> externalOpenHookProject(ProjectOpenHookQueryReq hookReq);

    int updateProjectContractAmountById(Project project);

    List<Project> getProjectOfYunshuUnitIdNull(@Param("set") Set<String> executeUnitIds);

    int updateYunshuUnitByExecuteUnitCode(List<YunshuExecuteInfoDto> dtos);

    int updateYunshuUnitByExecuteUnitId(List<ExecuteUnitTreeDto> dtos);

    Project qryProjectByOriginFileId(@Param("originFileId") Long originFileId, @Param("type") String type);

    int updateProjectAmount(@Param("projectId") Long projectId, @Param("contractAmount") BigDecimal contractAmount);

    List<Project> getHasNoDeptAndProcessList();

    Project getProjectByAssociatedId(@Param("associatedId") Long associatedId);

    BureauNamedProjectRelationship getBureauNominalProject(@Param("type") int type, @Param("projectId") Long projectId,
                                                           @Param("financeCode") String financeCode);

    int changeProjectRelation(@Param("projectId") Long projectId);

    ProjectDetailResp getByFinanceCode(@Param("financeCode") String financeCode);

    int setToSubContractingProject(@Param("generalContractorId") Long generalContractorId,
                                   @Param("projectId") Long projectId);

    int removeSubcontractingProject(@Param("subcontractingId") Long subcontractingId);

    List<BureauNominalProjectPageResp> getBureauNominalProjects(@Param("params") BureauNominalPageParams queryParams);

    List<BureauNominalProjectPageResp> batchBureauGeneralNames(List<Long> ids);

    void setWhetherProjectHasBeenCompleted(@Param("projectId") Long projectId);

    int getProjectHasBeenCompleted(@Param("projectId") Long projectId);

    void projectBeenComplete(@Param("projectId") Long projectId);

    int updateProjectYunshuExecuteUnit(Project project);

    List<ProjectResp> pageListCloudPivot(@Param("vo") ProjectQueryParams query);

    /**
     * 查询项目列表 并导出
     *
     * @param query
     * @return {@link List}<{@link ProjectExportDto}>
     */
    List<ProjectExportDto> listCloudPivotExPort(@Param("vo") ProjectQueryParams query, @Param("offset") long offset,
            @Param("limit") int limit);

    Long getCountCloudPivotExport(@Param("vo") ProjectQueryParams query);


    List<SpecialProjectResp> specialProjectListCloudPivot(@Param("vo") QuerySpecialProjectParams params);

    List<BureauNominalProjectPageResp>
    getBureauNominalProjectsCloudPivot(@Param("params") BureauNominalPageParams queryParams);

    ProjectArchiveResp getProjectArchive(@Param("id") Long projectId);

    Integer updateProjectByYzwProjectId(@Param("id") Long id, @Param("yzwProjectId") String yzwProjectId);

    int getCountIdByCpmProjectKey(@Param("cpmProjectKey") String cpmProjectKey, @Param("id") Long projectId);

    /**
     * 分页查询项目列表
     *
     * @param projectPageReq   分页查询参数
     * @param finalQueryIdPath 最终查询范围
     * @return {@link List}<{@link OpenProjectPageResp}>
     */
    List<OpenProjectPageResp> getOpenProjectPage(@Param("queryIdPath") String finalQueryIdPath,
                                                 @Param("req") OpenProjectPageReq projectPageReq);


    /**
     * 更新项目经纬度信息
     *
     * @param req 经纬度信息
     * @return int
     */
    int updateLatitudeAndLongitude(ProjectLatAndLngUpdateReq req);

    /**
     * 更新项目效果图
     *
     * @param effectPic 项目效果图
     * @param projectId 项目id
     * @return int
     */
    int updateProjectEffectPicture(@Param("effectPic") String effectPic, @Param("id") Long projectId);

    /**
     * 根据map更新项目
     *
     * @param id       id
     * @param fieldMap fieldMap 要更新的字段及值
     * @return {@link Boolean}
     */
    int updateByMap(@Param("id") Long id, @Param("fields") HashMap<String, Object> fieldMap);

    List<ProjectOpenHookQueryResp> hookProject(ProjectOpenHookQueryReq hookReq);

    /**
     * 获取项目关联的所有文件
     *
     * @param independentContractType 独立性类型
     * @param independentContractId   独立性id
     * @return {@link List}<{@link ContractFileRelationResp}>
     */
    List<ContractFileRelationResp> allFiles(@Param("independentContractType") Integer independentContractType,
            @Param("independentContractId") Long independentContractId);

    /**
     * 按文件类型获取文件/合同金额
     *
     * @param independentContractId   独立性id
     * @param independentContractType 独立性类型
     * @return {@link List}<{@link ProjectAmountDetailDto}>
     */
    List<ProjectAmountDetailDto> getFileAmountGroupByFileType(
            @Param("independentContractId") Long independentContractId,
            @Param("independentContractType") Integer independentContractType);

    /**
     * 通过云枢id和项目标识查询项目中云筑网相关信息
     *
     * @param mappingReqs {@link List}<{@link ProjectYzwMappingReq}>
     * @return {@link List }<{@link ProjectOpenMappingResp }>
     */
    List<ProjectOpenMappingResp> getYzwMappingInfo(List<ProjectYzwMappingReq> mappingReqs);

    /**
     * 通过云枢ids查询项目列表
     *
     * @param yunshuOrgIds 云枢ids
     * @return {@link List }<{@link ProjectOpenResp }>
     */
    List<ProjectOpenResp> getProjectPageByYunshuIds(@Param("list") Set<String> yunshuOrgIds);

    /**
     * 获取项目错误列表
     * 
     * @param req {@link ErrorProjectPageReq}
     * @return {@link List }<{@link ErrorProjectResp }>
     */
    List<ErrorProjectResp> getProjectErrorPageList(ErrorProjectPageReq req);

    /**
     * 通过id 批量删除项目
     *
     * @param ids id列表
     * @return int
     */
    int delBatchIds(@Param("ids") List<Long> ids);

    /**
     * 交换两个项目的财商信息
     *
     * @param project 项目信息
     * @return 影响的行数
     */
    int exchangeFinanceInfo(@Param("p") Project project);

    // 在ProjectMapper接口中添加
    int batchUpdateMDMInfo(List<SyncFinanceMDMScheduled.MDMInfo> mdmInfos);

}
