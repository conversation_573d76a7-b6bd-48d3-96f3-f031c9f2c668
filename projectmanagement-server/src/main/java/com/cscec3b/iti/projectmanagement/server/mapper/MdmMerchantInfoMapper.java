package com.cscec3b.iti.projectmanagement.server.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cscec3b.iti.projectmanagement.api.dto.request.merchant.MerchantInfoReq;
import com.cscec3b.iti.projectmanagement.server.entity.MdmMerchantInfo;
import com.cscec3b.iti.projectmanagement.server.entity.MerchantInfo;

import java.util.List;

public interface MdmMerchantInfoMapper extends BaseMapper<MdmMerchantInfo> {
    List<MerchantInfo> getMerchantInfoList(MerchantInfoReq merchantInfoRep);

    void batchInsertOrUpdate(List<MdmMerchantInfo> list);

}