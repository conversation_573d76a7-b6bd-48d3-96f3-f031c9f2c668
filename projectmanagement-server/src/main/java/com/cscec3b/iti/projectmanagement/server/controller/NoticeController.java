package com.cscec3b.iti.projectmanagement.server.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.INoticeApi;
import com.cscec3b.iti.projectmanagement.api.dto.request.notice.NoticeQueryReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.notice.NoticeSaveReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.notice.NoticeUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.notice.NoticeInfoResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.notice.NoticePageResp;
import com.cscec3b.iti.projectmanagement.server.service.INoticeService;

import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;

/**
 * 通知公告中心
 * @Description NoticeController
 * <AUTHOR>
 * @Date 2023/1/3 15:20
 */

@RestController
@RequestMapping(INoticeApi.PATH)
@Api(tags = "通知公告中心")
@Slf4j
public class NoticeController implements INoticeApi {

    @Resource
    INoticeService noticeService;

    /**
     *通知公告列表页查询
     * @param noticeQueryReq
     * @return
     */
    @Override
    public GenericityResponse<Page<NoticePageResp>> page(NoticeQueryReq noticeQueryReq) {
        return new GenericityResponse<>(noticeService.qryNotices(noticeQueryReq));
    }

    /**
     * 首页通知公告列表
     * @return
     */
    @Override
    public GenericityResponse<List<NoticePageResp>> homePage() {
        return new GenericityResponse<>(noticeService.homePageNotice());
    }

    /**
     * 新增通知公告
     * @param noticeSaveReq
     * @return
     */
    @Override
    public GenericityResponse<Long> save(NoticeSaveReq noticeSaveReq) {
        return new GenericityResponse<>(noticeService.createNotice(noticeSaveReq));
    }

    /**
     * 更新通知公告
     * @param noticeUpdateReq
     * @return
     */
    @Override
    public GenericityResponse<Boolean> update(NoticeUpdateReq noticeUpdateReq) {
        return new GenericityResponse<>(noticeService.updateNotice(noticeUpdateReq));
    }

    /**
     * 更新通知公告发布状态
     * @param id
     * @param status
     * @return
     */
    @Override
    public GenericityResponse<Boolean> updatePublishStatus(Long id, Integer status) {
        return new GenericityResponse<>(noticeService.updatePublishStatus(id, status));
    }

    /**
     * 阅读通知公告
     * @param id
     * @return
     */
    @Override
    public GenericityResponse<NoticeInfoResp> view(Long id) {
        return new GenericityResponse<>(noticeService.viewIncreaseQuantity(id));
    }

    /**
     * 获取通知公告详情
     * @param id
     * @return
     */
    @Override
    public GenericityResponse<NoticeInfoResp> detail(Long id) {
        return new GenericityResponse<>(noticeService.getNoticeById(id));
    }

    /**
     * 删除通知公告
     * @param id
     * @return
     */
    @Override
    public GenericityResponse<Boolean> delete(Long id) {
        return new GenericityResponse<>(noticeService.deleteNotice(id));
    }
}
