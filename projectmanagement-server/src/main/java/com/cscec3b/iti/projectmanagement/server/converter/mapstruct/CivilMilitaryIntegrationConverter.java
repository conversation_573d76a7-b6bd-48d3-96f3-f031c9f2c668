package com.cscec3b.iti.projectmanagement.server.converter.mapstruct;

import com.cscec3b.iti.projectmanagement.api.dto.response.secreycivilmilitary.CivilMilitaryIntegrationDetailResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.secreycivilmilitary.CivilMilitaryIntegrationResp;
import com.cscec3b.iti.projectmanagement.server.entity.CivilMilitaryIntegration;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants.ComponentModel;

@Mapper(componentModel = ComponentModel.SPRING)
public interface CivilMilitaryIntegrationConverter extends IConverter<CivilMilitaryIntegrationDetailResp, CivilMilitaryIntegrationResp,
        CivilMilitaryIntegration> {
}
