package com.cscec3b.iti.projectmanagement.server.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cscec3b.iti.projectmanagement.api.dto.dto.FinancialBusinessSegmentDto;
import com.cscec3b.iti.projectmanagement.server.entity.DictMainData;

import java.util.List;

public interface DictMainDataService extends IService<DictMainData> {

    /**
     * 通过idPathName 查询主数据信息
     *
     * @param idPathName 全路径名称
     * @return {@link DictMainData }
     */
    DictMainData getByFullPathName(String idPathName);

    /**
     * 通过财商业务板块全路径编码查询主数据信息
     *
     * @param oldFullPath 财商全路径编码
     * @return {@link DictMainData }
     */
    DictMainData getByOldFullPath(String oldFullPath);

    /**
     * 通过财商业务板块编码查询主数据信息
     *
     * @param oldCode 财商业务板块编码
     * @return {@link DictMainData }
     */
    DictMainData getByOldCode(String oldCode);


    /**
     * 业务板块-树形列表
     *
     * @return {@link List }<{@link FinancialBusinessSegmentDto }>
     */
    List<FinancialBusinessSegmentDto> getTreeList();


    /**
     * 获取列表
     *
     * @return {@link List }<{@link FinancialBusinessSegmentDto }>
     */
    List<FinancialBusinessSegmentDto> getAllList();
}
