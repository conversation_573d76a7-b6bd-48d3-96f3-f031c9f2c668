package com.cscec3b.iti.projectmanagement.server.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CommonJudgeEnum {
    Y("Y", "1", "1", "是"),
    N("N", "0", "2", "否");

    /**
     * 市场营销是否
     */
    private final String code;

    /**
     * 项目中心是否
     */
    private final String key;

    /**
     * 集团主数据是否
     */
    private final String value;

    /**
     * 描述
     */
    private final String msg;

    public String getCode() {
        return code;
    }

    public String getValue() {
        return value;
    }

    public String getKey() {
        return key;
    }

    public String getMsg() {
        return msg;
    }
    public static CommonJudgeEnum getByCode(String code) {
        for (CommonJudgeEnum commonJudgeEnum : CommonJudgeEnum.values()) {
            if (commonJudgeEnum.getCode().equals(code)) {
                return commonJudgeEnum;
            }
        }
        return null;
    }
    public static CommonJudgeEnum getByValue(String value) {
        for (CommonJudgeEnum commonJudgeEnum : CommonJudgeEnum.values()) {
            if (commonJudgeEnum.getValue().equals(value)) {
                return commonJudgeEnum;
            }
        }
        return null;
    }

    public static CommonJudgeEnum getByKey(String value) {
        for (CommonJudgeEnum commonJudgeEnum : CommonJudgeEnum.values()) {
            if (commonJudgeEnum.getKey().equals(value)) {
                return commonJudgeEnum;
            }
        }
        return null;
    }


}
