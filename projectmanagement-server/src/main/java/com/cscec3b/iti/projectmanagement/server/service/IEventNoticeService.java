package com.cscec3b.iti.projectmanagement.server.service;

import com.cscec3b.iti.model.req.ProjectFlowEventNoticeReq;
import com.cscec3b.iti.projectmanagement.server.event.SheJiYuanEpcEvent;

/**
 * 人资系统 通知服务
 *
 * <AUTHOR>
 * @date 2024/11/27
 */
public interface IEventNoticeService {
    /**
     * 向 HR 发送通知
     *
     * @param projectEventNoticeReq 项目活动通知 req
     * @return {@link Boolean }
     */
    Boolean sendNoticeToHr(ProjectFlowEventNoticeReq projectEventNoticeReq) throws Exception;

    /**
     * 设计院 Epc项目监听器
     * 
     * @param syjEvent SYJ 事件
     * @throws Exception 例外
     */
    void SjyEventListener(SheJiYuanEpcEvent syjEvent) throws Exception;
}
