package com.cscec3b.iti.projectmanagement.server.service;

import java.util.List;

import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.dto.AttachmentDto;
import com.cscec3b.iti.projectmanagement.api.dto.request.notice.NoticeQueryReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.notice.NoticeSaveReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.notice.NoticeUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.notice.NoticeInfoResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.notice.NoticePageResp;

/**
 * @Description INoticeService
 * <AUTHOR>
 * @Date 2023/1/3 15:35
 */
public interface INoticeService {
    /**
     * 查询通知请求
     *
     * @param noticeReq 通知请求
     * @return {@link Page}<{@link NoticePageResp}>
     */
    Page<NoticePageResp> qryNotices(NoticeQueryReq noticeReq);

    /**
     * 通知主页
     *
     * @return {@link List}<{@link NoticePageResp}>
     */
    List<NoticePageResp> homePageNotice();

    /**
     * 更新通知
     *
     * @param noticeUpdateReq 通知请求对象
     * @return {@link Boolean}
     */
    Boolean updateNotice(NoticeUpdateReq noticeUpdateReq);

    /**
     * 创建通知
     *
     * @param noticeSaveReq 通知保存对象
     * @return {@link Long}
     */
    Long createNotice(NoticeSaveReq noticeSaveReq);

    /**
     * 更新发布状态
     *
     * @param id     id
     * @param status 状态
     * @return {@link Boolean}
     */
    Boolean updatePublishStatus(Long id, Integer status);

    /**
     * 阅读量增加
     *
     * @param id id
     * @return {@link NoticeInfoResp}
     */
    NoticeInfoResp viewIncreaseQuantity(Long id);

    /**
     * 通过id获取通知
     *
     * @param id id
     * @return {@link NoticeInfoResp}
     */
    NoticeInfoResp getNoticeById(Long id);

    /**
     * 删除通知
     *
     * @param id id
     * @return {@link Boolean}
     */
    Boolean deleteNotice(Long id);

    /**
     * 删除附件
     *
     * @param noticeId 通知id
     * @return {@link List}<{@link AttachmentDto}>
     */
    List<AttachmentDto> qryAttachments(Long noticeId);
}
