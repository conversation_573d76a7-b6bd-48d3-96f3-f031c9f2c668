package com.cscec3b.iti.projectmanagement.server.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cscec3b.iti.common.web.exception.BusinessException;
import com.cscec3b.iti.projectmanagement.api.dto.request.workflow.G3WfCallBackReq;
import com.cscec3b.iti.projectmanagement.server.bidapprovalservice.IBidApprovalService;
import com.cscec3b.iti.projectmanagement.server.constant.Constants;
import com.cscec3b.iti.projectmanagement.server.entity.BidApproval;
import com.cscec3b.iti.projectmanagement.server.entity.CivilMilitaryIntegration;
import com.cscec3b.iti.projectmanagement.server.entity.WfApprovaler;
import com.cscec3b.iti.projectmanagement.server.enums.IndContractsTypeEnum;
import com.cscec3b.iti.projectmanagement.server.enums.workflow.WfTaskStateEnum;
import com.cscec3b.iti.projectmanagement.server.mapper.CivilMilitaryIntegrationMapper;
import com.cscec3b.iti.projectmanagement.server.service.AbstractWorkFlowCallBackService;
import com.cscec3b.iti.projectmanagement.server.service.IdTableService;
import com.cscec3b.iti.projectmanagement.server.service.WfApprovalerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * 军民融合项目流程 回调服务 impl
 *
 * <AUTHOR>
 * @date 2024/04/15
 */
@Slf4j
@Service
@Transactional
@RequiredArgsConstructor
public class CivilMilitaryWorkFlowCallBackServiceImpl extends AbstractWorkFlowCallBackService {


    private final WfApprovalerService wfApprovalService;

    /**
     * 中标未立项审批服务
     */
    private final IBidApprovalService bidApprovalService;

    /**
     * 军民融合Maper
     */
    private final CivilMilitaryIntegrationMapper civilMilitaryIntegrationMapper;

    /**
     * id 服务
     */
    private final IdTableService idService;

    private

    CivilMilitaryIntegration getCivilMilitaryInfo(Long civilMilitaryId) {
        final CivilMilitaryIntegration civilMilitaryIntegration = civilMilitaryIntegrationMapper.selectById(civilMilitaryId);
        if (ObjectUtils.isEmpty(civilMilitaryIntegration)) {
            throw new BusinessException(80107001);
        }
        return civilMilitaryIntegration;
    }


    @Override
    public Boolean start(G3WfCallBackReq callBackDto) {
        final String businessKey = callBackDto.getBusinessKey();
        final CivilMilitaryIntegration civilMilitaryInfo = this.getCivilMilitaryInfo(Long.valueOf(businessKey));
        civilMilitaryInfo.setApprovalStatus(WfTaskStateEnum.INPROGRESS.getDictCode())
                .setApprovalBeginTime(Instant.now().toEpochMilli());
        civilMilitaryIntegrationMapper.updateById(civilMilitaryInfo);
        return super.start(callBackDto);
    }

    /**
     * 流程正常结束
     *
     * @param callBackDto 回调参数
     * @return {@link Boolean}
     */
    @Override
    public Boolean end(G3WfCallBackReq callBackDto) {
        final CivilMilitaryIntegration civilMilitaryInfo = getCivilMilitaryInfo(Long.valueOf(callBackDto.getBusinessKey()));
        //修改当前流程状态
        civilMilitaryInfo.setApprovalStatus(WfTaskStateEnum.AGREE.getDictCode());
        civilMilitaryIntegrationMapper.updateById(civilMilitaryInfo);
        // 查询审批人信息
        final List<WfApprovaler> wfApprovalers =
                wfApprovalService.list(Wrappers.<WfApprovaler>lambdaQuery().eq(WfApprovaler::getProcInstId,
                        callBackDto.getProcessInstanceId()).orderByDesc(WfApprovaler::getCreateAt));
        final WfApprovaler endApprovaler =
                wfApprovalers.stream().filter(er -> StringUtils.isNotBlank(er.getApprovalerAccount())).findFirst().get();
        final WfApprovaler startApprovaler =
                wfApprovalers.stream().sorted(Comparator.comparing(WfApprovaler::getCreateAt)).filter(er -> StringUtils.isNotBlank(er.getApprovalerAccount())).findFirst().get();

        // 初始化中标未立项
        final BidApproval bidApproval = new BidApproval();
        bidApproval.setApprovalBeginTime(civilMilitaryInfo.getApprovalBeginTime()).setBelongId(civilMilitaryInfo.getId())
                .setType(IndContractsTypeEnum.CIVIL_MILITARY_INTEGRATION.getEnUS()).setProjectCode(civilMilitaryInfo.getFileCode())
                .setProjectName(civilMilitaryInfo.getProjectName()).setCustomerName(civilMilitaryInfo.getCustomerName())
                .setAddress(civilMilitaryInfo.getProjectAddress())
                .setYunshuExecuteUnitId(civilMilitaryInfo.getYunshuExecuteUnitId())
                .setYunshuExecuteUnitCode(civilMilitaryInfo.getYunshuExecuteUnitCode())
                .setYunshuExecuteUnitIdPath(civilMilitaryInfo.getYunshuExecuteUnitIdPath())
                .setYunshuExecuteUnit(civilMilitaryInfo.getYunshuExecuteUnit())
                .setCreateHead(Constants.DATA_FROM_N_CODE)
                .setCreateBy(civilMilitaryInfo.getCreateBy())
                .setCreateAt(Instant.now().toEpochMilli())
                .setSubmitPerson(startApprovaler.getApprovalerAccount())
                .setSubmitPersonName(startApprovaler.getApprovalerName())
                .setApprovalPerson(endApprovaler.getApprovalerAccount())
                .setApprovalPersonName(endApprovaler.getApprovalerName());
        // 拆分区域
        final String region = civilMilitaryInfo.getRegion();
        if (StringUtils.isNotBlank(region)) {
            if (region.startsWith("/中国")) {
                final String[] regions = region.split("/");
                bidApproval.setProvince(Optional.ofNullable(regions[2]).orElse(null));
                bidApproval.setCity(Optional.ofNullable(regions[3]).orElse(null));
                bidApproval.setRegion(Optional.ofNullable(regions[4]).orElse(null));
            }else if (region.startsWith("/")) {
                final String[] regions = region.split("/");
                bidApproval.setCountry(regions[0]);
            }
        }
        bidApprovalService.save(bidApproval);
        return super.end(callBackDto);
    }

    @Override
    public Boolean complete(G3WfCallBackReq callBackDto) {
        civilMilitaryIntegrationMapper.update(null, Wrappers.<CivilMilitaryIntegration>lambdaUpdate()
                .set(CivilMilitaryIntegration::getApprovalStatus, WfTaskStateEnum.INPROGRESS.getDictCode())
                        .eq(CivilMilitaryIntegration::getId, callBackDto.getBusinessKey()));
        return super.complete(callBackDto);
    }

    /**
     * 流程终止后(不通过)需要生成生成一个新的数据
     *
     * @param callBackDto
     * @return {@link Boolean}
     */
    @Override
    public Boolean terminate(G3WfCallBackReq callBackDto) {
        final CivilMilitaryIntegration civilMilitaryInfo  =
                getCivilMilitaryInfo(Long.valueOf(callBackDto.getBusinessKey()));
        // 旧的数据设置为不通过,禁止编辑
        civilMilitaryInfo.setApprovalStatus(WfTaskStateEnum.UNPASS.getDictCode());
        civilMilitaryIntegrationMapper.updateById(civilMilitaryInfo);
        // 生成新的belongId ， 重置审批状态及id
        civilMilitaryInfo.setBelongId(IdUtil.getSnowflake().nextId())
                .setFileCode(idService.getCommonKey(Constants.CIVIL_MILITARY_SEQUENCE_PREFIX))
                .setApprovalStatus(WfTaskStateEnum.INIT.getDictCode()).setId(null);
        civilMilitaryIntegrationMapper.insert(civilMilitaryInfo);
        return super.terminate(callBackDto);
    }

    /**
     * @param callBackDto 回调参数
     * @return {@link Boolean}
     */
    @Override
    public Boolean withdraw(G3WfCallBackReq callBackDto) {
        civilMilitaryIntegrationMapper.update(null,
                Wrappers.<CivilMilitaryIntegration>lambdaUpdate().set(CivilMilitaryIntegration::getApprovalStatus,
                        WfTaskStateEnum.INIT.getDictCode()).eq(CivilMilitaryIntegration::getId,
                        callBackDto.getBusinessKey()));
        return super.withdraw(callBackDto);
    }

    /**
     * 流程撤销
     *
     * @param callBackDto 回调参数
     * @return {@link Boolean}
     */
    @Override
    public Boolean cancel(G3WfCallBackReq callBackDto) {
        final CivilMilitaryIntegration civilMilitaryInfo =
                getCivilMilitaryInfo(Long.valueOf(callBackDto.getBusinessKey()));
        // 设置旧的流程为作废状态，禁编辑
        civilMilitaryInfo.setApprovalStatus(WfTaskStateEnum.CANCEL.getDictCode());
        civilMilitaryIntegrationMapper.updateById(civilMilitaryInfo);
        return super.cancel(callBackDto);
    }


    @Override
    public Boolean sign(G3WfCallBackReq callBackDto) {
        return super.sign(callBackDto);
    }

    /**
     * 流程被驳回 回到第一步
     *
     * @param callBackDto 回调参数
     * @return {@link Boolean}
     */
    @Override
    public Boolean reject(G3WfCallBackReq callBackDto) {
        civilMilitaryIntegrationMapper.update(null,
                Wrappers.<CivilMilitaryIntegration>lambdaUpdate().set(CivilMilitaryIntegration::getApprovalStatus,
                        WfTaskStateEnum.REJECT.getDictCode()).eq(CivilMilitaryIntegration::getId,
                        callBackDto.getBusinessKey()));
        return super.reject(callBackDto);
    }

    @Override
    public Boolean timeOut(G3WfCallBackReq callBackDto) {
        return super.timeOut(callBackDto);
    }
}
