package com.cscec3b.iti.projectmanagement.server.converter.excelConverter;

import com.alibaba.excel.converters.Converter;
import com.alibaba.excel.metadata.GlobalConfiguration;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.metadata.property.ExcelContentProperty;
import lombok.extern.slf4j.Slf4j;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.Objects;

/**
 * 时间戳转日期格式转换器
 *
 * <AUTHOR>
 * @date 2024/07/01
 */
@Slf4j
public class LongToDateTimeConverter implements Converter<String> {
    @Override
    public WriteCellData<?> convertToExcelData(String value, ExcelContentProperty contentProperty,
            GlobalConfiguration globalConfiguration) throws Exception {
        log.info("LongToDateTimeConverter convertToExcelData value:  {}, filedName: {}", value,
                contentProperty.getField().getName());
        if (Objects.nonNull(value)) {
            if ("0".equals(value)) {
                return new WriteCellData<>(value);
            }
            Instant instant = Instant.ofEpochMilli(Long.parseLong(value));
            LocalDate localDate = instant.atZone(ZoneId.systemDefault()).toLocalDate();

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年M月d日", Locale.CHINA);
            String formattedDate = localDate.format(formatter);
            return new WriteCellData<>(formattedDate);
        }

        return null;
    }
}
