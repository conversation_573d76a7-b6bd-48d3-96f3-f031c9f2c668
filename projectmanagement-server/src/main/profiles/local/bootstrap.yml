spring:
  application:
    name: @project.parent.artifactId@
  profiles:
    active: @active-profile@
  cloud:
    nacos:
      discovery:
        enabled: false #开发环境关闭 nacos 服务发现
        server-addr: http://172.19.5.91:8848
        username: nacos
        password: GjCb5r2aQ3m!
        namespace: xinfa
      config: #开发环境关闭 nacos 配置中心配置1
        enabled: false
        server-addr: http://172.19.5.91:8848
        username: nacos
        password: GjCb5r2aQ3m!
        namespace: xinfa
        file-extension: yaml

#logging:
#  level:
#    com.alibaba.cloud.nacos.client: DEBUG
#    com.cscec3b.iti: DEBUG
#    io.springfox: DEBUG
logging:
  level:
    com.alibaba.nacos.*: warn
    com.alibaba.cloud.nacos.*: warn
    com.cscec3b.iti: DEBUG
    io.springfox: WARN
    org.redisson.*: WARN
    springfox.*: WARN
    org.springframework: WARN
    org.springframework.boot: WARN
    io.netty.util: WARN
    org.hibernate: WARN
    com.baomidou: WARN
    io.lettuce: WARN
    org.apache.shenyu.*: OFF
    org.springframework.scheduling.quartz: DEBUG