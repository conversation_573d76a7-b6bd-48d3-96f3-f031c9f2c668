package com.cscec3b.iti.projectmanagement.api.dto.request.project;

import java.io.Serializable;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 开放接口: 通过合同文件信息查询项目信息
 *
 * <AUTHOR>
 * @date 2023/03/31 14:45
 **/

@Data
@ApiModel(value = "项目对外请求数据-合同信息", description = "合同信息")
public class ProjectOpenByFileReq implements Serializable {

    @ApiModelProperty(value = "合同类型 1.投标总结 2.合同定案 3.补充协议 4.局内分包合同 5.局内分包合同补充协议")
    @NotNull(message = "合同类型信息不能为空")
    private int contractType;

    @ApiModelProperty(value = "合同ID")
    @NotBlank(message = "合同ID不能为空")
    private String contractId;

}
