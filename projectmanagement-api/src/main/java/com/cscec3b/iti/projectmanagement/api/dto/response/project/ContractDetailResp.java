package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@Accessors(chain = true)
@ApiModel(value = "ContractDetailResp", description = "合同定案详细信息响应对象")
public class ContractDetailResp implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 发起人单位
     */
    @ApiModelProperty(value = "发起人", position = 1)
    private String submitPerson;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称", position = 2)
    private String projectName;

    /**
     * 工程编号
     */
    @ApiModelProperty(value = "工程编号", position = 3)
    private String projectCode;

    /**
     * 工程简称
     */
    @ApiModelProperty(value = "工程简称", position = 4)
    private String projectShortName;

    /**
     * 工程属地
     */
    @ApiModelProperty(value = "工程属地", position = 5)
    private String projectBelong;

    /**
     * 省
     */
    @ApiModelProperty(value = "省", position = 6)
    private String province;

    /**
     * 市
     */
    @ApiModelProperty(value = "市", position = 7)
    private String city;

    /**
     * 区
     */
    @ApiModelProperty(value = "区", position = 8)
    private String region;

    /**
     * 具体地址
     */
    @ApiModelProperty(value = "具体地址", position = 9)
    private String address;

    /**
     * 国别
     */
    @ApiModelProperty(value = "国别", position = 10)
    private String country;

    /**
     * 工程类型（国家标准）
     */
    @ApiModelProperty(value = "工程类型（国家标准）", position = 11)
    private String countryProjectType;

    /**
     * 工程类型（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）", position = 12)
    private String marketProjectType;

    /**
     * 工程类型(总公司市场口径)2
     */
    @ApiModelProperty(value = "工程类型(总公司市场口径)2", position = 13)
    private String marketProjectType2;

    /**
     * 工程类型(总公司综合口径)
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)", position = 14)
    private String projectType;

    /**
     * 工程类型(总公司综合口径)2
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)2", position = 15)
    private String projectType2;

    /**
     * 工程类型(总公司综合口径)3
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)3", position = 16)
    private String projectType3;

    /**
     * 工程类型(总公司综合口径)4
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)4", position = 17)
    private String projectType4;

    /**
     * 项目附件信息对象
     */
    @ApiModelProperty(value = "项目附件信息对象", position = 18)
    private String projectAttachment;

    /**
     * 总分包类别
     */
    @ApiModelProperty(value = "总分包类别", position = 19)
    private String totalSubcontractingCategory;

    /**
     * 结构形式
     */
    @ApiModelProperty(value = "结构形式", position = 20)
    private String structuralStyle;

    /**
     * 结构形式2
     */
    @ApiModelProperty(value = "结构形式2", position = 21)
    private String structuralStyle2;

    /**
     * 是否有钢结构
     */
    @ApiModelProperty(value = "是否有钢结构", position = 22)
    private String includingSteel;

    /**
     * 最长桩基长度
     */
    @ApiModelProperty(value = "最长桩基长度", position = 23)
    private String projectMaxLength;

    /**
     * 最大桩径
     */
    @ApiModelProperty(value = "最大桩径", position = 24)
    private String projectMaxWidth;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型", position = 25)
    private String contractType;

    /**
     * 是否装配式
     */
    @ApiModelProperty(value = "是否装配式", position = 26)
    private String fabricated;

    /**
     * 是否为投融资带动项目
     */
    @ApiModelProperty(value = "是否为投融资带动项目", position = 27)
    private String isInvestmentFinancingDrivenProjects;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", position = 28)
    private String businessType;

    /**
     * 是否投资项目
     */
    @ApiModelProperty(value = "是否投资项目", position = 29)
    private String investmentProjects;

    /**
     * 投资主体
     */
    @ApiModelProperty(value = "投资主体", position = 30)
    private String investors;

    /**
     * 所属办事处
     */
    @ApiModelProperty(value = "所属办事处", position = 31)
    private String signFormOffice;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", position = 32)
    private String customerName;

    /**
     * 客户母公司
     */
    @ApiModelProperty(value = "客户母公司", position = 33)
    private String superiorCompanyName;

    /**
     * 客户企业性质
     */
    @ApiModelProperty(value = "客户企业性质", position = 34)
    private String enterpriseType;

    /**
     * 建设单位（甲方）联系人
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人", position = 35)
    private String contactPerson;

    /**
     * 建设单位（甲方）联系人电话
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人电话", position = 36)
    private String contactPersonMobile;

    /**
     * 设计单位
     */
    @ApiModelProperty(value = "设计单位", position = 37)
    private String designer;

    /**
     * 监理单位
     */
    @ApiModelProperty(value = "监理单位", position = 38)
    private String supervisor;

    /**
     * 实际中标日期
     */
    @ApiModelProperty(value = "实际中标日期", position = 39)
    private Long successfulTime;

    /**
     * 实际签约日期
     */
    @ApiModelProperty(value = "实际签约日期", position = 40)
    private Long actualSignedTime;

    /**
     * 签约主体
     */
    @ApiModelProperty(value = "签约主体", position = 41)
    private String signedSubjectValue;

    /**
     * 签约主体code
     */
    @ApiModelProperty(value = "签约主体code", position = 41)
    private String signedSubjectCode;

    /**
     * 实施单位
     */
    @ApiModelProperty(value = "实施单位", position = 42)
    private String doUnit;

    /**
     * 含税合同总价（人民币）
     */
    @ApiModelProperty(value = "含税合同总价（人民币）", position = 43)
    private BigDecimal totalAmount;

    /**
     * 不含税金额
     */
    @ApiModelProperty(value = "不含税金额", position = 44)
    private BigDecimal noTaxIncludedMoney;

    /**
     * 自行施工不含税金额
     */
    @ApiModelProperty(value = "自行施工不含税金额", position = 45)
    private BigDecimal midAmountSelf;

    /**
     * 土建不含税金额
     */
    @ApiModelProperty(value = "土建不含税金额", position = 46)
    private BigDecimal selfCivilAmount;

    /**
     * 安装不含税金额
     */
    @ApiModelProperty(value = "安装不含税金额", position = 47)
    private BigDecimal selfInstallAmount;

    /**
     * 钢结构不含税金额
     */
    @ApiModelProperty(value = "钢结构不含税金额", position = 48)
    private BigDecimal selfSteelStructureAmount;

    /**
     * 总包服务费
     */
    @ApiModelProperty(value = "总包服务费", position = 49)
    private BigDecimal selfTotalServiceAmount;

    /**
     * 暂列金额或甲指分包金额
     */
    @ApiModelProperty(value = "暂列金额或甲指分包金额", position = 50)
    private BigDecimal subcontractAmount;

    /**
     * 销项税额
     */
    @ApiModelProperty(value = "销项税额", position = 51)
    private BigDecimal projectTaxAmount;

    /**
     * 合同优化条款
     */
    @ApiModelProperty(value = "合同优化条款", position = 52)
    private String contractOptimizeClause;

    /**
     * 合同优化金额
     */
    @ApiModelProperty(value = "合同优化金额", position = 53)
    private BigDecimal contractOptimizeAmount;

    /**
     * 合同优化率
     */
    @ApiModelProperty(value = "合同优化率", position = 54)
    private String contractOptimizeRatio;

    /**
     * 暂列金额工作内容
     */
    @ApiModelProperty(value = "暂列金额工作内容", position = 55)
    private String subcontractContent;

    /**
     * 中标项目经理
     */
    @ApiModelProperty(value = "中标项目经理", position = 56)
    private String bidManager;

    /**
     * 中标项目经理注册证书编号
     */
    @ApiModelProperty(value = "中标项目经理注册证书编号", position = 57)
    private String bidManagerCode;

    /**
     * 执行项目经理
     */
    @ApiModelProperty(value = "执行项目经理", position = 58)
    private String excuteManager;

    /**
     * 执行项目经理联系方式
     */
    @ApiModelProperty(value = "执行项目经理联系方式", position = 59)
    private String excuteManagerCode;

    /**
     * 合同项目经理
     */
    @ApiModelProperty(value = "合同项目经理", position = 60)
    private String contractManager;

    /**
     * 合同项目经理注册证书编号
     */
    @ApiModelProperty(value = "合同项目经理注册证书编号", position = 61)
    private String contractManagerCode;

    /**
     * 政府备案项目经理
     */
    @ApiModelProperty(value = "政府备案项目经理", position = 62)
    private String governmentManager;

    /**
     * 政府备案项目经理注册证书编号
     */
    @ApiModelProperty(value = "政府备案项目经理注册证书编号", position = 63)
    private String governmentManagerCode;

    /**
     * 承包模式
     */
    @ApiModelProperty(value = "承包模式", position = 64)
    private String contractMode1;

    /**
     * 承包模式2
     */
    @ApiModelProperty(value = "承包模式2", position = 65)
    private String contractMode2;

    /**
     * 合同承包范围
     */
    @ApiModelProperty(value = "合同承包范围", position = 66)
    private String contractScope;

    /**
     * 发包人指定分包、独立分包的工程
     */
    @ApiModelProperty(value = "发包人指定分包、独立分包的工程", position = 67)
    private String issuerProject;

    /**
     * 总工期（天）
     */
    @ApiModelProperty(value = "总工期（天）", position = 68)
    private Integer countDays;

    /**
     * 合同开工日期
     */
    @ApiModelProperty(value = "合同开工日期", position = 69)
    private Long workerBeginTime;

    /**
     * 合同竣工日期
     */
    @ApiModelProperty(value = "合同竣工日期", position = 70)
    private Long workerEndTime;

    /**
     * 实际开工日期
     */
    @ApiModelProperty(value = "实际开工日期", position = 71)
    private Long realWorkBeginTime;

    /**
     * 预计实际竣工日期
     */
    @ApiModelProperty(value = "预计实际竣工日期", position = 72)
    private Long predictWorkEndTime;

    /**
     * 工期奖罚类型
     */
    @ApiModelProperty(value = "工期奖罚类型", position = 73)
    private String workerDateRewardPunish;

    /**
     * 工期奖罚条款
     */
    @ApiModelProperty(value = "工期奖罚条款", position = 74)
    private String workerRewardPunishAppoint;

    /**
     * 合同范本类型
     */
    @ApiModelProperty(value = "合同范本类型", position = 75)
    private String contractStyle;

    /**
     * 质量要求
     */
    @ApiModelProperty(value = "质量要求", position = 76)
    private String qualityGuarantee;

    /**
     * 质量奖罚类型
     */
    @ApiModelProperty(value = "质量奖罚类型", position = 77)
    private String rewardPunishType;

    /**
     * 质量奖罚条款
     */
    @ApiModelProperty(value = "质量奖罚条款", position = 78)
    private String rewardPunishTerms;

    /**
     * 安全文明施工要求
     */
    @ApiModelProperty(value = "安全文明施工要求", position = 79)
    private String safetyRequirement;

    /**
     * 安全文明施工奖罚条款
     */
    @ApiModelProperty(value = "安全文明施工奖罚条款", position = 80)
    private String safetyRewardPunishTerms;

    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式", position = 81)
    private String pricingMethod;

    /**
     * 合同形式
     */
    @ApiModelProperty(value = "合同形式", position = 82)
    private String contractForm;

    /**
     * 人工费是否可调
     */
    @ApiModelProperty(value = "人工费是否可调", position = 83)
    private String costOfLaborChange;

    /**
     * 主材费是否可调
     */
    @ApiModelProperty(value = "主材费是否可调", position = 84)
    private String costOfLaborChange2;

    /**
     * 是否有预付款
     */
    @ApiModelProperty(value = "是否有预付款", position = 85)
    private String advancesFlag;

    /**
     * 进度款付款方式
     */
    @ApiModelProperty(value = "进度款付款方式", position = 86)
    private String advancesWay;

    /**
     * 月进度付款比例
     */
    @ApiModelProperty(value = "月进度付款比例", position = 87)
    private String advancesMonthRate;

    /**
     * 竣工验收支付比例
     */
    @ApiModelProperty(value = "竣工验收支付比例", position = 88)
    private String completedRate;

    /**
     * 竣工验收收款周期（月）
     */
    @ApiModelProperty(value = "竣工验收收款周期（月）", position = 89)
    private String completedCycle;

    /**
     * 结算支付比例
     */
    @ApiModelProperty(value = "结算支付比例", position = 90)
    private String settlementRate;

    /**
     * 结算周期（月）
     */
    @ApiModelProperty(value = "结算周期（月）", position = 91)
    private String settlementCycle;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号", position = 92)
    private String contractCode;

    /**
     * 保修金
     */
    @ApiModelProperty(value = "保修金", position = 93)
    private String warrantyPremium;

    /**
     * 保修金比例
     */
    @ApiModelProperty(value = "保修金比例", position = 94)
    private String warrantyPremiumRate;

    /**
     * 保修金支付方式
     */
    @ApiModelProperty(value = "保修金支付方式", position = 95)
    private String warrantyPremiumWay;

    /**
     * 是否垫资
     */
    @ApiModelProperty(value = "是否垫资", position = 96)
    private String advancesFundFlag;

    /**
     * 履约担保方式
     */
    @ApiModelProperty(value = "履约担保方式", position = 97)
    private String guaranteeWay;

    /**
     * 项目及土地是否合法
     */
    @ApiModelProperty(value = "项目及土地是否合法", position = 98)
    private String landLegalityFlag;

    /**
     * 是否放弃优先受偿权
     */
    @ApiModelProperty(value = "是否放弃优先受偿权", position = 99)
    private String giveUpCompensateFlag;

    /**
     * 付款比例是否低于百分之八十
     */
    @ApiModelProperty(value = "付款比例是否低于百分之八十", position = 100)
    private String payRateLessEightyFlag;

    /**
     * 支付节点时间是否超2个月
     */
    @ApiModelProperty(value = "支付节点时间是否超2个月", position = 101)
    private String nodeMoreTwoMonthFlag;

    /**
     * 独立合同ID
     */
    @ApiModelProperty(value = "独立合同ID", position = 102)
    private Long independentContractId;

    /**
     * 独立合同类型：1投标总结；2补充协议；3局内分包合同
     */
    @ApiModelProperty(value = "独立合同类型：1投标总结；2补充协议；3局内分包合同", position = 103)
    private Integer independentContractType;

    /**
     * 独立文件id
     */
    @ApiModelProperty(value = "独立文件id", position = 104)
    private Long originFileId;

    /**
     * 接口幂等性校验字段
     */
    @ApiModelProperty(value = "所属源文件ID，接口幂等性校验字段", position = 105)
    private Long belongId;

    /**
     * 是否首合同
     */
    @ApiModelProperty(value = "是否首合同", position = 106)
    private Integer isFirstContract;

    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别", position = 107)
    private String customerLevel;

    /**
     * 其他金额
     */
    @ApiModelProperty(value = "其他金额", position = 107)
    private BigDecimal selfOtherAmount;

    /**
     * 其他金额
     */
    @ApiModelProperty(value = "支付方式", position = 108)
    private String payTypeNew;

    @ApiModelProperty(value = "是否战新", position = 109)
    private String ifStrategicNewBusiness;

    @ApiModelProperty(value = "战新业务一级分类", position = 110)
    private String strategicNewBusinessType;

    @ApiModelProperty(value = "战新业务二级分类", position = 111)
    private String strategicNewBusinessType2;

    @ApiModelProperty(value = "战新业务三级分类", position = 112)
    private String strategicNewBusinessType3;

}
