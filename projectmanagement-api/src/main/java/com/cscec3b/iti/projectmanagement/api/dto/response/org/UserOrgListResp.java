package com.cscec3b.iti.projectmanagement.api.dto.response.org;


import com.g3.org.api.dto.executeForQueryDepartmentList.resp.UserDepartment;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Optional;

/**
 * 用户组织列表响应信息
 *
 * <AUTHOR>
 * @date 2024/08/15
 */
@Data
@Accessors(chain = true)
@ApiModel("用户组织列表响应信息")
public class UserOrgListResp implements Serializable {

    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID")
    private String orgId;

    /**
     * 组织名称
     */
    @ApiModelProperty(value = "组织名称")
    private String orgName;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private String departmentId;

    /**
     * 节点类型
     */
    @ApiModelProperty(value = "节点类型")
    private String nodeType;

    /**
     * 组织简称
     */
    @ApiModelProperty(value = "组织简称")
    private String orgShortName;

    /**
     * 树ID
     */
    @ApiModelProperty(value = "树ID")
    private String treeId;

    public UserOrgListResp() {
    }

    public UserOrgListResp(UserDepartment userDepartment) {
        this.orgId = userDepartment.getOrgID();
        this.orgName = userDepartment.getOrgFullName();
        this.departmentId = userDepartment.getOrgID();
        this.nodeType = null;
        this.orgShortName = userDepartment.getOrgFullName();
        this.treeId = Optional.ofNullable(userDepartment.getQueryCode())
                .map(code -> code.split("#"))
                .map(segments -> segments.length > 0 ? segments[segments.length - 1] : null).orElse(null);
    }
}
