package com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * 项目立项步骤-独立性判断请求参数
 *
 * <AUTHOR>
 * @date 2024/01/04
 */
@Getter
@Setter
@ApiModel("项目立项步骤-类型确认请求参数")
public class StepOfIndependentJudgmentReq extends ApprovalStepReq implements Serializable {

    /**
     * 是否独立项目 N：否 ； Y： 是；D：不予立项；默认：0
     */
    @ApiModelProperty(value = "是否独立项目 N：否 ； Y： 是；D：不予立项；默认：0")
    @NotBlank(message = "是否独立项目不能为空")
    private String independentProject;

    /**
     * 非独立性挂接项目id
     */
    @ApiModelProperty(value = "非独立性挂接项目id")
    private Long projectId;
}
