package com.cscec3b.iti.projectmanagement.api.bidapproval.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 局内分包合同
 *
 * <AUTHOR>
 * @Description
 * @Date 2022/10/18 15:53
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "BureauContractFileReq", description = "局内分包合同")
public class BidBureauContractFileReq extends MarketFileBaseReq implements Serializable {
    /**
     * 复核审批用户id
     */
    @ApiModelProperty(value = "复核审批用户id", position = 1)
    // @Size(max = 20, message = "复核审批用户id的长度必须小于20")
    // @NotNull(message = "复核审批用户id不能为空")
    private String approvalPerson;

    /**
     * 是否创建指挥部
     */
    @ApiModelProperty(value = "是否创建指挥部", position = 3)
    @Size(max = 2, message = "是否创建指挥部的长度必须小于2")
    private String isCreateHead;

    /**
     * 执行单位
     */
    @ApiModelProperty(value = "执行单位", position = 4)
    @Size(max = 200, message = "执行单位的长度必须小于128")
    private String executeUnit;

    /**
     * 独立性
     */
    @ApiModelProperty(value = "独立性", position = 5)
    @Size(max = 2, message = "独立性的长度必须小于2")
    private String isIndependent;

    /**
     * 关联工程/合同
     */
    @ApiModelProperty(value = "关联工程/合同", position = 6)
    @Size(max = 32, message = "关联工程/合同的长度必须小于32")
    private String associatedProject;

    /**
     * 合同编号
     */
    @ApiModelProperty(value = "合同编号", position = 7)
    @Size(max = 32, message = "合同编号的长度必须小于32")
    private String contractCode;

    /**
     * 工程编号
     */
    @ApiModelProperty(value = "工程编号", position = 8)
    @Size(max = 32, message = "工程编号的长度必须小于32")
    @NotNull(message = "工程编号不能为空")
    private String projectCode;


    /**
     * 工程简称
     */
    @ApiModelProperty(value = "工程简称", position = 10)
    @Size(max = 255, message = "工程简称的长度必须小于{max}")
    private String projectShortName;


    /**
     * 工程类型（国家标准）
     */
    @ApiModelProperty(value = "工程类型（国家标准）", position = 17)
    @NotNull(message = "工程类型（国家标准）不能为空")
    @Size(min = 1, max = 32, message = "工程类型（国家标准）不超过{max}个字符")
    private String countryProjectType;

    /**
     * 工程类型（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）", position = 18)
    @NotNull(message = "工程类型（总公司市场口径）不能为空")
    @Size(min = 1, max = 32, message = "工程类型（总公司市场口径）不超过20个字符")
    private String marketProjectType;


    /**
     * 工程类型(总公司综合口径)3
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)3", position = 22)
    @Size(max = 32, message = "工程类型（总公司综合口径）3的长度必须小于{max}")
    private String projectType3;

    /**
     * 工程类型(总公司综合口径)4
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)4", position = 23)
    @Size(max = 32, message = "工程类型（总公司综合口径）4的长度必须小于{max}")
    private String projectType4;

    /**
     * 项目附件信息对象
     */
    @ApiModelProperty(value = "项目附件信息对象", position = 24)
    private List<Map<Object, Object>> projectAttachment;

    /**
     * 总分包类别
     */
    @ApiModelProperty(value = "总分包类别", position = 25)
    @Size(max = 32, message = "总分包类别的长度必须小于{max}")
    private String totalSubcontractingCategory;

    /**
     * 结构形式
     */
    @ApiModelProperty(value = "结构形式", position = 26)
    @Size(max = 64, message = "结构形式的长度必须小于[64]")
    private String structuralStyle;

    /**
     * 结构形式2
     */
    @ApiModelProperty(value = "结构形式2", position = 27)
    @Size(max = 64, message = "结构形式2的长度必须小于{max}")
    private String structuralStyle2;

    /**
     * 是否有钢结构
     */
    @ApiModelProperty(value = "是否有钢结构", position = 28)
    @Size(max = 4, message = "是否有钢结构的长度必须小于{max}")
    private String includingSteel;

    /**
     * 最长桩基长度
     */
    @ApiModelProperty(value = "最长桩基长度", position = 29)
    @Size(max = 32, message = "最长桩基长度的长度必须小于{max}")
    private String projectMaxLength;

    /**
     * 最大桩径
     */
    @ApiModelProperty(value = "最大桩径", position = 30)
    @Size(max = 32, message = "最大桩径的长度必须小于{max}")
    private String projectMaxWidth;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型", position = 31)
    @Size(max = 64, message = "合同类型的长度必须小于{max}")
    private String contractType;

    /**
     * 是否装配式
     */
    @ApiModelProperty(value = "是否装配式", position = 32)
    @Size(max = 4, message = "是否装配式的长度必须小于{max}")
    private String fabricated;

    /**
     * 是否纳入公司考核指标
     */
    @ApiModelProperty(value = "是否纳入公司考核指标", position = 33)
    @Size(max = 2, message = "是否纳入公司考核指标的长度必须小于2")
    private String companyAssessmentIndicators;


    /**
     * 设计单位
     */
    @ApiModelProperty(value = "设计单位", position = 39)
    @Size(max = 255, message = "设计单位的长度必须小于{max}")
    private String designer;

    /**
     * 监理单位
     */
    @ApiModelProperty(value = "监理单位", position = 40)
    @Size(max = 255, message = "监理单位的长度必须小于{max}")
    private String supervisor;

    /**
     * 实际中标日期
     */
    @ApiModelProperty(value = "实际中标日期", position = 41)
    private Long successfulTime;

    /**
     * 实际签约日期
     */
    @ApiModelProperty(value = "实际签约日期", position = 42)
    private Long actualSignedTime;

    /**
     * 签约主体
     */
    @ApiModelProperty(value = "签约主体", position = 43)
    @Size(max = 255, message = "签约主体的长度必须小于255")
    private String signedSubjectValue;

    /**
     * 签约主体
     */
    @ApiModelProperty(value = "签约主体Code", position = 42)
    @Size(max = 100, message = "签约主体不超过{max}个字符")
    @NotBlank(message = "签约主体不能为空")
    private String signedSubjectCode;

    /**
     * 实施单位
     */
    @ApiModelProperty(value = "实施单位", position = 44)
    @Size(max = 255, message = "实施单位的长度必须小于{max}")
    private String doUnit;

    /**
     * 自行施工不含税金额
     */
    @ApiModelProperty(value = "自行施工不含税金额", position = 47)
    private BigDecimal midAmountSelf = BigDecimal.ZERO;

    /**
     * 土建不含税金额
     */
    @ApiModelProperty(value = "土建不含税金额", position = 48)
    private BigDecimal selfCivilAmount = BigDecimal.ZERO;

    /**
     * 安装不含税金额
     */
    @ApiModelProperty(value = "安装不含税金额", position = 49)
    private BigDecimal selfInstallAmount = BigDecimal.ZERO;

    /**
     * 钢结构不含税金额
     */
    @ApiModelProperty(value = "钢结构不含税金额", position = 50)
    private BigDecimal selfSteelStructureAmount = BigDecimal.ZERO;

    /**
     * 总包服务费
     */
    @ApiModelProperty(value = "总包服务费", position = 51)
    private BigDecimal selfTotalServiceAmount = BigDecimal.ZERO;

    /**
     * 其他
     */
    @ApiModelProperty(value = "其他", position = 52)
    private BigDecimal selfOtherAmount = BigDecimal.ZERO;

    /**
     * 暂列金额或甲指分包金额
     */
    @ApiModelProperty(value = "暂列金额或甲指分包金额", position = 53)
    private BigDecimal subcontractAmount = BigDecimal.ZERO;

    /**
     * 销项税额
     */
    @ApiModelProperty(value = "销项税额", position = 54)
    private BigDecimal projectTaxAmount = BigDecimal.ZERO;

    /**
     * 暂列金额工作内容
     */
    @ApiModelProperty(value = "暂列金额工作内容", position = 55)
    @Size(max = 1024, message = "暂列金额工作内容的长度必须小于1024")
    private String subcontractContent;

    /**
     * 中标项目经理
     */
    @ApiModelProperty(value = "中标项目经理", position = 56)
    @Size(max = 128, message = "中标项目经理的长度必须小于{max}")
    private String bidManager;

    /**
     * 中标项目经理注册证书编号
     */
    @ApiModelProperty(value = "中标项目经理注册证书编号", position = 57)
    @Size(max = 128, message = "中标项目经理注册证书编号的长度必须小于{max}")
    private String bidManagerCode;

    /**
     * 执行项目经理
     */
    @ApiModelProperty(value = "执行项目经理", position = 58)
    @Size(max = 128, message = "执行项目经理的长度必须小于{max}")
    private String excuteManager;

    /**
     * 执行项目经理联系方式
     */
    @ApiModelProperty(value = "执行项目经理联系方式", position = 59)
    @Size(max = 64, message = "执行项目经理联系方式的长度必须小于{max}")
    private String excuteManagerCode;

    /**
     * 合同项目经理
     */
    @ApiModelProperty(value = "合同项目经理", position = 60)
    @Size(max = 128, message = "合同项目经理的长度必须小于{max}")
    private String contractManager;

    /**
     * 合同项目经理注册证书编号
     */
    @ApiModelProperty(value = "合同项目经理注册证书编号", position = 61)
    @Size(max = 128, message = "合同项目经理注册证书编号的长度必须小于{max}")
    private String contractManagerCode;

    /**
     * 政府备案项目经理
     */
    @ApiModelProperty(value = "政府备案项目经理", position = 62)
    @Size(max = 128, message = "政府备案项目经理的长度必须小于{max}")
    private String governmentManager;

    /**
     * 政府备案项目经理注册证书编号
     */
    @ApiModelProperty(value = "政府备案项目经理注册证书编号", position = 63)
    @Size(max = 128, message = "政府备案项目经理注册证书编号的长度必须小于{max}")
    private String governmentManagerCode;

    /**
     * 承包模式
     */
    @ApiModelProperty(value = "承包模式", position = 64)
    @Size(max = 64, message = "承包模式的长度必须小于{max}")
    private String contractMode1;

    /**
     * 承包模式2
     */
    @ApiModelProperty(value = "承包模式2", position = 65)
    @Size(max = 64, message = "承包模式2的长度必须小于{max}")
    private String contractMode2;

    /**
     * 合同承包范围
     */
    @ApiModelProperty(value = "合同承包范围", position = 66)
    @Size(max = 2048, message = "合同承包范围的长度必须小于{max}")
    private String contractScope;

    /**
     * 发包人指定分包、独立分包的工程
     */
    @ApiModelProperty(value = "发包人指定分包、独立分包的工程", position = 67)
    @Size(max = 2048, message = "发包人指定分包、独立分包的工程的长度必须小于{max}")
    private String issuerProject;

    /**
     * 总工期（天）
     */
    @ApiModelProperty(value = "总工期（天）", position = 68)
    private Integer countDays;

    /**
     * 合同开工时间
     */
    @ApiModelProperty(value = "合同开工时间", position = 69)
    private Long workerBeginTime;

    /**
     * 合同竣工日期
     */
    @ApiModelProperty(value = "合同竣工日期", position = 70)
    private Long workerEndTime;

    /**
     * 实际开工日期
     */
    @ApiModelProperty(value = "实际开工日期", position = 71)
    private Long realWorkBeginTime;

    /**
     * 预计实际竣工日期
     */
    @ApiModelProperty(value = "预计实际竣工日期", position = 72)
    private Long predictWorkEndTime;

    /**
     * 工期奖罚类型
     */
    @ApiModelProperty(value = "工期奖罚类型", position = 73)
    @Size(max = 64, message = "工期奖罚类型的长度必须小于{max}")
    private String workerDateRewardPunish;

    /**
     * 工期奖罚条款
     */
    @ApiModelProperty(value = "工期奖罚条款", position = 74)
    @Size(max = 512, message = "工期奖罚条款的长度必须小于{max}")
    private String workerRewardPunishAppoint;

    /**
     * 合同范本类型
     */
    @ApiModelProperty(value = "合同范本类型", position = 75)
    @Size(max = 128, message = "合同范本类型的长度必须小于{max}")
    private String contractStyle;

    /**
     * 质量要求
     */
    @ApiModelProperty(value = "质量要求", position = 76)
    @Size(max = 64, message = "质量要求的长度必须小于{max}")
    private String qualityGuarantee;

    /**
     * 质量奖罚类型
     */
    @ApiModelProperty(value = "质量奖罚类型", position = 77)
    @Size(max = 64, message = "质量奖罚类型的长度必须小于{max}")
    private String rewardPunishType;

    /**
     * 质量奖罚条款
     */
    @ApiModelProperty(value = "质量奖罚条款", position = 78)
    @Size(max = 2048, message = "质量奖罚条款的长度必须小于{max}")
    private String rewardPunishTerms;

    /**
     * 安全文明施工要求
     */
    @ApiModelProperty(value = "安全文明施工要求", position = 79)
    @Size(max = 2048, message = "安全文明施工要求的长度必须小于{max}")
    private String safetyRequirement;

    /**
     * 安全文明施工奖罚条款
     */
    @ApiModelProperty(value = "安全文明施工奖罚条款", position = 80)
    @Size(max = 2048, message = "安全文明施工奖罚条款的长度必须小于{max}")
    private String safetyRewardPunishTerms;


    /**
     * 人工费是否可调
     */
    @ApiModelProperty(value = "人工费是否可调", position = 83)
    @Size(max = 2, message = "人工费是否可调的长度必须小于2")
    private String costOfLaborChange;

    /**
     * 人工费调差形式
     */
    @ApiModelProperty(value = "人工费调差形式", position = 84)
    @Size(max = 64, message = "人工费调差形式的长度必须小于{max}")
    private String changeWay;

    /**
     * 人工费调差条款
     */
    @ApiModelProperty(value = "人工费调差条款", position = 85)
    @Size(max = 2048, message = "人工费调差条款的长度必须小于{max}")
    private String terms;

    /**
     * 主材费是否可调
     */
    @ApiModelProperty(value = "主材费是否可调", position = 86)
    @Size(max = 2, message = "主材费是否可调的长度必须小于2")
    private String costOfLaborChange2;

    /**
     * 主材费调差幅度
     */
    @ApiModelProperty(value = "主材费调差幅度", position = 87)
    @Size(max = 15, message = "主材费调差幅度的长度必须小于15")
    private String changeRate2;

    /**
     * 主材费调差条款
     */
    @ApiModelProperty(value = "主材费调差条款", position = 88)
    @Size(max = 2048, message = "主材费调差条款的长度必须小于{max}}")
    private String terms2;

    /**
     * 是否有预付款
     */
    @ApiModelProperty(value = "是否有预付款", position = 89)
    @Size(max = 2, message = "是否有预付款的长度必须小于2")
    private String advancesFlag;

    /**
     * 进度款付款方式
     */
    @ApiModelProperty(value = "进度款付款方式", position = 90)
    @Size(max = 64, message = "进度款付款方式的长度必须小于{max}")
    private String advancesWay;

    /**
     * 竣工验收支付比例
     */
    @ApiModelProperty(value = "竣工验收支付比例", position = 91)
    @Size(max = 16, message = "竣工验收支付比例的长度必须小于{max}")
    private String completedRate;

    /**
     * 竣工验收收款周期（月）
     */
    @ApiModelProperty(value = "竣工验收收款周期（月）", position = 92)
    @Size(max = 64, message = "竣工验收收款周期（月）的长度必须小于{max}")
    private String completedCycle;

    /**
     * 结算支付比例
     */
    @ApiModelProperty(value = "结算支付比例", position = 93)
    @Size(max = 20, message = "结算支付比例的长度必须小于{max}")
    private String settlementRate;

    /**
     * 结算周期（月）
     */
    @ApiModelProperty(value = "结算周期（月）", position = 94)
    @Size(max = 64, message = "结算周期（月）的长度必须小于{max}")
    private String settlementCycle;

    /**
     * 保修金
     */
    @ApiModelProperty(value = "保修金", position = 95)
    @Size(max = 22, message = "保修金的长度必须小于{max}")
    private String warrantyPremium;

    /**
     * 保修金比例
     */
    @ApiModelProperty(value = "保修金比例", position = 96)
    @Size(max = 50, message = "保修金比例的长度必须小于{max}")
    private String warrantyPremiumRate;

    /**
     * 保修金支付方式
     */
    @ApiModelProperty(value = "保修金支付方式", position = 97)
    @Size(max = 128, message = "保修金支付方式的长度必须小于{max}")
    private String warrantyPremiumWay;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式", position = 98)
    @Size(max = 128, message = "支付方式的长度必须小于{max}")
    private String payTypeNew;

    /**
     * 非现金支付方式
     */
    @ApiModelProperty(value = "非现金支付方式", position = 99)
    @Size(max = 128, message = "非现金支付方式的长度必须小于{max}")
    private String noCashPayWay;

    /**
     * 是否垫资
     */
    @ApiModelProperty(value = "是否垫资", position = 100)
    @Size(max = 2, message = "是否垫资的长度必须小于2")
    private String advancesFundFlag;

    /**
     * 履约担保方式
     */
    @ApiModelProperty(value = "履约担保方式", position = 101)
    @Size(max = 64, message = "履约担保方式的长度必须小于{max}")
    private String guaranteeWay;

    /**
     * 项目及土地是否合法
     */
    @ApiModelProperty(value = "项目及土地是否合法", position = 102)
    @Size(max = 2, message = "项目及土地是否合法的长度必须小于2")
    private String landLegalityFlag;

    /**
     * 是否放弃优先受偿权
     */
    @ApiModelProperty(value = "是否放弃优先受偿权", position = 103)
    @Size(max = 2, message = "是否放弃优先受偿权的长度必须小于2")
    private String giveUpCompensateFlag;

    /**
     * 付款比例是否低于百分之八十
     */
    @ApiModelProperty(value = "付款比例是否低于百分之八十", position = 104)
    @Size(max = 4, message = "付款比例是否低于百分之八十的长度必须小于4")
    private String payRateLessEightyFlag;

    /**
     * 支付节点时间是否超过2个月
     */
    @ApiModelProperty(value = "支付节点时间是否超过2个月", position = 105)
    @Size(max = 4, message = "支付节点时间是否超过2个月的长度必须小于4")
    private String nodeMoreTwoMonthFlag;

    /**
     * 商票比例
     */
    @ApiModelProperty(value = "商票比例", position = 106)
    @Size(max = 22, message = "商票比例的长度必须小于{max}")
    private String commercialTicketProportion;

    /**
     * 重大风险化解措施
     */
    @ApiModelProperty(value = "重大风险化解措施", position = 107)
    @Size(max = 2048, message = "重大风险化解措施的长度必须小于{max}")
    private String bigRiskMeasure;

    /**
     * 交底日期
     */
    @ApiModelProperty(value = "交底日期", position = 108)
    private Long presentationTime;

    /**
     * 交底人
     */
    @ApiModelProperty(value = "交底人", position = 109)
    @Size(max = 255, message = "交底人的长度必须小于{max}")
    private String presentationUser;

    /**
     * 接收人
     */
    @ApiModelProperty(value = "接收人", position = 110)
    @Size(max = 512, message = "接收人的长度必须小于{max}")
    private String recipient;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", position = 111)
    @Size(max = 2048, message = "备注的长度必须小于{max}")
    private String remark;

    /**
     * 突破底线条款
     */
    @ApiModelProperty(value = "突破底线条款", position = 112)
    private String breakBottom;

    /**
     * 独立文件id
     */
    @ApiModelProperty(value = "独立文件id", position = 113)
    @NotNull(message = "独立文件id不能为空")
    private Long originFileId;

    /**
     * 接口幂等性校验字段
     */
    @NotNull(message = "接口幂等性校验字段不能为空")
    @ApiModelProperty(value = "所属源文件ID，接口幂等性校验字段", position = 114)
    private Long belongId;

    /**
     * 执行单位id
     */
    @ApiModelProperty(value = "执行单位id", position = 115)
    @Size(max = 200, message = "执行单位id的长度必须小于128")
    private String executeUnitId;

    @ApiModelProperty(value = "执行单位简称", position = 115)
    @Size(max = 200, message = "执行单位简称长度不超过{max}个字符")
    private String executeUnitAbbreviation;

    /**
     * 执行单位code
     */
    @ApiModelProperty(value = "执行单位code", position = 116)
    @Size(max = 128, message = "执行单位id的长度必须小于128")
    private String executeUnitCode;

    /**
     * 执行单位在标准组织的idPath
     */
    @ApiModelProperty(value = "执行单位在标准组织的idPath", position = 117)
    @Size(max = 1024, message = "执行单位在标准组织的idPath不超过1024个字符")
    private String executeUnitIdPath;

    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别", position = 118)
    @Size(max = 50, message = "客户级别不超过50个字符")
    private String customerLevel;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件", position = 119)
    @Size(max = 1024, message = "附件不超过1024个字符")
    private String attach;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", position = 120)
    @NotNull(message = "业务类型不能为空")
    @Size(min = 1, max = 32, message = "业务类型长度要求[{min},{max}]")
    private String businessType;


}
