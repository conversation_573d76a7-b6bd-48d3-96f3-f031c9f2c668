package com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 项目合同信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Data
@TableName("d_project_contract_info")
@ApiModel(value = "ProjectContractInfo对象", description = "项目合同信息表")
public class ProjectContractInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 与基础信息表关联
     */
    @ApiModelProperty(value = "与基础信息表关联")
    private Integer projectId;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称")
    private String contractName;

    /**
     * 合同金额，单位万元
     */
    @ApiModelProperty(value = "合同金额，单位万元")
    private String contractAmount;

    /**
     * 是否局内投资主体
     */
    @ApiModelProperty(value = "是否局内投资主体")
    private Byte contractIsAdministrationMainBody;

    /**
     * 投资主体
     */
    @ApiModelProperty(value = "投资主体")
    private String contractMainBody;

    /**
     * 签约单位
     */
    @ApiModelProperty(value = "签约单位")
    private String contractSignedCompany;

    /**
     * 合同开工日期
     */
    @ApiModelProperty(value = "合同开工日期")
    private Date startTime;

    /**
     * 合同竣工日期
     */
    @ApiModelProperty(value = "合同竣工日期")
    private Date finishTime;

    /**
     * 合同日历天数
     */
    @ApiModelProperty(value = "合同日历天数")
    private Integer totalRange;

    /**
     * 工期奖罚类型
     */
    @ApiModelProperty(value = "工期奖罚类型")
    private String contractRuleType;

    /**
     * 工期奖罚条款
     */
    @ApiModelProperty(value = "工期奖罚条款")
    private String contractRuleDetail;

    /**
     * 里程碑节点，json结构，包含时间事项
     */
    @ApiModelProperty(value = "里程碑节点，json结构，包含时间事项")
    private String contractMilestone;

    /**
     * 合同项目经理
     */
    @ApiModelProperty(value = "合同项目经理")
    private String contractPm;

    /**
     * 合同项目经理UID
     */
    @ApiModelProperty(value = "合同项目经理UID")
    private String contractPmUid;


    /**
     * 合同项目经理电话，加密
     */
    @ApiModelProperty(value = "合同项目经理电话，加密")
    private String contractTelephone;

    /**
     * 执行项目经理
     */
    @ApiModelProperty(value = "执行项目经理")
    private String operatorPm;


    /**
     * 执行项目经理UID
     */
    @ApiModelProperty(value = "执行项目经理UID")
    private String operatorPmUid;

    /**
     * 执行项目经理电话，加密
     */
    @ApiModelProperty(value = "执行项目经理电话，加密")
    private String operatorTelephone;

    /**
     * 中标项目经理
     */
    @ApiModelProperty(value = "中标项目经理")
    private String bidPm;

    /**
     * 中标项目经理电话，加密
     */
    @ApiModelProperty(value = "中标项目经理电话，加密")
    private String bidTelephone;

    /**
     * 政府备案项目经理
     */
    @ApiModelProperty(value = "政府备案项目经理")
    private String govPm;

    /**
     * 政府备案项目经理电话，加密
     */
    @ApiModelProperty(value = "政府备案项目经理电话，加密")
    private String govTelephone;

    /**
     * 质量目标
     */
    @ApiModelProperty(value = "质量目标")
    private String qualityTask;

    /**
     * 是否创优
     */
    @ApiModelProperty(value = "是否创优")
    private Byte qualityIsGood;

    /**
     * 创优所在地
     */
    @ApiModelProperty(value = "创优所在地")
    private String qualityLocation;

    /**
     * 创优目标
     */
    @ApiModelProperty(value = "创优目标")
    private String qualityGoal;

    /**
     * 创优奖项
     */
    @ApiModelProperty(value = "创优奖项")
    private String qualityAward;

    /**
     * 奖罚类型
     */
    @ApiModelProperty(value = "奖罚类型")
    private String qualityRuleType;

    /**
     * 奖罚条款
     */
    @ApiModelProperty(value = "奖罚条款")
    private String qualityRuleDetail;

    /**
     * 安全目标
     */
    @ApiModelProperty(value = "安全目标")
    private String securityTask;

    /**
     * 是否创优
     */
    @ApiModelProperty(value = "是否创优")
    private Byte securityIsGood;

    /**
     * 创优所在地
     */
    @ApiModelProperty(value = "创优所在地")
    private String securityLocation;

    /**
     * 创优目标
     */
    @ApiModelProperty(value = "创优目标")
    private String securityGoal;

    /**
     * 创优奖项
     */
    @ApiModelProperty(value = "创优奖项")
    private String securityAward;

    /**
     * 奖罚类型
     */
    @ApiModelProperty(value = "奖罚类型")
    private String securityRuleType;

    /**
     * 奖罚条款
     */
    @ApiModelProperty(value = "奖罚条款")
    private String securityRuleDetail;

    /**
     * 环境目标
     */
    @ApiModelProperty(value = "环境目标")
    private String environmentTask;

    /**
     * 是否创优
     */
    @ApiModelProperty(value = "是否创优")
    private Byte environmentIsGood;

    /**
     * 创优所在地
     */
    @ApiModelProperty(value = "创优所在地")
    private String environmentLocation;

    /**
     * 创优目标
     */
    @ApiModelProperty(value = "创优目标")
    private String environmentGoal;

    /**
     * 创优奖项
     */
    @ApiModelProperty(value = "创优奖项")
    private String environmentAward;

    /**
     * 奖罚类型
     */
    @ApiModelProperty(value = "奖罚类型")
    private String environmentRuleType;

    /**
     * 奖罚条款
     */
    @ApiModelProperty(value = "奖罚条款")
    private String environmentRuleDetail;

    /**
     * 审批通过的信息集合
     */
    @ApiModelProperty(value = "审批通过的信息集合")
    private String displayInfo;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

}
