package com.cscec3b.iti.projectmanagement.api.dto.request;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;

/**
 * 局内补充协议
 *
 * <AUTHOR>
 * @Description
 * @Date 2022/10/20 16:11
 */

@Data
@Accessors(chain = true)
@ApiModel(value = "BureauSupplementaryAgreementReq", description = "局内补充协议")
public class BureauSupplementaryAgreementReq {

    /**
     * 发起人单位
     */
    @ApiModelProperty(value = "发起人", position = 1)
    @Size(max = 50, message = "发起人的长度必须小于50")
    private String submitPerson;

    /**
     * 补充协议编号
     */
    @ApiModelProperty(value = "补充协议编号", position = 2)
    @Size(max = 32, message = "补充协议编号的长度必须小于32")
    private String agreementCode;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称", position = 3)
    @Size(max = 128, message = "工程名称的长度必须小于128")
    private String projectName;

    /**
     * 工程属地
     */
    @ApiModelProperty(value = "工程属地", position = 4)
    @Size(max = 128, message = "工程属地的长度必须小于{max}")
    @NotNull(message = "工程属地不能为空")
    private String projectBelong;

    /**
     * 是否局重点项目
     */
    @ApiModelProperty(value = "是否局重点项目", position = 5)
    @Size(max = 2, message = "是否局重点项目的长度必须小于2")
    private String bureauProject;

    /**
     * 是否授权外
     */
    @ApiModelProperty(value = "是否授权外", position = 6)
    @Size(max = 2, message = "是否授权外的长度必须小于2")
    private String mandateForeign;

    /**
     * 客户名称
     */
    @ApiModelProperty(value = "客户名称", position = 7)
    @NotNull(message = "客户名称不能为空")
    @Size(min = 1, max = 255, message = "业客户名称不超过{max}个字符")
    private String customerName;

    /**
     * 客户母公司
     */
    @ApiModelProperty(value = "客户母公司", position = 8)
    @Size(max = 255, message = "客户母公司的长度必须小于{max}}")
    private String superiorCompanyName;

    /**
     * 客户企业性质
     */
    @ApiModelProperty(value = "客户企业性质", position = 9)
    @NotNull(message = "客户企业性质不能为空")
    @Size(min = 1, max = 255, message = "客户企业性质不超过{max}个字符")
    private String enterpriseType;

    /**
     * 建设单位（甲方）联系人
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人", position = 10)
    @Size(max = 255, message = "建设单位（甲方）联系人的长度必须小于{max}")
    private String contactPerson;

    /**
     * 建设单位（甲方）联系人电话
     */
    @ApiModelProperty(value = "建设单位（甲方）联系人电话", position = 11)
    @Size(max = 64, message = "建设单位（甲方）联系人电话的长度必须小于64")
    private String contactPersonMobile;

    /**
     * 合同经办人
     */
    @ApiModelProperty(value = "合同经办人", position = 12)
    @Size(max = 64, message = "合同经办人的长度必须小于64")
    private String contractResponsiblePerson;

    /**
     * 是否纳入公司考核指标
     */
    @ApiModelProperty(value = "是否纳入公司考核指标", position = 13)
    @Size(max = 2, message = "是否纳入公司考核指标的长度必须小于2")
    private String companyAssessmentIndicators;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", position = 14)
    @NotNull(message = "务类型不能为空")
    @Size(min = 1, max = 32, message = "业务类型不超过{max}个字符")
    private String businessType;

    /**
     * 补充协议金额
     */
    @ApiModelProperty(value = "补充协议金额 ", position = 15)
    private BigDecimal supplementAmount = BigDecimal.ZERO;

    /**
     * 计价方式
     */
    @ApiModelProperty(value = "计价方式", position = 16)
    @Size(max = 64, message = "计价方式的长度必须小于{max}}")
    private String pricingMethod;

    /**
     * 合同形式
     */
    @ApiModelProperty(value = "合同形式", position = 17)
    @Size(max = 64, message = "合同形式的长度必须小于{max}")
    private String contractForm;

    /**
     * 人工费是否可调
     */
    @ApiModelProperty(value = "人工费是否可调", position = 18)
    @Size(max = 2, message = "人工费是否可调的长度必须小于2")
    private String costOfLaborChange;

    /**
     * 主材费是否可调
     */
    @ApiModelProperty(value = "主材费是否可调", position = 19)
    @Size(max = 2, message = "主材费是否可调的长度必须小于2")
    private String costOfLaborChange2;

    /**
     * 是否有预付款
     */
    @ApiModelProperty(value = "是否有预付款", position = 20)
    @Size(max = 2, message = "是否有预付款的长度必须小于2")
    private String advancesFlag;

    /**
     * 付款方式
     */
    @ApiModelProperty(value = "付款方式", position = 21)
    @Size(max = 64, message = "付款方式的长度必须小于{max}")
    private String advancesWay;

    /**
     * 月进度付款比例
     */
    @ApiModelProperty(value = "月进度付款比例", position = 22)
    @Size(max = 64, message = "月进度付款比例的长度必须小于{max}")
    private String advancesMonthRate;

    /**
     * 竣工验收支付比例
     */
    @ApiModelProperty(value = "竣工验收支付比例", position = 23)
    @Size(max = 16, message = "竣工验收支付比例的长度必须小于{max}")
    private String completedRate;

    /**
     * 竣工验收周期（月）
     */
    @ApiModelProperty(value = "竣工验收周期（月）", position = 24)
    @Size(max = 64, message = "竣工验收周期（月）的长度必须小于{max}")
    private String completedCycle;

    /**
     * 结算支付比例
     */
    @ApiModelProperty(value = "结算支付比例", position = 25)
    @Size(max = 20, message = "结算支付比例的长度必须小于20")
    private String settlementRate;

    /**
     * 结算周期（月）
     */
    @ApiModelProperty(value = "结算周期（月）", position = 26)
    @Size(max = 64, message = "结算周期（月）的长度必须小于{max}")
    private String settlementCycle;

    /**
     * 保修金
     */
    @ApiModelProperty(value = "保修金", position = 27)
    @Size(max = 22, message = "保修金的长度必须小于{max}")
    private String warrantyPremium;

    /**
     * 保修金比例
     */
    @ApiModelProperty(value = "保修金比例", position = 28)
    @Size(max = 50, message = "保修金比例的长度必须小于{max}")
    private String warrantyPremiumRate;

    /**
     * 保修金支付方式
     */
    @ApiModelProperty(value = "保修金支付方式", position = 29)
    @Size(max = 128, message = "保修金支付方式的长度必须小于{max}")
    private String warrantyPremiumWay;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式", position = 30)
    @Size(max = 128, message = "支付方式的长度必须小于{max}")
    private String payTypeNew;

    /**
     * 现金支付方式
     */
    @ApiModelProperty(value = "现金支付方式", position = 31)
    @Size(max = 128, message = "现金支付方式的长度必须小于{max}")
    private String specificPayWay;

    /**
     * 是否垫资
     */
    @ApiModelProperty(value = "是否垫资", position = 32)
    @Size(max = 2, message = "是否垫资的长度必须小于2")
    private String advancesFundFlag;

    /**
     * 履约担保方式
     */
    @ApiModelProperty(value = "履约担保方式", position = 33)
    @Size(max = 64, message = "履约担保方式的长度必须小于{max}")
    private String guaranteeWay;

    /**
     * 补充协议文本
     */
    @ApiModelProperty(value = "补充协议文本", position = 34)
    private String agreementUrl;

    /**
     * 合同主要条款对比表
     */
    @ApiModelProperty(value = "合同主要条款对比表", position = 35)
    private String contractTermUrl;

    /**
     * 法律意见书
     */
    @ApiModelProperty(value = "法律意见书", position = 36)
    private String lawUrl;

    /**
     * 接口幂等性校验字段
     */
    @NotNull(message = "接口幂等性校验字段不能为空")
    @ApiModelProperty(value = "所属源文件ID，接口幂等性校验字段", position = 38)
    private Long belongId;

    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别", position = 39)
    @Size(max = 50, message = "客户级别不超过50个字符")
    private String customerLevel;

    /**
     * 突破底线条款
     */
    @ApiModelProperty(value = "突破底线条款", position = 40)
    private String breakBottom;

    /**
     * 补充协议编号
     */
    @ApiModelProperty(value = "协议编号(agreementCode)", position = 41)
    @Size(max = 100, message = "协议编号(agreementCode)不超过100个字符")
    private String bureauSupplementaryAgreementCode;

    /**
     * 签约主体
     */
    @ApiModelProperty(value = "签约主体", position = 42)
    @Size(max = 255, message = "监理单位不超过{max}个字符")
    private String signedSubjectValue;

    /**
     * 签约主体Code
     */
    @ApiModelProperty(value = "签约主体", position = 42)
    @Size(max = 100, message = "签约主体Code的长度必须小于{max}")
    private String signedSubjectCode;

    /**
     * 业务类型Code
     */
    @TableField(value = "business_type_code")
    @ApiModelProperty(value = "业务类型Code")
    private String businessTypeCode;

    /**
     * 客户级别Code
     */
    @TableField(value = "customer_level_code")
    @ApiModelProperty(value = "客户级别Code")
    private String customerLevelCode;

    /**
     * 客户企业性质Code
     */
    @TableField(value = "enterprise_type_code")
    @ApiModelProperty(value = "客户企业性质Code")
    private String enterpriseTypeCode;


    /**
     * 客户id
     */
    @ApiModelProperty(value = "客户ID ")
    private String customerId;

}
