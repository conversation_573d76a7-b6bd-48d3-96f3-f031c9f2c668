package com.cscec3b.iti.projectmanagement.api.dto.request.project;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @description BureauNominalPageParams
 * @date 2023/07/31 10:23
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "BureauNominalPageParams", description = "局名义项目列表查询参数")
public class BureauNominalPageParams extends BasePage {

    @ApiModelProperty("idPath")
    private String idPath;

    @ApiModelProperty("云枢执行单位IdPath")
//    @NotBlank(message = "云枢执行单位IdPath不允许为空")
    private String yunshuExecuteUnitIdPath;

    @ApiModelProperty("项目名称(财商返回)")
    private String projectFinanceName;

    @ApiModelProperty("项目编码(财商返回)")
    private String projectFinanceCode;

    @ApiModelProperty("是否为局名义分包项目，(0:非局名义、1:局名义总包、2:局名义分包")
    private Integer bureauNominalProjectType;

    @ApiModelProperty("总包项目名称")
    private String generalContractingProject;

    @ApiModelProperty("项目标识")
    private String cpmProjectKey;

}
