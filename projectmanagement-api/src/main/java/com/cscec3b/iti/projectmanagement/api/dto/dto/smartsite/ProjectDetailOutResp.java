package com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite;

import java.io.Serializable;
import java.util.List;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 智慧工地项目详情信息
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Data
@Accessors(chain = true)
public class ProjectDetailOutResp implements Serializable {

    private ProjectBasicInfo projectBasicInfo;

    private ProjectContractInfo projectContractInfo;

    private ProjectContactInfo projectContactInfo;

    private ProjectAgreementInfo projectAgreementInfo;

    private ProjectInfo projectInfo;

    private ProjectRelyInfo projectRelyInfo;

    private ProjectHookInfo projectHookInfo;

    private String hookUser;

    private Integer hookNum;

    private List<HookDto> hookDTOList;

    /**
     * 挂接项目类型
     */
    private String hookProjectType = "非在途";
}
