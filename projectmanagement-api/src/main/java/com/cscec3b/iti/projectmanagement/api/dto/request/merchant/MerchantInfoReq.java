package com.cscec3b.iti.projectmanagement.api.dto.request.merchant;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
@ApiModel(value = "MerchantInfoReq", description = "客商信息对象请求体")
public class MerchantInfoReq extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 客商名称
     */
    @ApiModelProperty(value = "客商名称")
    private String merchantName;

    /**
     * 客商编码
     */
    @ApiModelProperty(value = "客商编码")
    private String merchantCode;

    /**
     * 是否启用客户
     */
    @ApiModelProperty(value = "是否启用客户")
    private String merchantEnable;

    /**
     * 客商类型
     */
    @ApiModelProperty(value = "客商类型")
    private String type;

    /**
     * 是否封存
     */
    @ApiModelProperty(value = "是否封存")
    private String freeze;

    /**
     * 客户性质
     */
    @ApiModelProperty(value = "客户性质")
    private String merchantProperty;

    /**
     * 关联人员编码
     */
    @ApiModelProperty(value = "关联人员编码")
    private String relateUserCode;

    /**
     * 关联公司编码
     */
    @ApiModelProperty(value = "关联公司编码")
    private String relateCompanyCode;

    /**
     * 供应商性质
     */
    @ApiModelProperty(value = "供应商性质")
    private String supplierProperty;

    /**
     * 是否启用供应商
     */
    @ApiModelProperty(value = "是否启用供应商")
    private String supplierEnable;

    /**
     * 行政区域
     */
    @ApiModelProperty(value = "行政区域")
    private String region;

    /**
     * 客商简称
     */
    @ApiModelProperty(value = "客商简称")
    private String merchantAbbreviation;

    /**
     * 统一社会信用代码
     */
    @ApiModelProperty(value = "统一社会信用代码")
    private String unifiedSocialCode;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Long createAt;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    private Long updateAt;

    /**
     * 是否删除：0： 未删除； 时间戳为删除时间
     */
    @ApiModelProperty(value = "是否删除：0： 未删除； 时间戳为删除时间")
    private Long deleted;
}
