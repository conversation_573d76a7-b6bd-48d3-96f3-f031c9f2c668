package com.cscec3b.iti.projectmanagement.api.dto.request;

import java.io.Serializable;

import javax.validation.constraints.NotNull;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "DOP 组织创建请求参数")
public class OrgCreateReq implements Serializable {

    /**
     * 组织名称
     */
    @NotNull(message = "departmentName cannot null")
    @ApiModelProperty(value = "组织名称")
    private String departmentName;

    /**
     * 组织简称
     */
    @ApiModelProperty(value = "组织简称")
    private String departmentAbbreviation = departmentName;

    /**
     * 上级组织节点id
     */
    @NotNull(message = "treeParentId cannot null")
    @ApiModelProperty(value = "上级组织节点id")
    private String treeParentId;

    /**
     * 集团主数据编码
     */
    @ApiModelProperty(value = "集团主数据编码")
    private String companyOrgCode;

    /**
     * 创建用户 ID
     */
    @ApiModelProperty(value = "创建用户 ID")
    private String createUserId;

    /**
     * 组织类型：只能传空，60、70
     */
    private String orgGrouping;

    /**
     * 组织编码
     */
    @ApiModelProperty(value = "组织编码")
    private String departmentCode;
}
