package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import com.cscec3b.iti.projectmanagement.api.dto.dto.project.ProjectAmountDetailDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@ApiModel("项目金额分类响应")
public class ProjectAmountCategoryResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 正式合同类
     */
    @ApiModelProperty("正式合同分类明细")
    private List<ProjectAmountDetailDto> formalContractList = new ArrayList<>();

    /**
     * 正式合同金额
     */
    @ApiModelProperty("正式合同金额")
    private BigDecimal formalContractAmount = BigDecimal.ZERO;

    /**
     * 暂定合同分类
     */
    @ApiModelProperty("暂定合同分类明细")
    private List<ProjectAmountDetailDto> tentativeContractList = new ArrayList<>();

    /**
     * 暂定合同金额
     */
    @ApiModelProperty("暂定合同金额")
    private BigDecimal tentativeContractAmount = BigDecimal.ZERO;

}
