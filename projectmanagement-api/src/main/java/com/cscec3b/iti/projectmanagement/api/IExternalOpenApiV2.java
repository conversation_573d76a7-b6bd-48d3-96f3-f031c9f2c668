package com.cscec3b.iti.projectmanagement.api;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Size;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.v2.ProjectOpenByFileReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.v2.ProjectOpenEventNoticeReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.v2.ProjectOpenHookQueryReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.v2.ProjectOpenReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.v2.ProjectEventCallBackResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.v2.ProjectOpenByFileResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.v2.ProjectOpenHookQueryResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.v2.ProjectOpenResp;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 对外开放API Version2.0
 * <AUTHOR>
 * @description 对外开放API Version2.0
 * @date 2023/7/28
 */
public interface IExternalOpenApiV2 {
    /**
     * 路径
     */
    String PATH = "/api/v2";

    /**
     * 项目对外信息获取API
     * @param projectOpenReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/external/open/project")
    @ApiOperation(value = "项目对外信息获取API", notes = "项目中心对外分发项目信息")
    GenericityResponse<List<ProjectOpenResp>> externalOpenProject(
            @Validated @ApiParam(value = "项目对外请求数据", required = true) @RequestBody ProjectOpenReq projectOpenReq);

    /**
     * 通过合同信息反查项目信息
     * @param openByFileReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/external/open/project-by-files")
    @ApiOperation(value = "通过合同信息反查项目信息", notes = "通过合同信息反查项目信息")
    GenericityResponse<List<ProjectOpenByFileResp>> externalOpenProjectByFiles(
        @ApiParam(value = "项目对外请求数据", required = true) @Size(min = 1, max = 100, message = "合同信息不能为空，且最多[{max}条]")
        @Validated @Valid @RequestBody List<ProjectOpenByFileReq> openByFileReq);

    /**
     * 事件回调接口
     * @param projectId
     * @param eventId
     * @param eventCode
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @GetMapping("/external/open/id")
    @ApiOperation(value = "事件回调接口", notes = "供外部系统根据事件ide及项目id查询项目信息, eventCode必填")
    GenericityResponse<ProjectEventCallBackResp> externalOpenByProjectId(
        @ApiParam(value = "项目id", required = true) @RequestParam("projectId") int projectId,
            @ApiParam(value = "事件id", required = false) @RequestParam(value = "eventId", required = false) Integer eventId,
            @ApiParam(value = "事件Code", required = false) @RequestParam(value = "eventCode", required = false) String eventCode);

    /**
     * 接收外部系统事件通知
     * @param noticeReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/external/open/event/notice")
    @ApiOperation(value = "接收外部系统事件通知", notes = "接收外部系统的事件通知信息")
    GenericityResponse<Boolean>
        eventNotice(@ApiParam(value = "事件通知信息") @Validated @RequestBody ProjectOpenEventNoticeReq noticeReq);

    /**
     * 市场营销中标未立项文件挂接项目列表
     * @param hookReqReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/external/open/hook/page")
    @ApiOperation(value = "市场营销中标未立项文件挂接项目列表", notes = "市场营销中标未立项文件挂接项目列表,条件为 财商立项完成，非特殊立项")
    GenericityResponse<Page<ProjectOpenHookQueryResp>>
        hookProjectPageList(@ApiParam(value = "查询条件") @RequestBody ProjectOpenHookQueryReq hookReqReq);
}
