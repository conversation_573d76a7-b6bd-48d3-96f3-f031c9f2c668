package com.cscec3b.iti.projectmanagement.api.dto.request.open;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import com.cscec3b.iti.projectmanagement.api.validation.annotation.MultiFieldCheck;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("项目档案分页查询参数")
@MultiFieldCheck.Rules({
        @MultiFieldCheck(message = "精准匹配模式下，一次只能传入一个有效参数(cpmProjectKey)，其他参数必须为空",
                conditional = "!#cpmProjectKey.empty",
                checkRule = "#businessSegmentCodePath.empty && #independentContractNo.empty && #executeUnitId.empty " +
                        "&& #financeProjectCode.empty"),
        @MultiFieldCheck(message = "精准匹配模式下，一次只能传入一个有效参数(businessSegmentCodePath)，其他参数必须为空",
                conditional = "!#businessSegmentCodePath.empty",
                checkRule = "#cpmProjectKey.empty && #independentContractNo.empty && #executeUnitId.empty" +
                        "&& #financeProjectCode.empty"),
        @MultiFieldCheck(message = "精准匹配模式下，一次只能传入一个有效参数(independentContractNo)，其他参数必须为空",
                conditional = "!#independentContractNo.empty",
                checkRule = "#cpmProjectKey.empty && #businessSegmentCodePath.empty && #executeUnitId.empty" +
                        "&& #financeProjectCode.empty"),
        @MultiFieldCheck(message = "精准匹配模式下，一次只能传入一个有效参数(executeUnitId)，其他参数必须为空",
                conditional = "!#executeUnitId.empty",
                checkRule = "#cpmProjectKey.empty && #independentContractNo.empty && #independentContractNo.empty" +
                        "&& #financeProjectCode.empty"),
        @MultiFieldCheck(message = "精准匹配模式下，一次只能传入一个有效参数(financeProjectCode)，其他参数必须为空",
                conditional = "!#financeProjectCode.empty",
                checkRule = "#cpmProjectKey.empty && #independentContractNo.empty && #independentContractNo.empty" +
                        "&& #executeUnitId.empty"),
        @MultiFieldCheck(message = "模糊匹配模式下，只接受cpmProjectName 和 executeUnitName参数，其他参数必须为空",
                conditional = "#cpmProjectName != null || #executeUnitName != null",
                checkRule = "#cpmProjectKey.empty && #independentContractNo.empty && #independentContractNo.empty" +
                        "&& #executeUnitId.empty"),
        @MultiFieldCheck(message = "当前api单次最大支持100条数据查询", checkRule = "#size <= 100")
})
public class OpenProjectArchiveReq extends BasePage implements Serializable {

    /**
     * 项目标识
     */
    @ApiModelProperty(value = "项目标识", notes = "不支持模糊查询")
    private List<String> cpmProjectKey = new ArrayList<>();

    /**
     * 财商业务板块codePath
     */
    @ApiModelProperty(value = "财商业务板块codePath", example = "['03/0304/030407','03/0304']", notes = "不支持模糊查询")
    private List<String> businessSegmentCodePath = new ArrayList<>();

    /**
     * 独立立项文件编号
     */
    @ApiModelProperty(value = "独立立项文件编号", notes = "不支持模糊查询")
    private List<String> independentContractNo = new ArrayList<>();

    /**
     * 执行单位Id
     */
    @ApiModelProperty(value = "执行单位Id", notes = "不支持模糊查询")
    private List<String> executeUnitId = new ArrayList<>();

    /**
     * 财商项目编码
     * projectFinanceCode
     */
    @ApiModelProperty(value = "财商项目编码", notes = "不支持模糊查询")
    private List<String> financeProjectCode = new ArrayList<>();


    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称", notes = "支持模糊匹配")
    private String cpmProjectName;


    /**
     * 执行单位名称
     */
    @ApiModelProperty(value = "执行单位名称", notes = "支持模糊匹配")
    private String executeUnitName;
}
