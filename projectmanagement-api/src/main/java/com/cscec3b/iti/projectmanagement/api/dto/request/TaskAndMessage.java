package com.cscec3b.iti.projectmanagement.api.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

@Data
@ApiModel(value = "任务转交请求参数")
public class TaskAndMessage implements Serializable {

    /**
     * 任务代码
     */
    @ApiModelProperty(value = "任务代码", required = true)
    @NotBlank(message = "任务代码不能为空")
    private String billId;

    /**
     * 用户codes
     */
    @ApiModelProperty(value = "用户codes", required = true)
    @NotEmpty(message = "用户codes不能为空")
    private List<UserCodes> userCodes;

    @Data
    public static class UserCodes {
        private String id;
        private String name;
    }
}
