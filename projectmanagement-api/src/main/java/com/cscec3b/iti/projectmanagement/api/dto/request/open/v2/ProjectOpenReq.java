
package com.cscec3b.iti.projectmanagement.api.dto.request.open.v2;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @description 项目对外请求数据
 * <AUTHOR>
 * @date 2022/11/03
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectOpenReq_v2.0", description = "项目对外请求数据")
public class ProjectOpenReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "A8项目编码")
    @JsonProperty(value = "projectCodeInA8")
    private String a8ProjectCode;

    @ApiModelProperty(value = "云枢组织id")
    @JsonProperty(value = "projectOrgId")
    private String yunshuOrgId;

    @ApiModelProperty(value = "财商项目编码")
    @JsonProperty(value = "projectCode")
    private String projectFinanceCode;

    @ApiModelProperty(value = "项目部标准组织编码")
    private String projectDeptId;

    @ApiModelProperty(value = "项目部的组织路径")
    private String projectDeptIdPath;
}
