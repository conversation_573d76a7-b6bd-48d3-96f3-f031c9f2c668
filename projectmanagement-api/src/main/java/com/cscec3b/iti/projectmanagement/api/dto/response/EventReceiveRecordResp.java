package com.cscec3b.iti.projectmanagement.api.dto.response;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EventReceiveRecordReq")
public class EventReceiveRecordResp extends BasePage implements Serializable {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     * 项目中心项目id
     */
    @ApiModelProperty(value = "项目中心项目id")
    private Long projectId;

    /**
     * 项目标识
     */
    @ApiModelProperty(value = "项目标识")
    private String cpmProjectKey;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String cpmProjectName;

    /**
     * 项目简称
     */
    @ApiModelProperty(value = "项目简称")
    private String cpmProjectAbbreviation;

    /**
     * 执行单位部门Id(组织id)
     */
    @ApiModelProperty(value = "执行单位部门Id(组织id)")
    private String yunshuExecuteUnitId;


    /**
     * 执行单位部门treeId路径
     */
    @ApiModelProperty(value = "执行单位部门treeId路径")
    private String yunshuExecuteUnitIdPath;

    /**
     * 执行单位名称
     */
    @ApiModelProperty(value = "执行单位名称")
    private String yunshuExecuteUnit;

    /**
     * 执行单位简称
     */
    @ApiModelProperty(value = "执行单位简称")
    private String yunshuExecuteUnitAbbreviation;

    /**
     * 执行单位全路径名称
     */
    @ApiModelProperty(value = "执行单位全路径名称")
    private String yunshuExecuteUnitFullPathName;

    /**
     * 执行单位全路径名称简称
     */
    @ApiModelProperty(value = "执行单位全路径名称简称")
    private String yunshuExecuteUnitFullPathAbbreviation;

    /**
     * 事件编码
     */
    @ApiModelProperty(value = "事件编码")
    private String eventCode;

    /**
     * 接收到参数
     */
    @ApiModelProperty(value = "接收参数")
    private String receiveParam;

    /**
     * 请求时间范围结束
     */
    @ApiModelProperty(value = "请求时间")
    private Long receiveTime;

    /**
     * api 返回结果 0：失败，1：成功
     */
    @ApiModelProperty(value = "api 返回结果 0：失败，1：成功")
    private Integer apiResult;

    /**
     * 错误信息
     */
    @ApiModelProperty(value = "错误信息")
    private String errMsg;

    /**
     * 项目来源
     */
    @ApiModelProperty("项目来源: 1:标准项目； 2:特殊立项")
    private int sourceSystem;
}
