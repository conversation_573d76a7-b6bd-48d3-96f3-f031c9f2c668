package com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 项目外部系统信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("d_project_rely_info")
@ApiModel(value = "ProjectRelyInfo对象", description = "项目外部系统信息表")
public class ProjectRelyInfo implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 自增id
     */
    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 与基础信息表关联
     */
    @ApiModelProperty(value = "与基础信息表关联")
    private Integer projectId;

    /**
     * 产值系统ID
     */
    @ApiModelProperty(value = "产值系统ID")
    private String valueSystemId;

    /**
     * 劳务类型
     */
    @ApiModelProperty(value = "劳务类型")
    private String serviceType;

    /**
     * 劳务系统ID
     */
    @ApiModelProperty(value = "劳务系统ID")
    private String serviceSystemId;

    /**
     * 劳务实名制平台类型
     */
    @ApiModelProperty(value = "劳务实名制平台类型")
    private String materialType;

    /**
     * 劳务实名制平台编号
     */
    @ApiModelProperty(value = "劳务实名制平台编号")
    private String materialSystemId;

    /**
     * 云筑智联编号
     */
    @ApiModelProperty(value = "云筑智联编号")
    private String videoProjectId;

    /**
     * 云筑智联编号校验
     */
    @ApiModelProperty(value = "云筑智联编号校验")
    private Integer videoFlag;

    /**
     * 审批通过的信息集合
     */
    @ApiModelProperty(value = "审批通过的信息集合")
    private String displayInfo;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

}
