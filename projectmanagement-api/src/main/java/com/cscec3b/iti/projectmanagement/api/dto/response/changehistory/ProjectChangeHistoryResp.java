package com.cscec3b.iti.projectmanagement.api.dto.response.changehistory;

import com.cscec3b.iti.projectmanagement.api.dto.request.changehistory.ProjectChangeHistoryDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "ProjectChangeHistoryResp", description = "项目变更历史响应对象")
public class ProjectChangeHistoryResp extends ProjectChangeHistoryDto {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private Long id;

    /**
     * 变更记录时间
     */
    @ApiModelProperty(value = "变更记录时间")
    private Long createAt;

    /**
     * 变更记录人
     */
    @ApiModelProperty(value = "变更记录人")
    private String createBy;


    /**
     * 变更记录人姓名
     */
    @ApiModelProperty(value = "变更记录人姓名")
    private String createName;

    /**
     * 变更记录更新时间
     */
    @ApiModelProperty(value = "变更记录更新时间")
    private String updateAt;

    /**
     * 变更记录更新人
     */
    @ApiModelProperty(value = "变更记录更新人")
    private String updateBy;

    /**
     * 变更记录更新人姓名
     */
    @ApiModelProperty(value = "变更记录更新人姓名")
    private String updateName;


}
