//package com.cscec3b.iti.projectmanagement.api;
//
//import org.springframework.web.bind.annotation.GetMapping;
//import org.springframework.web.bind.annotation.PutMapping;
//import org.springframework.web.bind.annotation.RequestParam;
//
//import com.cscec3b.iti.common.base.api.GenericityResponse;
//import com.cscec3b.iti.projectmanagement.api.dto.response.PmUserInfoResp;
//import com.cscec3b.iti.usercenter.sdk.api.extendapp.dto.resp.oauth.AppAuthRes;
//
//import io.swagger.annotations.ApiOperation;
//import io.swagger.annotations.ApiParam;
//
///**
// * 用户认证管理
// *
// * <AUTHOR>
// * @date 2022/4/1 13:03
// **/
//
//public interface IPmAuthController {
//    /**
//     * 路径
//     */
//    String PATH = "/auth";
//
//
//    /**
//     * 获取登陆地址
//     * @return
//     * <AUTHOR>
//     * @date 2023/10/8
//     */
//    @GetMapping("/login-url")
//    @ApiOperation(value = "获取登陆地址")
//    GenericityResponse<String> loginUrl();
//
//    /**
//     * 获取state参数
//     * @param callback
//     * @return
//     * <AUTHOR>
//     * @date 2023/10/8
//     */
//    @GetMapping("/state")
//    @ApiOperation(value = "获取state参数", notes = "state是为了保持请求一致性,防止CSRF; 该步骤非必须")
//    GenericityResponse<String> state(@ApiParam(value = "callback uri") String callback);
//
//    /**
//     * 认证服务,code换取token
//     * @param code
//     * @param state
//     * @return
//     * <AUTHOR>
//     * @date 2023/10/8
//     */
//    @GetMapping("/authorize")
//    @ApiOperation(value = "认证服务,code换取token", notes = "从认证中心进行认证,使用code换取token")
//    GenericityResponse<AppAuthRes> authorize(
//        @ApiParam(value = "认证中心返回的code", required = true) @RequestParam(value = "code") String code,
//        @ApiParam(value = "请求code时传入的state") @RequestParam(value = "state", required = false) String state);
//
//
//    /**
//     * 获取用户信息
//     * @return
//     * <AUTHOR>
//     * @date 2023/10/8
//     */
//    @GetMapping("/userinfo")
//    @ApiOperation(value = ("获取用户信息"))
//    GenericityResponse<PmUserInfoResp> getUserInfo();
//
//
//    /**
//     * 刷新token
//     * @return
//     * <AUTHOR>
//     * @date 2023/10/8
//     */
//    @PutMapping
//    @ApiOperation(value = "刷新token")
//    GenericityResponse<AppAuthRes> refreshToken();
//
//    /**
//     * 退出登录
//     * @return
//     * <AUTHOR>
//     * @date 2023/10/8
//     */
//    @PutMapping("/logout")
//    @ApiOperation(value = "退出登录")
//    GenericityResponse<Boolean> logout();
//}
