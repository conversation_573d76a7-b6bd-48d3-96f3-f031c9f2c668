package com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 工程项目信息 ：包含上下级信息
 * 
 * <AUTHOR>
 * @date 2025/03/14
 */
@Data
@ApiModel(value = "工程项目信息(上下级)返回值")
public class EngineeringProjectInfoResp implements Serializable {

    /**
     * 上级工程信息
     */
    @ApiModelProperty(value = "上级工程信息")
    private EngineeringProjectVo parentProject;

    /**
     * 当前工程信息
     */
    @ApiModelProperty(value = "当前工程信息")
    private EngineeringProjectVo currentProject;

    /**
     * 下级工程信息
     */
    @ApiModelProperty(value = "下级工程信息")
    private List<EngineeringProjectVo> childProjects;
}
