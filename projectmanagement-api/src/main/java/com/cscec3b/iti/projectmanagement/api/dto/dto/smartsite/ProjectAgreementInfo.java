package com.cscec3b.iti.projectmanagement.api.dto.dto.smartsite;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 项目履约信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-09
 */
@Data
@ApiModel(value = "ProjectAgreementInfo对象", description = "项目履约信息表")
public class ProjectAgreementInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @ApiModelProperty(value = "自增id")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 与基础信息表关联
     */
    @ApiModelProperty(value = "与基础信息表关联")
    private Integer projectId;

    /**
     * 合同开工日期
     */
    @ApiModelProperty(value = "合同开工日期")
    private Date contractStartDate;

    /**
     * 合同竣工日期
     */
    @ApiModelProperty(value = "合同竣工日期")
    private Date contractEndDate;

    /**
     * 合同天数
     */
    @ApiModelProperty(value = "合同天数")
    private Integer contractDay;

    /**
     * 实际开工日期
     */
    @ApiModelProperty(value = "实际开工日期")
    private Date realityStartDate;

    /**
     * 实际竣工日期
     */
    @ApiModelProperty(value = "实际竣工日期")
    private Date realityEndDate;

    /**
     * 实际天数
     */
    @ApiModelProperty(value = "实际天数")
    private Integer realityDay;

    /**
     * 项目进场日期
     */
    @ApiModelProperty(value = "项目进场日期")
    private Date projectApprovalDate;

    /**
     * 业主下发开工令日期
     */
    @ApiModelProperty(value = "业主下发开工令日期")
    private Date ownerStartDate;

    /**
     * 监理下发开工令日期
     */
    @ApiModelProperty(value = "监理下发开工令日期")
    private Date managerStartDate;

    /**
     * 五方主体验收日期
     */
    @ApiModelProperty(value = "五方主体验收日期")
    private Date checkDate;

    /**
     * 竣工备案日期
     */
    @ApiModelProperty(value = "竣工备案日期")
    private Date recordDate;

    /**
     * 项目状态
     */
    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    /**
     * 审批通过的信息集合
     */
    @ApiModelProperty(value = "审批通过的信息集合")
    private String displayInfo;

    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createdAt;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updatedAt;

    /**
     * 项目完工（通车）时间
     */
    @ApiModelProperty(value = "项目完工（通车）时间")
    private Date projectFinishDate;

    /**
     * 智慧工地立项时间
     */
    @ApiModelProperty(value = "智慧工地立项时间")
    private Date approvalTime;

}
