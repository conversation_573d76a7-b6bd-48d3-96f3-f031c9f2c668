package com.cscec3b.iti.projectmanagement.api;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.projectmanagement.api.dto.request.UcAppNotify;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 应用回调通知
 * <AUTHOR>
 * @date 2022/10/19 16:35
 **/
@Api(tags = "应用回调通知")
public interface IAppMessageController {

    /**
     * 路径
     */
    String PATH = "/callback";
    
    /**
     * 接收应用推送通知
     *
     * @param reqMap 通知信息
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/08/21
     */
    @Deprecated
    @ApiOperation("接收应用推送通知")
    @PostMapping(value = "/uc/notify")
    GenericityResponse<Boolean> receiveMessage(@ApiParam(value = "入参对象", required = true) @RequestBody UcAppNotify<String> reqMap);

}
