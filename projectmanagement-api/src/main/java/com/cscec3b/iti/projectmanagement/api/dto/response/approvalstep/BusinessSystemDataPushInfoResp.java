package com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 业业系统数据推送信息
 *
 * <AUTHOR>
 * @date 2024/01/15
 */
@Data
@ApiModel(description = "业业系统数据推送信息")
public class BusinessSystemDataPushInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 业务系统id
     */
    @ApiModelProperty("业务系统id")
    private Long subscribeId;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Long cpmProjectId;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String cpmProjectKey;

    /**
     * 系统业务名称
     */
    @ApiModelProperty("系统业务名称")
    private String subscriber;

    /**
     * 业务系统对应组织queryCode
     */
    @ApiModelProperty("业务系统对应组织queryCode")
    private String yunshuExecuteUnitIdPath;

    /**
     * 业务系统对应组织名称
     */
    @ApiModelProperty("业务系统对应组织名称")
    private String yunshuExecuteUnit;

    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Long createAt;
    /**
     * 推送时间
     */
    @ApiModelProperty("推送时间")
    private Long pushTime;

    /**
     * 推送结果: 0:未成功； 1：成功
     */
    @ApiModelProperty("推送结果: 0:未成功； 1：成功")
    private Long pushSuccess;

    /**
     * EHR 立项状态 0:未立项; 1:立项中: 2:立项成功;
     */
    @ApiModelProperty("组织类型 10：局；30：公司；40：分公司")
    private Integer orgType;

}
