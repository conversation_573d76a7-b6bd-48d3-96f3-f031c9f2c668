package com.cscec3b.iti.projectmanagement.api.dto.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description attachmentDto
 * <AUTHOR>
 * @Date 2023/1/3 10:55
 */

@Data
@Accessors(chain = true)
@ApiModel(value = "AttachmentDto", description = "附件信息对象")
public class AttachmentDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 原始文件名称
     */
    @ApiModelProperty(value = "原始文件名称")
    @NotNull(message = "原始文件名称不能为空")
    private String originalName;

    /**
     * 文件大小(bytes)
     */
    @ApiModelProperty(value = "文件大小(bytes)")
    private Long fileSize;

    /**
     * 附件访问路径
     */
    @ApiModelProperty(value = "附件访问路径")
    @NotNull(message = "附件访问路径不能为空")
    private String filePath;

    /**
     * 文件编码后的id
     */
    @ApiModelProperty(value = "文件编码后的id")
    @NotNull(message = "文件id不能为空")
    private String fileId;

    /**
     * 文件md5
     */
    @ApiModelProperty(value = "文件md5")
    @NotNull(message = "文件md5不能为空")
    private String fileMd5;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    private String createAt;

    @ApiModelProperty(value = "是否编辑器内文件")
    private int inEditor = 0;

}
