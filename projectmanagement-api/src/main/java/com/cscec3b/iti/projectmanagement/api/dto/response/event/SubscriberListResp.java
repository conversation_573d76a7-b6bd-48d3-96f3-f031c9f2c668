package com.cscec3b.iti.projectmanagement.api.dto.response.event;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/26 10:14
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SubscriberListResp", description = "订阅系统查询列表对象")
public class SubscriberListResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订阅系统id
     */
    @ApiModelProperty(value = "主键id")
    private Integer id;


    /**
     * 订阅系统id
     */
    @ApiModelProperty(value = "订阅系统id")
    private Integer consumerId;

    /**
     * 订阅系统
     */
    @ApiModelProperty(value = "订阅系统")
    private String subscriber;

    /**
     * 事件类型：事件id,逗号分隔
     */
    @ApiModelProperty(value = "事件类型：事件id,逗号分隔")
    private String eventIds;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态 0:删除; 1:正常")
    private int status;


    @ApiModelProperty(value = "订阅者业务标识")
    private String appCode;


    /**
     * spi扩展实现类名称
     */
    @ApiModelProperty(value = "spi扩展实现类名称")
    private String spiClassName;

    public void setId(Integer id) {
        this.id = id;
        this.consumerId = id;
    }
}
