package com.cscec3b.iti.projectmanagement.api.dto.request;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 运维导入项目信息
 *
 * <AUTHOR>
 * @date 2023/07/25 15:17
 **/

@Data
public class OMProjectUpdateReq extends OMProjectInitReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    @NotNull(message = "项目id不能为空")
    private Long id;


}
