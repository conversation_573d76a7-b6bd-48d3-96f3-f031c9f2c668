package com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 集团主数据推送信息
 *
 * <AUTHOR>
 * @date 2024/01/15
 */
@Data
@ApiModel(description = "集团主数据推送信息")
public class MainDataPushInfoResp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 项目id
     */
    @ApiModelProperty("项目id")
    private Long cpmProjectId;

    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String cpmProjectKey;

    /**
     * 财商立项开始时间
     */
    @ApiModelProperty("财商立项开始时间")
    private Long toFinanceTime;

    /**
     * 财商立项结束时间
     */
    @ApiModelProperty("财商立项结束时间")
    private Long approveFinishTime;

    /**
     * 财商立项状态 0：未立项； 1：立项中； 2：已立项
     */
    @ApiModelProperty("财商立项状态 0：未立项； 1：立项中； 2：已立项")
    private Integer approveStatus;

    /**
     * 财商立项备注
     */
    @ApiModelProperty("财商立项备注")
    private String financeRemarks;

    /**
     * EHR组织创建时间
     */
    @ApiModelProperty("EHR组织创建时间")
    private Long smartQueryTime;

    /**
     * EHR组织创建完成时间
     */
    @ApiModelProperty("EHR组织创建完成时间")
    private Long toUcTime;

    /**
     * EHR 立项状态 0:未立项; 1:立项中: 2:立项成功;
     */
    @ApiModelProperty("EHR/工地/组织填报 立项状态  0:未立项; 1:立项中: 2:立项成功;")
    private Integer smartApproveStatus;

    /**
     * 智慧工地立项备注
     */
    @ApiModelProperty("智慧工地立项备注")
    private String smartRemarks;

    /**
     * 财商项目编码
     */
    @ApiModelProperty("财商项目编码")
    private String projectFinanceCode;

    /**
     * 财商项目名称
     */
    @ApiModelProperty("财商项目名称")
    private String projectFinanceName;

    /**
     * 财商项目简称
     */
    @ApiModelProperty("财商项目简称")
    private String projectFinanceAbbreviation;

    /**
     * 项目部组织id
     */
    @ApiModelProperty("项目部组织id")
    private String yunshuOrgId;

    /**
     * 项目部组织名称
     */
    @ApiModelProperty("项目部组织名称")
    private String yunshuOrgName;

    /**
     * 六统一组织编码
     */
    @ApiModelProperty("六统一组织编码")
    private String allInOneCode;

}
