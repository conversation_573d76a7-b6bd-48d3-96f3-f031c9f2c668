package com.cscec3b.iti.projectmanagement.api.bidapproval.dto.request;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 补充协议
 *
 * <AUTHOR>
 * @Description
 * @Date 2022/10/18 15:53
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SupplementaryAgreementFileReq", description = "补充协议请求对象")
public class BidSupplementaryAgreementFileReq extends MarketFileBaseReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 独立文件id
     */
    @ApiModelProperty(value = "独立文件id", position = 132)
    @NotNull(message = "独立文件id不能为空")
    private Long originFileId;

    /**
     * 复核审批用户id
     */
    @ApiModelProperty(value = "复核审批用户id", position = 1)
    // @NotNull(message = "复核审批用户id不能为空")
    // @Size(max = 20, message = "复核审批用户id的字段长度必须小于[{max}]")
    private String approvalPerson;

    /**
     * 是否创建指挥部
     */
    @ApiModelProperty(value = "是否创建指挥部", position = 3)
    private String isCreateHead;

    /**
     * 执行单位
     */
    @ApiModelProperty(value = "执行单位", position = 4)
    @Size(max = 200, message = "执行单位的字段长度必须小于[{max}]")
    private String executeUnit;

    @ApiModelProperty(value = "执行单位简称", position = 4)
    @Size(max = 200, message = "执行单位简称长度不超过{max}个字符")
    private String executeUnitAbbreviation;

    /**
     * 独立性
     */
    @ApiModelProperty(value = "独立性", position = 5)
    @Size(max = 2, message = "独立性的字段长度必须小于[{max}]")
    private String isIndependent;

    /**
     * 关联工程/合同
     */
    @ApiModelProperty(value = "关联工程/合同", position = 6)
    @Size(max = 32, message = "关联工程/合同的字段长度必须小于[{max}]")
    private String associatedProject;

    /**
     * 工程编号
     */
    @ApiModelProperty(value = "工程编号", position = 7)
    // @NotNull(message = "工程编号字段不能为空")
    // @Size(max = 32, message = "工程编号的字段长度必须小于[{max}]")
    private String projectCode;


    /**
     * 所属办事处
     */
    @ApiModelProperty(value = "所属办事处", position = 9)
    @Size(max = 128, message = "所属办事处的字段长度必须小于[{max}]")
    private String signFormOffice;

    /**
     * 工程简称
     */
    @ApiModelProperty(value = "工程简称", position = 10)
    @Size(max = 255, message = "工程简称的字段长度必须小于[{max}]")
    private String projectAbbreviation;


    /**
     * 工程类型（国家标准）
     */
    @ApiModelProperty(value = "工程类型（国家标准）", position = 17)
    //    @NotNull(message = "工程类型（国家标准）不能为空")
    @Size(max = 32, message = "工程类型（国家标准）的字段长度必须小于[{max}]")
    private String countryProjectType;

    /**
     * 工程类型（总公司市场口径）
     */
    @ApiModelProperty(value = "工程类型（总公司市场口径）", position = 18)
    //    @NotNull(message = "工程类型（总公司市场口径）不能为空")
    @Size(max = 32, message = "工程类型（总公司市场口径）的字段长度必须小于[{max}]")
    private String marketProjectType;

    /**
     * 工程类型(总公司市场口径)2
     */
    @ApiModelProperty(value = "工程类型(总公司市场口径)2", position = 19)
    @Size(max = 32, message = "工程类型(总公司市场口径)2的字段长度必须小于[{max}]")
    private String marketProjectType2;


    /**
     * 工程类型(总公司综合口径)3
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)3", position = 22)
    @Size(max = 32, message = "工程类型(总公司综合口径)3的字段长度必须小于[{max}]")
    private String projectType3;

    /**
     * 工程类型(总公司综合口径)4
     */
    @ApiModelProperty(value = "工程类型(总公司综合口径)4", position = 23)
    @Size(max = 32, message = "工程类型(总公司综合口径)4的字段长度必须小于[{max}]")
    private String projectType4;

    /**
     * 总分包类别
     */
    @ApiModelProperty(value = "总分包类别", position = 24)
    @Size(max = 32, message = "总分包类别的字段长度必须小于[{max}]")
    private String totalSubcontractingCategory;

    /**
     * 结构形式
     */
    @ApiModelProperty(value = "结构形式", position = 25)
    @Size(max = 64, message = "结构形式的字段长度必须小于[{max}]")
    private String structuralStyle;

    /**
     * 结构形式2
     */
    @ApiModelProperty(value = "结构形式2", position = 26)
    @Size(max = 64, message = "结构形式2的字段长度必须小于[{max}]")
    private String structuralStyle2;

    /**
     * 是否有钢结构
     */
    @ApiModelProperty(value = "是否有钢结构", position = 27)
    @Size(max = 4, message = "是否有钢结构的字段长度必须小于[{max}]")
    private String includingSteel;

    /**
     * 最长桩基长度
     */
    @ApiModelProperty(value = "最长桩基长度", position = 28)
    @Size(max = 32, message = "最长桩基长度的字段长度必须小于[{max}]")
    private String projectMaxLength;

    /**
     * 最大桩径
     */
    @ApiModelProperty(value = "最大桩径", position = 29)
    @Size(max = 32, message = "最大桩径的字段长度必须小于[{max}]")
    private String projectMaxWidth;

    /**
     * 合同类型
     */
    @ApiModelProperty(value = "合同类型", position = 30)
    @Size(max = 64, message = "合同类型的字段长度必须小于[{max}]")
    private String contractType;

    /**
     * 是否装配式
     */
    @ApiModelProperty(value = "是否装配式", position = 31)
    @Size(max = 4, message = "是否装配式的字段长度必须小于[{max}]")
    private String fabricated;

    /**
     * 装配率
     */
    @ApiModelProperty(value = "装配率", position = 32)
    @Size(max = 255, message = "装配率的字段长度必须小于[{max}]")
    private String fabricatedRate;

    /**
     * 是否为投融资带动项目
     */
    @ApiModelProperty(value = "是否为投融资带动项目", position = 33)
    @Size(max = 4, message = "是否为投融资带动项目的字段长度必须小于[{max}]")
    private String isInvestmentFinancingDrivenProjects;

    /**
     * 业务类型
     */
    @ApiModelProperty(value = "业务类型", position = 34)
    @NotNull(message = "业务类型不能为空")
    @Size(max = 32, message = "业务类型的字段长度必须小于[{max}]")
    private String businessType;

    /**
     * 是否投资项目
     */
    @ApiModelProperty(value = "是否投资项目", position = 35)
    @NotNull(message = "是否投资项目不能为空")
    @Size(max = 32, message = "是否投资项目的字段长度必须小于[{max}]")
    private String investmentProjects;

    /**
     * 投资主体
     */
    @ApiModelProperty(value = "投资主体", position = 36)
    @Size(max = 20, message = "投资主体的字段长度必须小于[{max}]")
    private String investors;


    /**
     * 设计单位
     */
    @ApiModelProperty(value = "设计单位", position = 42)
    @Size(max = 255, message = "设计单位的字段长度必须小于[{max}]")
    private String designer;

    /**
     * 监理单位
     */
    @ApiModelProperty(value = "监理单位", position = 43)
    @Size(max = 255, message = "监理单位的字段长度必须小于[{max}]")
    private String supervisor;

    /**
     * 实际中标日期
     */
    @ApiModelProperty(value = "实际中标日期", position = 44)
    private Long successfulTime;

    /**
     * 实际签约日期
     */
    @ApiModelProperty(value = "实际签约日期", position = 45)
    private Long actualSignedTime;

    /**
     * 签约主体
     */
    @ApiModelProperty(value = "签约主体", position = 46)
    @Size(max = 255, message = "签约主体的字段长度必须小于[{max}]")
    private String signedSubjectValue;

    /**
     * 签约主体
     */
    @ApiModelProperty(value = "签约主体Code", position = 42)
    @Size(max = 100, message = "签约主体不超过{max}个字符")
    private String signedSubjectCode;

    /**
     * 实施单位
     */
    @ApiModelProperty(value = "实施单位", position = 47)
    @Size(max = 255, message = "实施单位的字段长度必须小于[{max}]")
    private String doUnit;


    /**
     * 自行施工不含税金额
     */
    @ApiModelProperty(value = "自行施工不含税金额", position = 50)
    private BigDecimal midAmountSelf;

    /**
     * 土建不含税金额
     */
    @ApiModelProperty(value = "土建不含税金额", position = 51)
    private BigDecimal selfCivilAmount;

    /**
     * 安装不含税金额
     */
    @ApiModelProperty(value = "安装不含税金额", position = 52)
    private BigDecimal selfInstallAmount;

    /**
     * 钢结构不含税金额
     */
    @ApiModelProperty(value = "钢结构不含税金额", position = 53)
    private BigDecimal selfSteelStructureAmount;

    /**
     * 总包服务费
     */
    @ApiModelProperty(value = "总包服务费", position = 54)
    private BigDecimal selfTotalServiceAmount;

    /**
     * 其他
     */
    @ApiModelProperty(value = "其他", position = 55)
    private BigDecimal selfOtherAmount;

    /**
     * 暂列金额或甲指分包金额
     */
    @ApiModelProperty(value = "暂列金额或甲指分包金额", position = 56)
    private BigDecimal subcontractAmount;

    /**
     * 销项税额
     */
    @ApiModelProperty(value = "销项税额", position = 57)
    private BigDecimal projectTaxAmount;

    /**
     * 合同优化条款
     */
    @ApiModelProperty(value = "合同优化条款", position = 58)
    private String contractOptimizeClause;

    /**
     * 合同优化金额
     */
    @ApiModelProperty(value = "合同优化金额", position = 59)
    private BigDecimal contractOptimizeAmount;

    /**
     * 合同优化率
     */
    @ApiModelProperty(value = "合同优化率", position = 60)
    private String contractOptimizeRatio;

    /**
     * 暂列金额工作内容
     */
    @ApiModelProperty(value = "暂列金额工作内容", position = 61)
    @Size(max = 1024, message = "暂列金额工作内容的字段长度必须小于[{max}]")
    private String subcontractContent;

    /**
     * 中标项目经理
     */
    @ApiModelProperty(value = "中标项目经理", position = 62)
    @Size(max = 128, message = "中标项目经理的字段长度必须小于[{max}]")
    private String bidManager;

    /**
     * 中标项目经理注册证书编号
     */
    @ApiModelProperty(value = "中标项目经理注册证书编号", position = 63)
    @Size(max = 128, message = "中标项目经理注册证书编号的字段长度必须小于[{max}]")
    private String bidManagerCode;

    /**
     * 执行项目经理
     */
    @ApiModelProperty(value = "执行项目经理", position = 64)
    @Size(max = 128, message = "执行项目经理的字段长度必须小于[{max}]")
    private String excuteManager;

    /**
     * 执行项目经理联系方式
     */
    @ApiModelProperty(value = "执行项目经理联系方式", position = 65)
    @Size(max = 64, message = "执行项目经理联系方式的字段长度必须小于[{max}]")
    private String excuteManagerCode;

    /**
     * 合同项目经理
     */
    @ApiModelProperty(value = "合同项目经理", position = 66)
    @Size(max = 128, message = "合同项目经理的字段长度必须小于[{max}]")
    private String contractManager;

    /**
     * 合同项目经理注册证书编号
     */
    @ApiModelProperty(value = "合同项目经理注册证书编号", position = 67)
    @Size(max = 128, message = "合同项目经理注册证书编号的字段长度必须小于[{max}]")
    private String contractManagerCode;

    /**
     * 政府备案项目经理
     */
    @ApiModelProperty(value = "政府备案项目经理", position = 68)
    @Size(max = 128, message = "政府备案项目经理的字段长度必须小于[{max}]")
    private String governmentManager;

    /**
     * 政府备案项目经理注册证书编号
     */
    @ApiModelProperty(value = "政府备案项目经理注册证书编号", position = 69)
    @Size(max = 128, message = "政府备案项目经理注册证书编号的字段长度必须小于[{max}]")
    private String governmentManagerCode;

    /**
     * 承包模式
     */
    @ApiModelProperty(value = "承包模式", position = 70)
    @Size(max = 64, message = "承包模式的字段长度必须小于[{max}]")
    private String contractMode1;

    /**
     * 承包模式2
     */
    @ApiModelProperty(value = "承包模式2", position = 71)
    @Size(max = 64, message = "承包模式2的字段长度必须小于[{max}]")
    private String contractMode2;

    /**
     * 合同承包范围
     */
    @ApiModelProperty(value = "合同承包范围", position = 72)
    @Size(max = 2048, message = "合同承包范围的字段长度必须小于[{max}]")
    private String contractScope;

    /**
     * 发包人指定分包、独立分包的工程
     */
    @ApiModelProperty(value = "发包人指定分包、独立分包的工程", position = 73)
    @Size(max = 2048, message = "发包人指定分包、独立分包的工程的字段长度必须小于[{max}]")
    private String issuerProject;

    /**
     * 总工期（天）
     */
    @ApiModelProperty(value = "总工期（天）", position = 74)
    @JsonProperty("countDays")
    private Integer countdays;

    /**
     * 合同开工日期
     */
    @ApiModelProperty(value = "合同开工日期", position = 75)
    private Long workerBeginTime;

    /**
     * 合同竣工日期
     */
    @ApiModelProperty(value = "合同竣工日期", position = 76)
    private Long workerEndTime;

    /**
     * 实际开工日期
     */
    @ApiModelProperty(value = "实际开工日期", position = 77)
    private Long realWorkBeginTime;

    /**
     * 预计实际竣工日期
     */
    @ApiModelProperty(value = "预计实际竣工日期", position = 78)
    private Long predictWorkEndTime;

    /**
     * 工期奖罚类型
     */
    @ApiModelProperty(value = "工期奖罚类型", position = 79)
    @Size(max = 20, message = "工期奖罚类型的字段长度必须小于[{max}]")
    @JsonProperty("workerDateRewardPunish")
    private String workerDatetimeRewardPunish;

    /**
     * 工期奖罚条款
     */
    @ApiModelProperty(value = "工期奖罚条款", position = 80)
    @Size(max = 512, message = "工期奖罚条款的字段长度必须小于[{max}]")
    private String workerRewardPunishAppoint;

    /**
     * 合同范本类型
     */
    @ApiModelProperty(value = "合同范本类型", position = 81)
    @Size(max = 128, message = "合同范本类型的字段长度必须小于[{max}]")
    private String contractStyle;

    /**
     * 质量要求
     */
    @ApiModelProperty(value = "质量要求", position = 82)
    @Size(max = 64, message = "质量要求的字段长度必须小于[{max}]")
    private String qualityGuarantee;

    /**
     * 质量奖罚类型
     */
    @ApiModelProperty(value = "质量奖罚类型", position = 83)
    @Size(max = 64, message = "质量奖罚类型的字段长度必须小于[{max}]")
    private String rewardPunishType;

    /**
     * 质量奖罚条款
     */
    @ApiModelProperty(value = "质量奖罚条款", position = 84)
    @Size(max = 2048, message = "质量奖罚条款的字段长度必须小于[{max}]")
    private String rewardPunishTerms;

    /**
     * 安全文明施工要求
     */
    @ApiModelProperty(value = "安全文明施工要求", position = 85)
    @Size(max = 2048, message = "安全文明施工要求的字段长度必须小于[{max}]")
    private String safetyRequirement;

    /**
     * 安全文明施工奖罚条款
     */
    @ApiModelProperty(value = "安全文明施工奖罚条款", position = 86)
    @Size(max = 2048, message = "安全文明施工奖罚条款的字段长度必须小于[{max}]")
    private String safetyRewardPunishTerms;


    /**
     * 人工费是否可调
     */
    @ApiModelProperty(value = "人工费是否可调", position = 89)
    @Size(max = 2, message = "人工费是否可调的字段长度必须小于[{max}]")
    private String costOfLaborChange;

    /**
     * 人工费调差形式
     */
    @ApiModelProperty(value = "人工费调差形式", position = 90)
    @Size(max = 64, message = "人工费调差形式的字段长度必须小于[{max}]")
    private String changeWay;

    /**
     * 信息价
     */
    @ApiModelProperty(value = "信息价", position = 91)
    @Size(max = 15, message = "信息价的字段长度必须小于[{max}]")
    private String changeRate;

    /**
     * 人工费调差条款
     */
    @ApiModelProperty(value = "人工费调差条款", position = 92)
    @Size(max = 2048, message = "人工费调差条款的字段长度必须小于[{max}]")
    private String terms;

    /**
     * 主材费是否可调
     */
    @ApiModelProperty(value = "主材费是否可调", position = 93)
    @Size(max = 2, message = "主材费是否可调的字段长度必须小于[{max}]")
    private String costOfLaborChange2;

    /**
     * 主材费调差幅度
     */
    @ApiModelProperty(value = "主材费调差幅度", position = 94)
    @Size(max = 15, message = "主材费调差幅度的字段长度必须小于[{max}]")
    private String changeRate2;

    /**
     * 主材费调差条款
     */
    @ApiModelProperty(value = "主材费调差条款", position = 95)
    @Size(max = 2048, message = "主材费调差条款的字段长度必须小于[{max}]")
    private String terms2;

    /**
     * 是否有预付款
     */
    @ApiModelProperty(value = "是否有预付款", position = 96)
    @Size(max = 2, message = "是否有预付款的字段长度必须小于[{max}]")
    private String advancesFlag;

    /**
     * 预付款比例
     */
    @ApiModelProperty(value = "预付款比例", position = 97)
    @Size(max = 16, message = "预付款比例的字段长度必须小于[{max}]")
    private String advancesRate;

    /**
     * 预付款金额
     */
    @ApiModelProperty(value = "预付款金额", position = 98)
    private BigDecimal advancesAmount;

    /**
     * 预付款是否抵扣
     */
    @ApiModelProperty(value = "预付款是否抵扣", position = 99)
    @Size(max = 2, message = "预付款是否抵扣的字段长度必须小于[{max}]")
    private String advancesDeductionFlag;

    /**
     * 预付款抵扣方式
     */
    @ApiModelProperty(value = "预付款抵扣方式", position = 100)
    @Size(max = 255, message = "预付款抵扣方式的字段长度必须小于[{max}]")
    private String advancesDeduction;

    /**
     * 进度款付款方式
     */
    @ApiModelProperty(value = "进度款付款方式", position = 101)
    @Size(max = 64, message = "进度款付款方式的字段长度必须小于[{max}]")
    private String advancesWay;

    /**
     * 月进度付款比例
     */
    @ApiModelProperty(value = "月进度付款比例", position = 102)
    @Size(max = 64, message = "月进度付款比例的字段长度必须小于[{max}]")
    private String advancesMonthRate;

    /**
     * 竣工验收支付比例
     */
    @ApiModelProperty(value = "竣工验收支付比例", position = 103)
    @Size(max = 16, message = "竣工验收支付比例的字段长度必须小于[{max}]")
    private String completedRate;

    /**
     * 竣工验收收款周期（月）
     */
    @ApiModelProperty(value = "竣工验收收款周期（月）", position = 104)
    @Size(max = 64, message = "竣工验收收款周期（月）的字段长度必须小于[{max}]")
    private String completedCycle;

    /**
     * 结算支付比例
     */
    @ApiModelProperty(value = "结算支付比例", position = 105)
    @Size(max = 20, message = "结算支付比例的字段长度必须小于[{max}]")
    private String settlementRate;

    /**
     * 结算周期（月）
     */
    @ApiModelProperty(value = "结算周期（月）", position = 106)
    @Size(max = 64, message = "结算周期（月）的字段长度必须小于[{max}]")
    private String settlementCycle;

    /**
     * 保修金
     */
    @ApiModelProperty(value = "保修金", position = 107)
    @Size(max = 22, message = "保修金的字段长度必须小于[{max}]")
    private String warrantyPremium;

    /**
     * 保修金比例
     */
    @ApiModelProperty(value = "保修金比例", position = 108)
    @Size(max = 50, message = "保修金比例的字段长度必须小于[{max}]")
    private String warrantyPremiumRate;

    /**
     * 保修金支付方式
     */
    @ApiModelProperty(value = "保修金支付方式", position = 109)
    @Size(max = 128, message = "保修金支付方式的字段长度必须小于[{max}]")
    private String warrantyPremiumWay;

    /**
     * 支付方式
     */
    @ApiModelProperty(value = "支付方式", position = 110)
    @Size(max = 128, message = "支付方式的字段长度必须小于[{max}]")
    private String payTypeNew;

    /**
     * 现金支付方式
     */
    @ApiModelProperty(value = "现金支付方式", position = 111)
    @Size(max = 128, message = "现金支付方式的字段长度必须小于[{max}]")
    private String specificPayWay;

    /**
     * 是否垫资
     */
    @ApiModelProperty(value = "是否垫资", position = 112)
    @Size(max = 2, message = "是否垫资的字段长度必须小于[{max}]")
    private String advancesFundFlag;

    /**
     * 履约担保方式
     */
    @ApiModelProperty(value = "履约担保方式", position = 113)
    @Size(max = 64, message = "履约担保方式的字段长度必须小于[{max}]")
    private String guaranteeWay;

    /**
     * 履约担保比例
     */
    @ApiModelProperty(value = "履约担保比例", position = 114)
    @Size(max = 16, message = "履约担保比例的字段长度必须小于[{max}]")
    private String guaranteeRate;

    /**
     * 履约担保金额
     */
    @ApiModelProperty(value = "履约担保金额", position = 115)
    private BigDecimal guaranteeAmount;

    /**
     * 其他保函名称
     */
    @ApiModelProperty(value = "其他保函名称", position = 116)
    @Size(max = 64, message = "其他保函名称的字段长度必须小于[{max}]")
    private String otherGuaranteeName;

    /**
     * 是否经第三方审计
     */
    @ApiModelProperty(value = "是否经第三方审计", position = 117)
    @Size(max = 4, message = "是否经第三方审计的字段长度必须小于[{max}]")
    private String isBeenAuditedByThirdParty;

    /**
     * 项目及土地是否合法
     */
    @ApiModelProperty(value = "项目及土地是否合法", position = 118)
    @Size(max = 2, message = "项目及土地是否合法的字段长度必须小于[{max}]")
    private String landLegalityFlag;

    /**
     * 是否放弃优先受偿权
     */
    @ApiModelProperty(value = "是否放弃优先受偿权", position = 119)
    @Size(max = 2, message = "是否放弃优先受偿权的字段长度必须小于[{max}]")
    private String giveUpCompensateFlag;

    /**
     * 付款比例是否低于百分之八十
     */
    @ApiModelProperty(value = "付款比例是否低于百分之八十", position = 120)
    @Size(max = 4, message = "付款比例是否低于百分之八十的字段长度必须小于[{max}]")
    private String payRateLessEightyFlag;

    /**
     * 支付节点时间是否超过2个月
     */
    @ApiModelProperty(value = "支付节点时间是否超过2个月", position = 121)
    @Size(max = 4, message = "支付节点时间是否超过2个月的字段长度必须小于[{max}]")
    private String nodeMoreTwoMonthFlag;

    /**
     * 重大风险化解措施
     */
    @ApiModelProperty(value = "重大风险化解措施", position = 122)
    @Size(max = 2048, message = "重大风险化解措施的字段长度必须小于[{max}]")
    private String bigRiskMeasure;

    /**
     * 交底日期
     */
    @ApiModelProperty(value = "交底日期", position = 123)
    private Long presentationTime;

    /**
     * 交底人
     */
    @ApiModelProperty(value = "交底人", position = 124)
    @Size(max = 255, message = "交底人的字段长度必须小于[{max}]")
    private String presentationUser;

    /**
     * 接收人
     */
    @ApiModelProperty(value = "接收人", position = 125)
    @Size(max = 512, message = "接收人的字段长度必须小于[{max}]")
    private String recipient;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", position = 126)
    @Size(max = 2048, message = "备注的字段长度必须小于[{max}]")
    private String remark;

    /**
     * 项目附件信息对象
     */
    @ApiModelProperty(value = "项目附件信息对象", position = 130)
    @JsonProperty("projectAttachment")
    private List<Map> projectAttachment1;

    /**
     * 突破底线条款
     */
    @ApiModelProperty(value = "突破底线条款", position = 131)
    private String breakBottom;

    /**
     * 所属源文件id
     */
    @ApiModelProperty(value = "所属源文件id", position = 133)
    @NotNull(message = "所属源文件id不能为空")
    private Long belongId;

    /**
     * 项目附件信息对象
     */
    @ApiModelProperty(value = "执行单位id", position = 134)
    @Size(max = 128, message = "执行单位id不超过128个字符")
    private String executeUnitId;

    /**
     * 项目附件信息对象
     */
    @ApiModelProperty(value = "执行单位Code", position = 135)
    @Size(max = 128, message = "执行单位Code不超过128个字符")
    private String executeUnitCode;

    /**
     * 执行单位在标准组织的idPath
     */
    @ApiModelProperty(value = "执行单位在标准组织的idPath", position = 136)
    @Size(max = 1024, message = "执行单位在标准组织的idPath不超过1024个字符")
    private String executeUnitIdPath;

    /**
     * 补充协议编号
     */
    @ApiModelProperty(value = "补充协议编号", position = 137)
    @Size(max = 1024, message = "补充协议编号不超过1024个字符")
    @JsonProperty("agreementCode")
    private String supplementaryAgreementCode;

    /**
     * 客户级别
     */
    @ApiModelProperty(value = "客户级别", position = 138)
    @Size(max = 50, message = "客户级别不超过50个字符")
    private String customerLevel;

    // /**
    //  * 云枢组织名称
    //  */
    // @ApiModelProperty(value = "云枢组织名称", position = 104)
    // private String yunshuExecuteUnit;
    //
    // /**
    //  * 云枢组织ID
    //  */
    // @ApiModelProperty(value = "云枢组织ID", position = 104)
    // private String yunshuExecuteUnitId;
    //
    // /**
    //  * 云枢组织编码
    //  */
    // @ApiModelProperty(value = "云枢组织编码", position = 104)
    // private String yunshuExecuteUnitCode;
    //
    // /**
    //  * 云枢组织idPath
    //  */
    // @ApiModelProperty(value = "云枢组织idPath", position = 104)
    // private String yunshuExecuteUnitIdPath;

    /**
     * 是否创新业务
     */
    @ApiModelProperty(value = "是否创新业务")
    private String ifInnovativeBusiness;

    /**
     * 创新业务分类
     */
    @ApiModelProperty(value = "创新业务分类")
    private String innovativeBusinessType;

    /**
     * 创新业务分类2
     */
    @ApiModelProperty(value = "创新业务分类2")
    private String innovativeBusinessType2;


    /**
     * 创新业务分类3
     */
    @ApiModelProperty(value = "创新业务分类3")
    private String innovativeBusinessType3;

    @ApiModelProperty(value = "是否战新")
    private String ifStrategicNewBusiness;

    @ApiModelProperty(value = "战新业务一级分类")
    private String strategicNewBusinessType;

    @ApiModelProperty(value = "战新业务二级分类")
    private String strategicNewBusinessType2;

    @ApiModelProperty(value = "战新业务三级分类")
    private String strategicNewBusinessType3;

    @ApiModelProperty(value = "补充协议金额")
    private BigDecimal supplementAmount = new BigDecimal(0);

    /**
     * 是否局重点项目Y与N
     */
    @ApiModelProperty(value = "是否局重点项目Y与N")
    private String bureauProject;

    /**
     * 局标准分类1
     */
    @ApiModelProperty("局标准分类1")
    private String standardType1;

    /**
     * 局标准分类2
     */
    @ApiModelProperty("局标准分类2")
    private String standardType2;

    /**
     * 局标准分类3
     */
    @ApiModelProperty("局标准分类3")
    private String standardType3;

    /**
     * 局标准分类4
     */
    @ApiModelProperty("局标准分类4")
    private String standardType4;

    /**
     * 变更后的合同金额
     */
    @ApiModelProperty("变更后的合同金额")
    private BigDecimal afterChangeContractAmount;

    /**
     * 是否授权外Y与N
     */
    @ApiModelProperty("是否授权外Y与N")
    private String mandateForeign;

    //    /**
    //     *计价方式(清单计价、定额计价)
    //     */
    //    @ApiModelProperty("计价方式(清单计价、定额计价)")
    //    private String calculatePriceType;
    //
    //    /**
    //     * 业主Id
    //     */
    //    @ApiModelProperty("业主Id")
    //    private Long customerId;

    /**
     * 预估垫资时间
     */
    @ApiModelProperty("预估垫资时间")
    private String advancesFundMonth;

    /**
     * 预估垫资金额（折算美元）
     */
    @ApiModelProperty("预估垫资金额")
    private BigDecimal estimatedAdvanceAmount;

    // /**
    //  * 源文件类型: agreement:补充协议; agreement_presentation:补充协议定案
    //  */
    // @ApiModelProperty("源文件类型: agreement:补充协议; agreement_presentation:补充协议定案")
    // @NotNull(message = "源文件类型不能为空")
    // private String belongFileType;

    /**
     * 合同经办人
     */
    @ApiModelProperty(value = "合同经办人", position = 12)
    @Size(max = 64, message = "合同经办人的长度必须小于64")
    private String contractResponsiblePerson;

    @ApiModelProperty("预估垫资金额")
    private String advancesFundAmount;

    /**
     * 非现金支付方式
     */
    @ApiModelProperty(value = "非现金支付方式", position = 99)
    private String noCashPayWay;

    /**
     * 合同评审id
     */
    @ApiModelProperty(value = "合同评审id", position = 99)
    private Long evaluationId;
}
