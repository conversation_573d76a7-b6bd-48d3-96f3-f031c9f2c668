package com.cscec3b.iti.projectmanagement.api.dto.request.project;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;

/**
 * 修订财商信息
 *
 * <AUTHOR>
 * @date 2023/07/04 10:07
 **/

@Getter
@Setter
public class ProjectUpdateFinanceReq implements Serializable {

    @NotNull(message = "项目id不能为空")
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    @NotNull(message = "项目编码不能为空")
    @ApiModelProperty(value = "项目编码")
    @Size(min = 1, max = 32, message = "项目编码长度要求为{min}-{max}")
    private String projectFinanceCode;

    @NotNull(message = "项目名称不能为空")
    @ApiModelProperty(value = "项目名称")
    @Size(min = 1, max = 200, message = "项目名称长度要求为{min}-{max}")
    private String projectFinanceName;

    @NotNull(message = "项目简称不能为空")
    @ApiModelProperty(value = "项目简称")
    @Size(min = 1, max = 200, message = "项目简称长度要求为{min}-{max}")
    private String projectFinanceAbbreviation;
}
