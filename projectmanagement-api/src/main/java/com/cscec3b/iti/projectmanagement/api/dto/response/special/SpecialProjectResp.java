package com.cscec3b.iti.projectmanagement.api.dto.response.special;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonProperty;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description SpecialProjectResp
 * <AUTHOR>
 * @Date 2023/2/14 9:46
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "SpecialProjectResp", description = "特殊立项项目列表响应对象")
public class SpecialProjectResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 特殊立项项目id
     */
    @ApiModelProperty(value = "特殊立项项目id")
    private Long id;

    @ApiModelProperty(value = "执行单位名称")
    private String executeUnit;

    @ApiModelProperty(value = "执行单位简称")
    private String executeUnitAbbreviation;

    /**
     * 财商立项编号
     */
    @ApiModelProperty(value = "财商立项编号")
    private String projectFinanceCode;

    /**
     * 财商立项名称
     */
    @ApiModelProperty(value = "财商立项名称")
    private String projectFinanceName;

    /**
     * 财商立项项目简称（中文）
     */
    @ApiModelProperty(value = "财商立项项目简称（中文）")
    private String projectFinanceAbbreviation;

    /**
     * A8编码
     */
    @ApiModelProperty(value = "A8编码")
    private String a8ProjectCode;

    /**
     * 特殊立项项目分类id
     */
    @ApiModelProperty(value = "特殊立项项目分类id")
    private String projectClassId;

    /**
     * 项目分类名称
     */
    @ApiModelProperty(value = "项目分类名称")
    private String projectClassName;

    /**
     * 项目分类idpath
     */
    @ApiModelProperty(value = "项目分类idpath")
    private String projectClassIdPath;

    /**
     * 特殊立项项目状态
     */
    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    /**
     * 项目地址
     */
    @ApiModelProperty(value = "项目地址", position = 15)
    private String projectAddress;

    /**
     * 工程名称
     */
    @ApiModelProperty(value = "工程名称")
    private String projectName;


    @ApiModelProperty(value = "云枢执行单位名称")
    private String yunshuExecuteUnit;

    @ApiModelProperty(value = "云枢执行单位编码")
    private String yunshuExecuteUnitCode;

    @ApiModelProperty(value = "云枢执行单位id")
    private String yunshuExecuteUnitId;

    @ApiModelProperty(value = "云枢执行单位idPath")
    private String yunshuExecuteUnitIdPath;

    @ApiModelProperty(value = "云枢执行单位简称")
    private String yunshuExecuteUnitAbbreviation;

    @ApiModelProperty(value = "项目唯一标识 含义：接收市场营销立项通知或特殊立项发起后生成.P+年月日+四位流水号")
    private String cpmProjectKey;

    @ApiModelProperty(value = "局标准分类")
    private String standardType;

    /**
     * 财商业务板块
     */
    @ApiModelProperty(value = "财商业务板块")
    @JsonProperty("financialBusinessSegment")
    private String marketingFinancialBusinessSegment;

    /**
     * 局标准分类编码路径
     */
    @ApiModelProperty(value = "局标准分类编码路径")
    private String standardTypeCodePath;

    /**
     * 财商业务板块codePath
     */
    @ApiModelProperty(value = "财商业务板块codePath")
    @JsonProperty("businessSegmentCodePath")
    private String marketingBusinessSegmentCodePath;

    /**
     * 项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;
     */
    @ApiModelProperty(value = "项目状态(工程): 00:开工准备; 01:在施; 02:完工; 03:竣工; 04:销项; 0199:停工; 0399:质保;")
    private String projectStatusEng;

    /**
     * 项目状态(财务): 01:在施; 0301:已竣未结; 0302:已竣已结; 0199:停工; 04:销项;
     */
    @ApiModelProperty(value = "项目状态(财务): 01:在施; 0301:已竣未结; 0302:已竣已结; 0199:停工; 04:销项;")
    private String projectStatusFin;

    /**
     * 项目状态(商务): 05:未结; 06:已结
     */
    @ApiModelProperty(value = "项目状态(商务): 05:未结; 06:已结")
    private String projectStatusBiz;
}
