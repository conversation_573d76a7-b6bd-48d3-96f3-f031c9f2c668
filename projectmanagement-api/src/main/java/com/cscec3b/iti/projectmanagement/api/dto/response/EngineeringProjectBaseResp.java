package com.cscec3b.iti.projectmanagement.api.dto.response;


import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("工程项目基础信息")
public class EngineeringProjectBaseResp {

    @ApiModelProperty(value = "计划开工日期")
    private Long workerBeginTime;

    @ApiModelProperty(value = "计划竣工日期")
    private Long workerEndTime;

    @ApiModelProperty(value = "项目承接主体类型")
    private String projectContractorType;

    @ApiModelProperty(value = "是否装配式")
    private String fabricated;

    @ApiModelProperty(value = "项目分类")
    private String projectCategory;

    @ApiModelProperty(value = "业主单位")
    private String customCode;

    @ApiModelProperty(value = "承建模式")
    private String contractMode;

    @ApiModelProperty(value = "是否与总承包同一标段")
    private String sameSectionAsGc;

    @ApiModelProperty(value = "项目重要性类别")
    private String projectImportanceClass;

    @ApiModelProperty(value = "签约主体名称")
    private String signedSubjectValue;

    @ApiModelProperty(value = "签约主体代码")
    private String signedSubjectCode;

    @ApiModelProperty(value = "建设单位")
    private String buildUnit;

    @ApiModelProperty(value = "设计单位")
    private String designUnit;

    @ApiModelProperty(value = "勘察单位")
    private String surveyUnit;

    @ApiModelProperty(value = "监理单位")
    private String supervisionUnit;

}
