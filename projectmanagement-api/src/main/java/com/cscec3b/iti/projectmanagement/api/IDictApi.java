package com.cscec3b.iti.projectmanagement.api;


import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.dict.SysDictDataReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.dict.SysDictTypeReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.dict.SysDictDataResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.dict.SysDictTypeResp;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 字典服务类
 *
 * <AUTHOR>
 * @date 2024/01/04
 */
public interface IDictApi {

    String PATH = "/dict";

    /**
     * 查询字典类型列表
     */
    @PostMapping("/type/list")
    @ApiOperation(value = "查询字典类型列表")
    GenericityResponse<Page<SysDictTypeResp>> typeListPage(
            @ApiParam("字典类型查询参数") @RequestBody SysDictTypeReq dictType);


    /**
     * 查询字典类型详细
     *
     * @param dictId 字典ID
     */
    @GetMapping(value = "/type/{dictId}")
    @ApiOperation(value = "查询字典类型详细")
    GenericityResponse<SysDictTypeResp> getInfo(@ApiParam("字典类型id") @PathVariable Long dictId);

    /**
     * 新增字典类型
     */
    @PostMapping("/type")
    @ApiOperation(value = "新增字典类型")
    GenericityResponse<List<SysDictDataResp>> add(
            @ApiParam("字典类型新增参数") @Validated @RequestBody SysDictTypeReq dict);

    /**
     * 修改字典类型
     */
    @PutMapping("/type")
    @ApiOperation(value = "修改字典类型")
    GenericityResponse<List<SysDictDataResp>> edit(
            @ApiParam("字典类型编辑参数") @Validated @RequestBody SysDictTypeReq dict);

    /**
     * 删除字典类型
     *
     * @param dictIds 字典ID串
     */
    @DeleteMapping("/type/{dictIds}")
    @ApiOperation(value = "删除字典类型")
    GenericityResponse<Boolean> removeTypes(@ApiParam("字典类型ids") @PathVariable Long[] dictIds);

    /**
     * 刷新字典缓存
     */
    @DeleteMapping("/refreshCache")
    @ApiOperation(value = "刷新字典缓存")
    GenericityResponse<Boolean> refreshCache();


    /**
     * 查询字典数据列表
     */
    @PostMapping("/data/list")
    @ApiOperation(value = "查询字典数据列表")
    GenericityResponse<Page<SysDictDataResp>> dataListPage(
            @ApiParam("字典数据参数列表") @RequestBody SysDictDataReq dictData);

    /**
     * 查询字典数据详细
     *
     * @param dictCode 字典code
     */
    @GetMapping(value = "/data/{dictCode}")
    @ApiOperation(value = "查询字典数据详细")
    GenericityResponse<SysDictDataResp> getDataInfo(@ApiParam("字典数据id") @PathVariable Long dictCode);

    /**
     * 根据字典类型查询字典数据信息
     *
     * @param dictType 字典类型
     */
    @GetMapping(value = "/type-data/{dictType}")
    @ApiOperation(value = "根据字典类型查询字典数据信息", notes = "如果dictType是树形结构则返回树形结构数据")
    GenericityResponse<List<SysDictDataResp>> getDataByDictType(
            @ApiParam(value = "字典类型") @PathVariable String dictType);

    /**
     * 新增字典数据
     */
    @PostMapping("/data")
    @ApiOperation(value = "新增字典数据")
    GenericityResponse<List<SysDictDataResp>> add(
            @ApiParam(value = "新增字典数据") @Validated @RequestBody SysDictDataReq dict);

    /**
     * 修改保存字典数据
     */

    @PutMapping("/data")
    @ApiOperation(value = "修改保存字典数据")
    GenericityResponse<List<SysDictDataResp>> edit(
            @ApiParam(value = "字典数据更新参数") @Validated @RequestBody SysDictDataReq dict);

    /**
     * 删除字典数据
     *
     * @param dictCodes 字典code串
     */

    @DeleteMapping("/data/{dictCodes}")
    @ApiOperation(value = "删除字典数据")
    GenericityResponse<Boolean> removeDatas(@ApiParam(value = "dictCodes") @PathVariable Long[] dictCodes);

}
