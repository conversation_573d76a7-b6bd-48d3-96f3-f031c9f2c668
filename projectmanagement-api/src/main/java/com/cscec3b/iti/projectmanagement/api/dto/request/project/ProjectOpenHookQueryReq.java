
package com.cscec3b.iti.projectmanagement.api.dto.request.project;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @description 项目对外请求数据
 * <AUTHOR>
 * @date 2022/11/03
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProjectOpenHookQueryReq", description = "项目挂接查询请求数据")
public class ProjectOpenHookQueryReq extends BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "云枢组织id")
    private String yunshuOrgId;

    @ApiModelProperty(value = "财商项目编码")
    private String projectFinanceCode;

    @ApiModelProperty(value = "财商项目名称")
    private String projectFinanceName;

    @ApiModelProperty(value = "执行单位id")
    private String executeUnitId;

    /**
     * 查询关键字
     */
    @ApiModelProperty(value = "查询关键字：项目标识, 项目编码, 项目名称")
    private String searchKey;

}
