
package com.cscec3b.iti.projectmanagement.api.dto.request;

import java.io.Serializable;

import javax.validation.constraints.Min;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description 分页基础数据
 * <AUTHOR>
 * @date 2022/10/20
 */
@Data
@ApiModel("基础分页数据")
public class BasePage implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("当前页")
    @Min(value = 1, message = "当前页最小为1")
    private Integer current = 1;

    @ApiModelProperty("每页条数")
    @Min(value = 1, message = "每页大小最小为1")
    private Integer size = 10;

    /**
     * 分页查询起始位置
     * @return Integer
     */
    public Integer getStart() {
        return current > 0 ? (current - 1) * size : 0;
    }
}
