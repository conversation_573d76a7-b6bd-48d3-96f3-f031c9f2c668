package com.cscec3b.iti.projectmanagement.api;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ApprovalStepNoteReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ApprovalStepNoteUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep.ApprovalStepPageReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.approvalstep.ApprovalStepNoteResp;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;


/**
 * 项目立项步骤注意事件
 *
 * <AUTHOR>
 * @date 2023/10/23
 */
@Validated
public interface IApprovalStepNoteApi {
    /**
     * 路径
     */
    String PATH = "/approval/step/note";

    /**
     * 添加立项步骤注意事件
     *
     * @param req 立项步骤注意事件
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    @PostMapping
    @ApiOperation(value = "添加立项步骤注意事件", notes = "添加立项步骤注意事件")
    GenericityResponse<Boolean> saveStepNote(
            @ApiParam(value = "立项步骤注意事件", required = true) @RequestBody ApprovalStepNoteReq req);

    /**
     * 修改立项步骤注意事件
     *
     * @param req 立项步骤注意事件
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    @PutMapping
    @ApiOperation(value = "修改立项步骤注意事件", notes = "修改立项步骤注意事件")
    GenericityResponse<Boolean> updateStepNote(
            @ApiParam(value = "立项步骤注意事件", required = true) @RequestBody ApprovalStepNoteUpdateReq req);


    /**
     * 删除立项步骤注意事件
     *
     * @param id 立项步骤注意事件ID
     * @return {@link GenericityResponse}<{@link Boolean}>
     */
    @DeleteMapping("/{id}")
    @ApiOperation(value = "删除立项步骤注意事件", notes = "删除立项步骤注意事件")
    GenericityResponse<Boolean> deleteStepNote(
            @ApiParam(value = "立项步骤注意事件ID", required = true) @PathVariable(value = "id") String id);


    /**
     * 获取立项步骤注意事件列表
     *
     * @param req 立项步骤分页请求参数
     * @return {@link GenericityResponse}<{@link Page}<{@link ApprovalStepNoteResp}>>
     */
    @PostMapping("/page")
    @ApiOperation(value = "获取立项步骤注意事件列表", notes = "获取立项步骤注意事件")
    GenericityResponse<Page<ApprovalStepNoteResp>> getStepNoteList(
            @ApiParam(value = "立项步骤分页请求参数", required = true) @RequestBody ApprovalStepPageReq req);

    /**
     * 获取立项步骤注意事件详情
     *
     * @param id 立项步骤注意事件ID
     * @return {@link GenericityResponse}<{@link ApprovalStepNoteResp}>
     */
    @GetMapping("/{id}")
    @ApiOperation(value = "获取立项步骤注意事件详情", notes = "获取立项步骤注意事件详情")
    GenericityResponse<ApprovalStepNoteResp> getStepNote(
            @ApiParam(value = "立项步骤注意事件ID", required = true) @PathVariable(value = "id") String id);
}
