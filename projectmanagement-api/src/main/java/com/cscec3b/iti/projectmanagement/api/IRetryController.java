package com.cscec3b.iti.projectmanagement.api;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;

/**
 * 异常重试
 *
 * <AUTHOR>
 * @date 2023/04/12 10:20
 **/

public interface IRetryController {

    /**
     * 路径
     */
    String PATH = "/retry";

    /**
     * 获取需要干预的方法列表
     *
     * @param current
     * @param size
     * @param className
     * @param methodName
     * @param param
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @GetMapping("/need-intervene")
    @ApiOperation(value = "获取需要干预的方法列表", notes = "获取需要干预的方法列表")
    GenericityResponse<Page<Object>> needIntervene(
        @ApiParam(value = "当前页码") @RequestParam(value = "current", required = false,
            defaultValue = "1") @Min(1) int current,
        @ApiParam(value = "每页数量") @RequestParam(value = "size", required = false,
            defaultValue = "10") @Min(1) @Max(100) int size,
        @ApiParam(value = "类名称(全路径)") @RequestParam(value = "className", required = false) String className,
        @ApiParam(value = "方法名称") @RequestParam(value = "methodName", required = false) String methodName,
        @ApiParam(value = "方法参数") @RequestParam(value = "param", required = false) String param,
        @ApiParam(value = "方法参数类型") @RequestParam(value = "paramType", required = false) String paramType,
        @ApiParam(value = "开始时间") @RequestParam(value = "beginTime", required = false) Long beginTime,
        @ApiParam(value = "结束时间") @RequestParam(value = "endTime", required = false) Long endTime);

    /**
     * 置为自动执行
     * @param id
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PutMapping("/intervene")
    @ApiOperation(value = "置为自动执行", notes = "将需要人工干预的任务置为自动执行, 需要仔细确认")
    GenericityResponse<Integer> intervene(@ApiParam(value = "id") @RequestParam(value = "id") String id);

}
