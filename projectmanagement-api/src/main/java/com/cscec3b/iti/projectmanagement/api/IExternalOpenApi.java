package com.cscec3b.iti.projectmanagement.api;

import com.cscec.api.data.out.DictionaryInfo;
import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.model.req.OpenProjectSyncReq;
import com.cscec3b.iti.model.req.ProjectEventCallBackReq;
import com.cscec3b.iti.model.req.ProjectFlowEventNoticeReq;
import com.cscec3b.iti.model.req.ProjectOpenReq;
import com.cscec3b.iti.model.req.open.ProjectYzwMappingReq;
import com.cscec3b.iti.model.resp.ProjectArchiveResp;
import com.cscec3b.iti.model.resp.ProjectOpenResp;
import com.cscec3b.iti.model.resp.open.ProjectOpenMappingResp;
import com.cscec3b.iti.projectmanagement.api.bidapproval.dto.response.BidApprovalPageResp;
import com.cscec3b.iti.projectmanagement.api.dto.request.OrgCreateReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.engineeringproject.CompanyViewEngineerProjectReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.open.*;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectOpenByFileReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectOpenEventNoticeReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.project.ProjectOpenHookQueryReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.BureauNamedProjectRelationship;
import com.cscec3b.iti.projectmanagement.api.dto.response.dict.SysDictDataResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.CompanyViewEngineeringProjectResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.engineeringproject.CompanyViewEngineeringProjectTreeResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.event.ProjectEventCallBackResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.open.*;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectOpenByFileResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectOpenHookQueryResp;
import com.cscec3b.iti.projectmanagement.api.dto.response.project.ProjectResp;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;
import java.util.Map;

/**
 * 对外开发API
 * <AUTHOR>
 * @description 对外开放API
 * @date 2022/11/03
 */
public interface IExternalOpenApi {
    /**
     * 路径
     */
    String PATH = "/api";

    /**
     * 项目对外信息获取API
     * @param projectOpenReq
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/external/open/project")
    @ApiOperation(value = "项目对外信息获取API", notes = "项目中心对外分发项目信息")
    GenericityResponse<List<ProjectOpenResp>> externalOpenProject(
            @Validated @ApiParam(value = "项目对外请求数据", required = true) @RequestBody ProjectOpenReq projectOpenReq);

    /**
     * 通过合同信息反查项目信息
     * @param openByFileReq
     * @return {@link GenericityResponse }<{@link List }<{@link ProjectOpenByFileResp }>>
     * <AUTHOR>
     * @date 2023/10/09
     */
    @PostMapping("/external/open/project-by-files")
    @ApiOperation(value = "通过合同信息反查项目信息", notes = "通过合同信息反查项目信息")
    GenericityResponse<List<ProjectOpenByFileResp>>
    externalOpenProjectByFiles(@ApiParam(value = "项目对外请求数据", required = true) @Size(min = 1, max = 100,
            message = "合同信息不能为空，且最多[{max}条]") @Validated @Valid @RequestBody List<ProjectOpenByFileReq> openByFileReq);

    /**
     * 事件回调接口(工地)
     * @param projectId
     * @param eventId
     * @param eventCode event编码
     * @return {@link GenericityResponse }<{@link ProjectEventCallBackResp }>
     * <AUTHOR>
     * @date 2023/10/09
     */
    @GetMapping("/external/open/id")
    @ApiOperation(value = "事件回调接口", notes = "供外部系统根据事件ide及项目id查询项目信息, eventCode必填")
    GenericityResponse<ProjectEventCallBackResp> externalOpenByProjectId(
        @ApiParam(value = "项目id", required = true) @RequestParam("projectId") int projectId,
            @ApiParam(value = "事件id", required = false) @RequestParam(value = "eventId", required = false) Integer eventId,
            @ApiParam(value = "事件id", required = false) @RequestParam(value = "eventCode", required = false) String eventCode);

    /**
     * 项目分发事件回调接口
     * @param callBackReq
     * @return {@link GenericityResponse }<{@link ProjectArchiveResp }>
     * <AUTHOR>
     * @date 2023/10/09
     */
    @PostMapping("/external/flow/event/callback")
    @ApiOperation(value = "事件回调接口", notes = "供外部系统根据事件id及项目id查询项目信息, eventCode必填")
    GenericityResponse<ProjectArchiveResp> externalOpenForEventCallback(
            @ApiParam(value = "回调参数", required = true) @Validated @RequestBody ProjectEventCallBackReq callBackReq);


    /**
     * 接收外部系统事件通知(工地)
     * @param noticeReq
     * @return {@link GenericityResponse }<{@link Boolean }>
     * <AUTHOR>
     * @date 2023/10/09
     */
    @PostMapping("/external/open/event/notice")
    @ApiOperation(value = "接收外部系统事件通知", notes = "接收外部系统的事件通知信息")
    GenericityResponse<Boolean>
        eventNotice(@ApiParam(value = "事件通知信息") @Validated @RequestBody ProjectOpenEventNoticeReq noticeReq);

    /**
     * 市场营销中标未立项文件挂接项目列表
     * @param hookReqReq
     * @return {@link GenericityResponse }<{@link Page }<{@link ProjectOpenHookQueryResp }>>
     * <AUTHOR>
     * @date 2023/10/09
     */
    @PostMapping("/external/open/hook/page")
    @ApiOperation(value = "市场营销中标未立项文件挂接项目列表", notes = "市场营销中标未立项文件挂接项目列表,条件为 财商立项完成，非特殊立项")
    GenericityResponse<Page<ProjectOpenHookQueryResp>>
        hookProjectPageList(@ApiParam(value = "查询条件") @RequestBody ProjectOpenHookQueryReq hookReqReq);

    /**
     * 通过财商编码查询局名义总包与分包项目信息
     *
     * @param type
     * @param financeCode finance编码
     * @return {@link GenericityResponse }<{@link BureauNamedProjectRelationship }>
     * <AUTHOR>
     * @date 2023/10/09
     */
    @GetMapping("/external/open/bureau-nominal-project/{type}/{financeCode}")
    @ApiOperation(value = "通过财商编码查询局名义总包与分包项目信息", notes = " 通过局名义总包项目/分包项目类型，财商编码查询项目总包与分包项目相关信息")
    GenericityResponse<BureauNamedProjectRelationship> getBureauNominalProjectByFinanceCode(
            @ApiParam(value = "局名义项目类型：1:局名义总包类型; 2:局名义分包类型") @PathVariable int type,
            @ApiParam(value = "财商编码") @PathVariable String financeCode);

    /**
     * 分页查询项目列表
     *
     * @param openApiKey   项目中心鉴权appKey
     * @param projectPageReq 分页查询参数
     * @return {@link GenericityResponse}<{@link OpenProjectPageResp}>
     */
    @PostMapping("/external/open/project-page")
    @ApiOperation(value = "项目分页列表", notes = "查询标准项目/特殊立项项目分页列表")
    GenericityResponse<Page<OpenProjectPageResp>>
    getProjectPage(@ApiParam(value = "appkey") @RequestHeader("appKey") String openApiKey,
                @ApiParam(value = "项目分页列表请求参数") @RequestBody OpenProjectPageReq projectPageReq);

    @PostMapping("/external/open/bid-page")
    @ApiOperation(value = "中标未立项文件列表")
    GenericityResponse<Page<BidApprovalPageResp>> getBidPage(
            @ApiParam("appKey") @RequestHeader("appKey") String openApiKey,
            @ApiParam("中标未立项分页参数") @RequestBody @Validated OpenProjectBidPageReq pageReq);

    @PostMapping("/external/open/project/sync")
    @ApiOperation(value = "项目信息同步", notes = "与外部系统进行项目信息同步")
    GenericityResponse<Boolean> syncProjectInfo(
            @ApiParam("业务系统同步信息") @RequestBody @Validated OpenProjectSyncReq syncReq);

    @GetMapping("/external/open/dict/value")
    GenericityResponse<SysDictDataResp> getDictDataByCode(
            @ApiParam("字典信息查询") @RequestParam String dictValue);

    @PostMapping("/external/open/proxy/dictionary/select/code")
    GenericityResponse<List<DictionaryInfo>> proxyInfoByCode(
            @ApiParam("市场字典信息查询") @RequestBody List<String> codes);

    @PostMapping("/external/open/yzw-mapping")
    @ApiOperation(value = "云筑网项目关系映射信息", notes = "通过云枢id和项目标识查询对应的云筑网系统id信息")
    GenericityResponse<List<ProjectOpenMappingResp>> getMappingInfo(
            @ApiParam("项目") @RequestBody @Size(min = 1, max = 100, message = "仅支持1-100条信息,请检查查询数量")
            List<ProjectYzwMappingReq> mappingReqs);

    @PostMapping("/external/open/project/page/by-yunshu-ids")
    @ApiOperation(value = "通过云枢id列表查询项目分页列表", notes = "通过云枢id列表查询项目分页列表")
    GenericityResponse<Page<ProjectOpenResp>> getProjectPageByYunshuIds(
            @ApiParam("appKey") @RequestHeader("appKey") String openApiKey,
            @ApiParam("云枢组织id列表") @RequestBody @Validated OpenProjectPageByYunshuIdsReq req);

    /**
     * 云枢组织-带分页项目列表查询
     *
     * @param queryParams
     * @return
     * <AUTHOR>
     * @date 2023/10/8
     */
    @PostMapping("/external/open/project/page/by-execute-unit")
    @ApiOperation(value = "按执行单位获取分页项目列表", notes = "按执行单位获取分页项目列表")
    GenericityResponse<Page<ProjectResp>> getProjectPageByExecuteUnit(
            @ApiParam("appKey") @RequestHeader("appKey") String appKey,
            @ApiParam("项目列表参数") @Validated @RequestBody ProjectQueryByExecuteReq queryParams);

    @PostMapping("/external/open/org/create")
    @ApiOperation(value = "创建组织")
    GenericityResponse<Map<String, Object>> createProject(
            @ApiParam("组织创建参数") @Validated @RequestBody OrgCreateReq orgCreateReq);

    @PostMapping("/external/open/file/detail")
    @ApiOperation(value = "获取文件详情列表")
    GenericityResponse<?> getFileDetailList(
            @RequestHeader String appKey,
            @ApiParam("投标总结参数") @Validated @RequestBody ContractFileDetailReq param);

    @PostMapping("/external/open/project-archive/page")
    @ApiOperation(value = "分页查询已归档项目信息列表")
    GenericityResponse<Page<OpenProjectArchiveResp>> getProjectArchivedPage(
            @ApiParam("appKey") @RequestHeader("appKey") String openApiKey,
            @ApiParam("项目分页列表参数") @Validated @RequestBody OpenProjectArchiveReq pageReq);

    @PostMapping("/external/open/engineering-archive/page")
    @ApiOperation(value = "分页查询已归档工程信息列表")
    GenericityResponse<Page<OpenEngineeringArchiveResp>> getEngineeringArchivedPage(
            @ApiParam("appKey") @RequestHeader("appKey") String openApiKey,
            @ApiParam("工程分页列表参数") @Validated @RequestBody OpenEngineeringArchiveReq pageReq);

    /**
     * 通过施工项目标识批量查询关联的工程项目
     */
    @PostMapping("/external/open/engineering-project/mapping")
    @ApiOperation(value = "通过施工项目标识/id查询关联的工程项目", notes = "通过施工项目标识查询关联的工程项目")
    GenericityResponse<Page<OpenEngineeringProjectMappingResp>> getEnginAndStandardProjectMappingInfo(
            @ApiParam("appKey") @RequestHeader("appKey") String openApiKey,
            @ApiParam("项目标识分页列表参数") @RequestBody @Validated OpenEngineeringMappingArchiveReq req);

    @PostMapping("/external/open/hr/notice")
    @ApiOperation(value = "人资消息通知接口", notes = "通过标准立项事件触发当前api，给人资发送通知消息")
    GenericityResponse<Boolean> sendNoticeToHr(
        @ApiParam("消息通知参数") @Validated @RequestBody ProjectFlowEventNoticeReq projectEventNoticeReq) throws Exception;

    @PostMapping("/external/open/enginee-project/page")
    @ApiOperation(value = "分页查询工程项目列表", notes = "分页查询工程项目列表")
    GenericityResponse<Page<OpenEngineeringProjectPageResp>> getEngineeProjectPage(
        @ApiParam("appKey") @RequestHeader("appKey") String openApiKey,
        @ApiParam("工程项目分页列表参数") @Validated @RequestBody OpenEngineerProjectPageReq pageReq);

    @GetMapping("/external/open/enginee-project/tree-by-key")
    @ApiOperation(value = "根据工程项目标识查询工程项目树结构", notes = "根据工程项目标识查询工程项目树结构,返回当前工程项目全量tree")
    GenericityResponse<OpenEngineerProjectTreeResp> getEngineeProjectTreeByKey(
        @ApiParam("appKey") @RequestHeader("appKey") String openApiKey,
        @ApiParam("工程项目标识") @RequestParam("engineeringProjectKey") String key);

    @GetMapping("/external/open/finance/{instanceId}")
    @ApiOperation(value = "根据组织id查询项目财商信息", notes = "根据组织id查询项目财商信息")
    GenericityResponse<OpenProjectFinanceResp> getFinanceInfoByOrgIds(@RequestHeader String appKey,
        @ApiParam("云枢组织id") @PathVariable String instanceId);

    /**
     * 以公司视角查询工程信息
     */
    @PostMapping("/external/open/engineering-project/page/company-view")
    @ApiOperation(value = "以公司视角查询工程信息", notes = "以公司视角查询工程信息，支持权限验证")
    GenericityResponse<Page<CompanyViewEngineeringProjectResp>> getCompanyViewEngineeringProjectPage(
            @ApiParam("appKey") @RequestHeader("appKey") String openApiKey,
            @ApiParam("查询参数") @Validated @RequestBody CompanyViewEngineerProjectReq req);

    /**
     * 获取单个工程的树形结构
     */
    @GetMapping("/external/open/engineering-project/company-view/tree/{key}")
    @ApiOperation(value = "获取单个工程的树形结构", notes = "获取单个工程的树形结构，支持权限验证")
    GenericityResponse<List<CompanyViewEngineeringProjectTreeResp>> getCompanyViewEngineeringProjectTreeOpen(
            @ApiParam("appKey") @RequestHeader("appKey") String openApiKey,
            @ApiParam("工程项目id") @PathVariable(value = "key") String engineeringKey);

}
