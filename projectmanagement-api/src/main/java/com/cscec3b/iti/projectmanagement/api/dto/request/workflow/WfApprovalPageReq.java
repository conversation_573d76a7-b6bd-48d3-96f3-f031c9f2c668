package com.cscec3b.iti.projectmanagement.api.dto.request.workflow;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * 流程待办查询参数
 *
 * <AUTHOR>
 * @date 2023/11/30
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "流程待办查询参数")
public class WfApprovalPageReq extends BasePage implements Serializable {

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称", notes = "财商名称或工程名称")
    private String name;

    /**
     * 项目标识
     */
    @ApiModelProperty(value = "项目标识")
    private String cpmProjectKey;

    /**
     * 项目状态
     */
    @ApiModelProperty("项目状态")
    private String[] status;

    /**
     * 业务板块类型
     */
    @ApiModelProperty("业务板块类型")
    private String[] businessType;

    /**
     * 执行单位idPath
     */
    @ApiModelProperty(value = "执行单位idPath", notes = "执行单位idPath")
    private String yunshuExecuteUnitIdPath;

    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称", notes = "字段名称")
    private String fieldName;

    /**
     * 字段id
     */
    @ApiModelProperty(value = "字段id", notes = "字段id")
    private String fieldId;

    /**
     * 业务板块
     */
    @ApiModelProperty(value = "业务板块", notes = "业务板块")
    private String scopeType;

}
