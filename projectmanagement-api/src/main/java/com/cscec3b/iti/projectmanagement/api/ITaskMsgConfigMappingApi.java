package com.cscec3b.iti.projectmanagement.api;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.common.base.page.Page;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.TaskMsgConfigMappingReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig.TaskMsgConfigMappingUpdateReq;
import com.cscec3b.iti.projectmanagement.api.dto.response.pendingtaskmsgconfig.PendingTaskMsgConfigResp;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

public interface ITaskMsgConfigMappingApi {

    /**
     * 路径
     */
    String PATH = "/task-msg-mapping";

    @PostMapping
    @ApiOperation("待办任务配置与人员关联添加")
    GenericityResponse<Boolean> create(
        @ApiParam("新增参数") @Validated(TaskMsgConfigMappingReq.Create.class) @RequestBody TaskMsgConfigMappingReq saveReq);

    @PutMapping
    @ApiOperation("待办任务配置与人员关联更新")
    GenericityResponse<Boolean> update(@ApiParam("更新参数") @RequestBody TaskMsgConfigMappingUpdateReq updateReq);

    @PostMapping("/page")
    @ApiOperation("待办任务配置与人员关联分页查询")
    GenericityResponse<Page<PendingTaskMsgConfigResp>>
        page(
            @ApiParam("分页查询参数") @Validated(TaskMsgConfigMappingReq.Page.class) @RequestBody TaskMsgConfigMappingReq pageReq);

    @DeleteMapping("{id}")
    @ApiOperation("待办任务配置与人员关联删除")
    GenericityResponse<Boolean> delete(@ApiParam("id") @PathVariable Long id);
}
