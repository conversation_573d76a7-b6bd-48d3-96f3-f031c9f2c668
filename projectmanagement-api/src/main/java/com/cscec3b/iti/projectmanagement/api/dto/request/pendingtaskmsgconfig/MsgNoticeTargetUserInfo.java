package com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("待办消息通知目标用户")
public class MsgNoticeTargetUserInfo implements Serializable {

    /**
     * 用户code
     */
    @ApiModelProperty(value = "用户code")
    private String id;
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String name;

}
