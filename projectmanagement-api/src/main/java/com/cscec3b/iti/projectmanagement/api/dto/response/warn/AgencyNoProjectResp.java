package com.cscec3b.iti.projectmanagement.api.dto.response.warn;

import java.io.Serializable;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "AgencyNoProjectResp", description = "签约未立项响应对象")
public class AgencyNoProjectResp implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "预警主键ID")
    private long projectId;

    @ApiModelProperty(value = "独立合同ID")
    private Long independentContractId;

    @ApiModelProperty(value = "独立合同类型：1投标总结；2补充协议；3局内分包合同")
    private Integer independentContractType;

    @ApiModelProperty(value = "独立合同文件 独立合同ID,独立合同类型")
    private String independentContract;

    @ApiModelProperty(value = "合同编号")
    private String contractCode;

    @ApiModelProperty(value = "合同名称")
    private String contractName;

    @ApiModelProperty(value = "执行单位")
    private String executeUnit;

    @ApiModelProperty(value = "项目状态")
    private String projectStatus;

    @ApiModelProperty(value = "项目部状态")
    private String projectDepartmentStatus;

    @ApiModelProperty(value = "接收时间")
    private Long receiveTime;

    @ApiModelProperty(value = "执行单位简称")
    private String executeUnitAbbreviation;

    @ApiModelProperty(value = "云枢执行单位名称")
    private String yunshuExecuteUnit;

    @ApiModelProperty(value = "云枢执行单位编码")
    private String yunshuExecuteUnitCode;

    @ApiModelProperty(value = "云枢执行单位id")
    private String yunshuExecuteUnitId;

    @ApiModelProperty(value = "云枢执行单位idPath")
    private String yunshuExecuteUnitIdPath;

    /**
     * 云枢执行单位简称
     */
    @ApiModelProperty(value = "云枢执行单位简称")
    private String yunshuExecuteUnitAbbreviation;

}
