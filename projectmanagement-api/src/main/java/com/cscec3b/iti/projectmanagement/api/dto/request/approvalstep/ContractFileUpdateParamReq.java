package com.cscec3b.iti.projectmanagement.api.dto.request.approvalstep;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel("通用文件合同更新参数")
public class ContractFileUpdateParamReq implements Serializable {

    /**
     * 文件id
     */
    @ApiModelProperty("文件id")
    private Long fileId;

    /**
     * 业务板块codePath
     */
    @ApiModelProperty("业务板块codePath")
    private String businessSegmentCopePath;
}
