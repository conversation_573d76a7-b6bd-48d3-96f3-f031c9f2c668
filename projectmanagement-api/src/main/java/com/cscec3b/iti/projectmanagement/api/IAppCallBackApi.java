package com.cscec3b.iti.projectmanagement.api;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import com.cscec3b.iti.common.base.api.GenericityResponse;
import com.cscec3b.iti.logger.annotations.Logger;
import com.cscec3b.iti.projectmanagement.api.dto.dto.enignproject.MdmApi;
import com.cscec3b.iti.projectmanagement.api.dto.request.uc.G3OrgEventCallbackReq;
import com.cscec3b.iti.projectmanagement.api.dto.request.workflow.G3WfCallBackReq;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 流程引擎对外开放接口
 *
 * <AUTHOR>
 * @date 2024/01/08
 */
public interface IAppCallBackApi {

    String PATH = "/common-data";

    /**
     * 流程引擎回调接口
     *
     * @param callBackDto 回调参数
     * @return {@link Boolean}
     */
    @Logger
    @PostMapping("/wf/g3/callback")
    @ApiOperation(value = "g3workFlow回调")
    GenericityResponse<Boolean> g3WorkFlowCallback(@ApiParam("回调参数") @RequestBody G3WfCallBackReq callBackDto);


    @Logger
    @RequestMapping(value = "/uc/org/callback", method = {RequestMethod.POST, RequestMethod.PUT})
    @ApiOperation(value = "g3组织树事件回调-新增、修改")
    void g3OrgTreeCallback(HttpServletResponse response, HttpServletRequest request,
            @ApiParam("回调参数") @Validated @RequestBody G3OrgEventCallbackReq callBackDto);

    @Logger
    @DeleteMapping("/uc/org/callback")
    @ApiOperation(value = "g3组织树事件回调-删除")
    void g3OrgTreeDeleteCallback(HttpServletResponse response, HttpServletRequest request,
            @ApiParam("职能树ID，对应云枢treeID") @RequestParam String id,
        @ApiParam("是否组织单元删除，默认false") @RequestParam(defaultValue = "false") Boolean mainData);

    @Logger
    @PostMapping(value = {"/mdm/org/callback", "/mdm/callback", "/mdm/engine/project/callback"})
    @ApiOperation(value = "mdm集团主数据数据回调")
    <T> void mdmEngineProjectCallback(HttpServletResponse response, HttpServletRequest request,
        @ApiParam("回调参数") @Validated @RequestBody MdmApi<T> callBackDto);

}
