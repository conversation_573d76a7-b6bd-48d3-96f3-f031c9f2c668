package com.cscec3b.iti.projectmanagement.api.dto.response.project;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 开标记录入参对象
 *
 * <AUTHOR>
 * @Description
 * @Date 2022/11/4 16:37
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "BidOpenRecordResp", description = "开标记录响应对象")
public class BidOpenRecordResp implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 投标单位
     */
    @ApiModelProperty(value = "投标单位", position = 1)
    private String bidder;
    /**
     * 投标报价
     */
    @ApiModelProperty(value = "投标报价", position = 2)
    private BigDecimal tenderOffer;
    /**
     * 工期
     */
    @ApiModelProperty(value = "工期", position = 3)
    private String duration;
    /**
     * 资信标得分
     */
    @ApiModelProperty(value = "资信标得分", position = 4)
    private BigDecimal creditMark;
    /**
     * 技术标得分
     */
    @ApiModelProperty(value = "技术标得分", position = 5)
    private BigDecimal technicalProposalScore;
    /**
     * 商务标得分
     */
    @ApiModelProperty(value = "商务标得分", position = 6)
    private BigDecimal scoreOfCommercialProposal;
    /**
     * 总得分
     */
    @ApiModelProperty(value = "总得分", position = 7)
    private BigDecimal totalScore;
    /**
     * 排名
     */
    @ApiModelProperty(value = "排名", position = 8)
    private Integer ranking;
    /**
     * 报价得分
     */
    @ApiModelProperty(value = "报价得分", position = 10)
    private BigDecimal quotationScore;
    /**
     * 质量
     */
    @ApiModelProperty(value = "质量", position = 11)
    private String quality;
    /**
     * 企业编号
     */
    @ApiModelProperty(value = "企业编号", position = 12)
    private String companyNo;
    /**
     * 竞争对手
     */
    @ApiModelProperty(value = "竞争对手: Y: 我司； N：竞争对手", position = 13)
    private String dataFrom;
    /**
     * 投标费率
     */
    @ApiModelProperty(value = "投标费率", position = 14)
    private String bidRate;
    /**
     * 关联投标总结id
     */
    @ApiModelProperty(value = "关联投标总结id", position = 15)
    private String relationId;
    /**
     * 主键id
     */
    @ApiModelProperty(value = "主键id", position = 16)
    private long id;

}
