package com.cscec3b.iti.projectmanagement.api.dto.request.pendingtaskmsgconfig;

import java.io.Serializable;

import com.cscec3b.iti.projectmanagement.api.dto.request.BasePage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
@ApiModel("待办消息配置查询参数")
public class PendingTaskMsgReq extends BasePage implements Serializable {

    /**
     * UC MSG 配置代码
     */
    @ApiModelProperty("UC MSG 配置代码")
    private String ucMsgConfigCode;

    /**
     * 类型代码
     */
    @ApiModelProperty("业务场景类型代码")
    private String typeCode;

    /**
     * 树 ID
     */
    @ApiModelProperty("组织树节点ID")
    private String treeId;

    /**
     * 云枢执行单元ID
     */
    @ApiModelProperty("云枢执行单元IDPath全路径")
    private String yunshuExecuteUnitIdPath;

}
