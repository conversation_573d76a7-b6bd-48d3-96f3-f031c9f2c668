package com.cscec3b.iti.projectmanagement.api.dto.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 待办列表
 *
 * <AUTHOR>
 * @date 2023/11/30
 */
@Data
@ApiModel(value = "审批流待办中心列表")
public class WfApprovalTodoPageResp implements Serializable {

    private static final long serialVersionUID = 1L;


    @ApiModelProperty(value = "流程实例id")
    private String procInstId;

    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private Long projectId;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String cpmProjectName;

    /**
     * 项目标识
     */
    @ApiModelProperty(value = "项目标识")
    private String cpmProjectKey;

    /**
     * 执行单位组织id
     */
    private String yunshuExecuteUnitId;

    /**
     * 执行单位
     */
    @ApiModelProperty(value = "执行单位")
    private String yunshuExecuteUnit;

    /**
     * 执行单位简称
     */
    @ApiModelProperty(value = "执行单位简称")
    private String yunshuExecuteUnitAbbreviation;


    /**
     * 业务板块
     */
    @ApiModelProperty(value = "业务板块")
    private String scopeType;

    /**
     * 业务板块名称
     */
    @ApiModelProperty(value = "业务板块名称")
    private String scopeTypeName;

    /**
     * 字段id
     */
    @ApiModelProperty(value = "字段id")
    private String fieldId;


    /**
     * 字段名称
     */
    @ApiModelProperty(value = "字段名称")
    private String fieldName;


    /**
     * 字段原始值
     */
    @ApiModelProperty(value = "字段原始值")
    private String rawFieldContent;

    /**
     * 原始字段值枚举
     */
    @ApiModelProperty(value = "原始字段值枚举")
    private String rawFieldEnumName;

    /**
     * 字段目录值
     */
    @ApiModelProperty(value = "字段目标值")
    private String targetFieldContent;

    /**
     * 目标字段值枚举
     */
    @ApiModelProperty(value = "目标字段值枚举")
    private String targetFieldEnumName;


    /**
     * 流程发起时间
     */
    @ApiModelProperty(value = "流程发起时间")
    private Long procStartTime;

    /**
     * 流程结束时间
     */
    @ApiModelProperty(value = "流程结束时间")
    private Long procEndTime;

    /**
     * 审批人名称
     */
    @ApiModelProperty(value = "审批人名称")
    private String approvalerName;

    /**
     * 审批时间
     */
    @ApiModelProperty(value = "审批时间")
    private Long approvalTime;


    /**
     * 流程状态
     */
    @ApiModelProperty(value = "流程状态")
    private String procStatus;


    /**
     * 配置id
     */
    @ApiModelProperty(value = "配置id")
    private String approvalConfigId;


    /**
     * 项目来源
     */
    @ApiModelProperty(value = "项目来源")
    private Integer sourceSystem;

}
