package com.cscec3b.iti.projectmanagement.api.bidapproval.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 开标记录入参对象
 *
 * <AUTHOR>
 * @Description
 * @Date 2022/11/4 10:52
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "BidOpenRecordReq", description = "开标记录")
public class BidOpenRecordFileReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 投标单位
     */
    @ApiModelProperty(value = "投标单位", position = 1)
    @Size(max = 128, message = "开标记录-投标单位不超过128个字符")
    private String bidder;
    /**
     * 投标报价
     */
    @ApiModelProperty(value = "投标报价", position = 2)
    private BigDecimal tenderOffer;
    /**
     * 工期
     */
    @ApiModelProperty(value = "工期", position = 3)
    @Size(max = 20, message = "开标记录-工期不超过20个字符")
    private String duration;
    /**
     * 资信标得分
     */
    @ApiModelProperty(value = "资信标得分", position = 4)
    private BigDecimal creditMark;
    /**
     * 技术标得分
     */
    @ApiModelProperty(value = "技术标得分", position = 5)
    private BigDecimal technicalProposalScore;
    /**
     * 商务标得分
     */
    @ApiModelProperty(value = "商务标得分", position = 6)
    private BigDecimal scoreOfCommercialProposal;
    /**
     * 总得分
     */
    @ApiModelProperty(value = "总得分", position = 7)
    private BigDecimal totalScore;
    /**
     * 排名
     */
    @ApiModelProperty(value = "排名", position = 8)
    private Integer ranking;
    /**
     * 报价得分
     */
    @ApiModelProperty(value = "报价得分", position = 10)
    private BigDecimal quotationScore;
    /**
     * 质量
     */
    @ApiModelProperty(value = "质量", position = 11)
    @Size(max = 128, message = "开标记录-质量不超过128个字符")
    private String quality;
    /**
     * 企业编号
     */
    @ApiModelProperty(value = "企业编号", position = 12)
    @Size(max = 32, message = "开标记录-企业编号不超过32个字符")
    private String companyNo;
    /**
     * 竞争对手
     */
    @ApiModelProperty(value = "竞争对手", position = 13)
    @Size(max = 2, message = "开标记录-竞争对手不超过2个字符")
    private String dataFrom;
    /**
     * 投标费率
     */
    @ApiModelProperty(value = "投标费率", position = 14)
    @Size(max = 32, message = "开标记录-投标费率不超过32个字符")
    private String bidRate;

}
