package com.cscec3b.iti.projectmanagement.api.dto.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "EventReceiveRecordReq")
public class EventReceiveRecordReq extends BasePage implements Serializable {

    /**
     * 项目中心项目id
     */
    @ApiModelProperty(value = "项目中心项目id")
    private Long projectId;

    /**
     * 项目标识
     */
    @ApiModelProperty(value = "项目标识")
    private String cpmProjectKey;

    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String cpmProjectName;

    /**
     * 项目简称
     */
    @ApiModelProperty(value = "项目简称")
    private String cpmProjectAbbreviation;

    /**
     * 事件列表
     */
    @ApiModelProperty(value = "事件列表")
    private List<String> eventCodeList;

    /**
     * 请求时间范围起始
     */
    @ApiModelProperty(value = "请求时间范围起始")
    private Long receiveTimeStart;

    /**
     * 请求时间范围结束
     */
    @ApiModelProperty(value = "请求时间范围结束")
    private Long receiveTimeEnd;

    /**
     * api 返回结果 0：失败，1：成功
     */
    @ApiModelProperty(value = "api 返回结果 0：失败，1：成功")
    private Integer apiResult;

    /**
     * 执行单位idPath
     */
    @ApiModelProperty(value = "执行单位idPath")
    private String executeUnitIdPath;

    /**
     * 执行单位treeId
     */
    @ApiModelProperty(value = "执行单位treeId")
    private String treeId;

    /**
     * 通知内容
     */
    @ApiModelProperty(value = "通知内容")
    private String receiveParam;
}
