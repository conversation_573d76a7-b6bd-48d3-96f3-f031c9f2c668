//package com.cscec3b.iti.projectmanagement.api;
//
//import org.springframework.web.bind.annotation.PathVariable;
//import org.springframework.web.bind.annotation.PutMapping;
//
//import com.cscec3b.iti.common.base.api.GenericityResponse;
//import com.cscec3b.iti.projectmanagement.api.dto.response.PmUserInfoResp;
//
//import io.swagger.annotations.ApiOperation;
//import io.swagger.annotations.ApiParam;
//
///**
// * 用户组织操作相关
// *
// * <AUTHOR>
// * @date 2022/4/1 13:03
// **/
//
//public interface IUserOrgController {
//    /**
//     * 路径
//     */
//    String PATH = "/user";
//
//    /**
//     * 切换组织
//     * @param orgId
//     * @return
//     * <AUTHOR>
//     * @date 2023/10/8
//     */
//    @PutMapping("/change-org/{orgId}")
//    @ApiOperation(value = "切换组织")
//    GenericityResponse<PmUserInfoResp> changeOrg(@ApiParam(value = "精益建造组织id") @PathVariable("orgId") String orgId);
//
//}
