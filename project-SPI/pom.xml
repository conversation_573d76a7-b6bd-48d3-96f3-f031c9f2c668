<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.cscec3b.iti.projectmanagement</groupId>
    <artifactId>project-SPI</artifactId>
    <name>project-SPI</name>
    <version>1.0.9.snapshot</version>
    <packaging>jar</packaging>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <lombok.version>1.18.34</lombok.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <version>2.3.2.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.cscec3b.iti</groupId>
            <artifactId>common-SPISpring</artifactId>
            <version>1.1.0-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>com.cscec3b.iti</groupId>
            <artifactId>common-web</artifactId>
            <version>1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.cscec3b-iti.projectmanagement</groupId>
            <artifactId>projectmanagement-model</artifactId>
            <version>1.0.2.snapshot</version>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>cscec-nexus</id>
            <url>https://maven.cscec3b-iti.com/repository/maven-public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>cscec-nexus</id>
            <url>https://maven.cscec3b-iti.com/repository/maven-public/</url>
        </pluginRepository>
    </pluginRepositories>
    <distributionManagement>
        <repository>
            <id>cscec-releases</id>
            <url>https://maven.cscec3b-iti.com/repository/maven-releases/</url>
            <uniqueVersion>true</uniqueVersion>
        </repository>
        <snapshotRepository>
            <id>cscec-snapshots</id>
            <url>https://maven.cscec3b-iti.com/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
